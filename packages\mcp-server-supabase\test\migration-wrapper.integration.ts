import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { promises as fs } from 'fs';
import path from 'path';
import { MigrationWrapper } from '../src/migration/migration-wrapper.js';
import { MigrationStatus, MigrationDirection, MigrationTemplateType } from '../src/migration/migration-types.js';
import { DatabaseError, ConfigurationError } from '../src/utils/errors.js';
import type { LocalConfig } from '../src/config/index.js';

// Mock node-pg-migrate
vi.mock('node-pg-migrate', () => ({
  default: vi.fn()
}));

// Mock fs/promises
vi.mock('fs/promises');

// Mock logger
vi.mock('../src/utils/logger.js', () => ({
  contextLogger: {
    child: () => ({
      debug: vi.fn(),
      info: vi.fn(),
      error: vi.fn(),
      warn: vi.fn()
    })
  }
}));

describe('MigrationWrapper', () => {
  let mockLocalConfig: LocalConfig;
  let migrationWrapper: MigrationWrapper;
  let mockMigrate: any;

  beforeEach(async () => {
    // Reset all mocks
    vi.clearAllMocks();

    // Mock local config
    mockLocalConfig = {
      DATABASE_URL: 'postgresql://test:test@localhost:5432/testdb',
      SUPABASE_URL: 'http://localhost:54321',
      SUPABASE_ANON_KEY: 'test-anon-key',
      SUPABASE_SERVICE_ROLE_KEY: 'test-service-key',
      READ_ONLY: false,
      DEBUG_SQL: false
    };

    // Mock migrate function
    mockMigrate = vi.fn();
    const { default: migrate } = await import('node-pg-migrate');
    vi.mocked(migrate).mockImplementation(mockMigrate);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('constructor', () => {
    it('should initialize successfully with valid config', () => {
      expect(() => {
        new MigrationWrapper(mockLocalConfig);
      }).not.toThrow();
    });

    it('should throw ConfigurationError when DATABASE_URL is missing', () => {
      const invalidConfig = { ...mockLocalConfig };
      delete invalidConfig.DATABASE_URL;

      expect(() => {
        new MigrationWrapper(invalidConfig);
      }).toThrow(ConfigurationError);
    });

    it('should throw error when database URL is invalid', () => {
      const invalidConfig = {
        ...mockLocalConfig,
        DATABASE_URL: 'invalid-url'
      };

      expect(() => {
        new MigrationWrapper(invalidConfig);
      }).toThrow();
    });
  });

  describe('createMigration', () => {
    beforeEach(() => {
      migrationWrapper = new MigrationWrapper(mockLocalConfig);
    });

    it('should create a migration with custom template', async () => {
      const mockAccess = vi.mocked(fs.access);
      const mockMkdir = vi.mocked(fs.mkdir);
      const mockWriteFile = vi.mocked(fs.writeFile);

      // Mock directory doesn't exist, then file doesn't exist
      mockAccess
        .mockRejectedValueOnce({ code: 'ENOENT' }) // Directory check
        .mockRejectedValueOnce({ code: 'ENOENT' }); // File existence check

      mockMkdir.mockResolvedValueOnce(undefined);
      mockWriteFile.mockResolvedValueOnce();

      const options = {
        name: 'create_users_table',
        template: MigrationTemplateType.CUSTOM,
        templateData: { description: 'Create users table' }
      };

      const result = await migrationWrapper.createMigration(options);

      expect(result).toHaveProperty('filename');
      expect(result).toHaveProperty('path');
      expect(result).toHaveProperty('content');
      expect(result.filename).toMatch(/^\d{14}_create_users_table\.sql$/);
      expect(result.content).toContain('Create users table');

      expect(mockMkdir).toHaveBeenCalledWith(
        expect.stringContaining('supabase/migrations'),
        { recursive: true }
      );
      expect(mockWriteFile).toHaveBeenCalled();
    });

    it('should create a migration with CREATE_TABLE template', async () => {
      const mockAccess = vi.mocked(fs.access);
      const mockMkdir = vi.mocked(fs.mkdir);
      const mockWriteFile = vi.mocked(fs.writeFile);

      mockAccess
        .mockRejectedValueOnce({ code: 'ENOENT' })
        .mockRejectedValueOnce({ code: 'ENOENT' });

      mockMkdir.mockResolvedValueOnce(undefined);
      mockWriteFile.mockResolvedValueOnce();

      const options = {
        name: 'create_products_table',
        template: MigrationTemplateType.CREATE_TABLE,
        templateData: { tableName: 'products' }
      };

      const result = await migrationWrapper.createMigration(options);

      expect(result.content).toContain('CREATE TABLE IF NOT EXISTS products');
      expect(result.content).toContain('DROP TABLE IF EXISTS products');
      expect(result.filename).toMatch(/^\d{14}_create_products_table\.sql$/);
    });

    it('should create a migration with ALTER_TABLE template', async () => {
      const mockAccess = vi.mocked(fs.access);
      const mockMkdir = vi.mocked(fs.mkdir);
      const mockWriteFile = vi.mocked(fs.writeFile);

      mockAccess
        .mockRejectedValueOnce({ code: 'ENOENT' })
        .mockRejectedValueOnce({ code: 'ENOENT' });

      mockMkdir.mockResolvedValueOnce(undefined);
      mockWriteFile.mockResolvedValueOnce();

      const options = {
        name: 'add_email_to_users',
        template: MigrationTemplateType.ALTER_TABLE,
        templateData: { 
          tableName: 'users',
          columnName: 'email',
          columnType: 'VARCHAR(255)'
        }
      };

      const result = await migrationWrapper.createMigration(options);

      expect(result.content).toContain('ALTER TABLE users');
      expect(result.content).toContain('ADD COLUMN IF NOT EXISTS email VARCHAR(255)');
      expect(result.content).toContain('DROP COLUMN IF EXISTS email');
    });

    it('should create a migration with CREATE_INDEX template', async () => {
      const mockAccess = vi.mocked(fs.access);
      const mockMkdir = vi.mocked(fs.mkdir);
      const mockWriteFile = vi.mocked(fs.writeFile);

      mockAccess
        .mockRejectedValueOnce({ code: 'ENOENT' })
        .mockRejectedValueOnce({ code: 'ENOENT' });

      mockMkdir.mockResolvedValueOnce(undefined);
      mockWriteFile.mockResolvedValueOnce();

      const options = {
        name: 'add_email_index',
        template: MigrationTemplateType.CREATE_INDEX,
        templateData: { 
          tableName: 'users',
          indexName: 'idx_users_email',
          columnName: 'email'
        }
      };

      const result = await migrationWrapper.createMigration(options);

      expect(result.content).toContain('CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email');
      expect(result.content).toContain('ON users (email)');
      expect(result.content).toContain('DROP INDEX IF EXISTS idx_users_email');
    });

    it('should throw error when migration file already exists', async () => {
      const mockAccess = vi.mocked(fs.access);
      const mockMkdir = vi.mocked(fs.mkdir);

      mockAccess
        .mockRejectedValueOnce({ code: 'ENOENT' }) // Directory check
        .mockResolvedValueOnce(); // File exists

      mockMkdir.mockResolvedValueOnce(undefined);

      const options = {
        name: 'existing_migration',
        template: MigrationTemplateType.CUSTOM
      };

      await expect(migrationWrapper.createMigration(options)).rejects.toThrow(
        DatabaseError
      );
    });

    it('should handle directory creation errors', async () => {
      const mockAccess = vi.mocked(fs.access);
      const mockMkdir = vi.mocked(fs.mkdir);

      mockAccess.mockRejectedValue({ code: 'ENOENT' });
      mockMkdir.mockRejectedValue(new Error('Permission denied'));

      const options = {
        name: 'test_migration',
        template: MigrationTemplateType.CUSTOM
      };

      await expect(migrationWrapper.createMigration(options)).rejects.toThrow(
        DatabaseError
      );
    });
  });

  describe('up', () => {
    beforeEach(() => {
      migrationWrapper = new MigrationWrapper(mockLocalConfig);
    });

    it('should run migrations successfully', async () => {
      const mockResult = [
        { name: '20241201000000_create_users', timestamp: Date.now() },
        { name: '20241201000001_create_posts', timestamp: Date.now() }
      ];

      mockMigrate.mockResolvedValue(mockResult);

      const result = await migrationWrapper.up();

      expect(result.success).toBe(true);
      expect(result.migrationsRun).toHaveLength(2);
      expect(result.migrationsRun[0]).toMatchObject({
        id: '20241201000000_create_users',
        name: '20241201000000_create_users',
        status: MigrationStatus.APPLIED,
        direction: MigrationDirection.UP
      });
      expect(result.durationMs).toBeGreaterThan(0);

      expect(mockMigrate).toHaveBeenCalledWith(
        expect.objectContaining({
          direction: 'up',
          noLock: true
        })
      );
    });

    it('should run migrations with options', async () => {
      mockMigrate.mockResolvedValue([]);

      const options = {
        count: 2,
        dryRun: true,
        lockTable: true
      };

      const result = await migrationWrapper.up(options);

      expect(result.success).toBe(true);
      expect(mockMigrate).toHaveBeenCalledWith(
        expect.objectContaining({
          direction: 'up',
          count: 2,
          dryRun: true,
          noLock: false
        })
      );
    });

    it('should handle migration errors gracefully', async () => {
      const error = new Error('Migration failed');
      mockMigrate.mockRejectedValue(error);

      const result = await migrationWrapper.up();

      expect(result.success).toBe(false);
      expect(result.error).toBe('Migration failed');
      expect(result.migrationsRun).toHaveLength(0);
      expect(result.durationMs).toBeGreaterThan(0);
    });
  });

  describe('down', () => {
    beforeEach(() => {
      migrationWrapper = new MigrationWrapper(mockLocalConfig);
    });

    it('should rollback migrations successfully', async () => {
      const mockResult = [
        { name: '20241201000001_create_posts', timestamp: Date.now() }
      ];

      mockMigrate.mockResolvedValue(mockResult);

      const result = await migrationWrapper.down();

      expect(result.success).toBe(true);
      expect(result.migrationsRun).toHaveLength(1);
      expect(result.migrationsRun[0]).toMatchObject({
        id: '20241201000001_create_posts',
        name: '20241201000001_create_posts',
        status: MigrationStatus.ROLLED_BACK,
        direction: MigrationDirection.DOWN
      });

      expect(mockMigrate).toHaveBeenCalledWith(
        expect.objectContaining({
          direction: 'down',
          count: 1
        })
      );
    });

    it('should rollback migrations with options', async () => {
      mockMigrate.mockResolvedValue([]);

      const options = {
        count: 3,
        to: '20241201000000_create_users'
      };

      const result = await migrationWrapper.down(options);

      expect(result.success).toBe(true);
      expect(mockMigrate).toHaveBeenCalledWith(
        expect.objectContaining({
          direction: 'down',
          count: 3,
          to: '20241201000000_create_users'
        })
      );
    });

    it('should handle rollback errors gracefully', async () => {
      const error = new Error('Rollback failed');
      mockMigrate.mockRejectedValue(error);

      const result = await migrationWrapper.down();

      expect(result.success).toBe(false);
      expect(result.error).toBe('Rollback failed');
      expect(result.migrationsRun).toHaveLength(0);
    });
  });

  describe('getStatus', () => {
    beforeEach(() => {
      migrationWrapper = new MigrationWrapper(mockLocalConfig);
    });

    it('should return migration status correctly', async () => {
      const appliedMigrations = [
        { name: '20241201000000_create_users' }
      ];

      const migrationFiles = [
        '20241201000000_create_users.sql',
        '20241201000001_create_posts.sql'
      ];

      mockMigrate.mockResolvedValue(appliedMigrations);
      vi.mocked(fs.readdir).mockResolvedValue(migrationFiles as any);

      const status = await migrationWrapper.getStatus();

      expect(status).toHaveLength(2);
      expect(status[0]).toMatchObject({
        id: '20241201000000_create_users',
        name: '20241201000000_create_users',
        status: MigrationStatus.APPLIED
      });
      expect(status[1]).toMatchObject({
        id: '20241201000001_create_posts',
        name: '20241201000001_create_posts',
        status: MigrationStatus.PENDING
      });
    });

    it('should handle directory read errors', async () => {
      mockMigrate.mockResolvedValue([]);
      vi.mocked(fs.readdir).mockRejectedValue(new Error('Directory not found'));

      await expect(migrationWrapper.getStatus()).rejects.toThrow(DatabaseError);
    });

    it('should handle migration query errors', async () => {
      mockMigrate.mockRejectedValue(new Error('Database connection failed'));

      await expect(migrationWrapper.getStatus()).rejects.toThrow(DatabaseError);
    });
  });

  describe('testConnection', () => {
    beforeEach(() => {
      migrationWrapper = new MigrationWrapper(mockLocalConfig);
    });

    it('should return true for successful connection', async () => {
      mockMigrate.mockResolvedValue([]);

      const result = await migrationWrapper.testConnection();

      expect(result).toBe(true);
      expect(mockMigrate).toHaveBeenCalledWith(
        expect.objectContaining({
          direction: 'up',
          dryRun: true,
          count: 0
        })
      );
    });

    it('should return false for failed connection', async () => {
      mockMigrate.mockRejectedValue(new Error('Connection failed'));

      const result = await migrationWrapper.testConnection();

      expect(result).toBe(false);
    });
  });

  describe('getConfig', () => {
    beforeEach(() => {
      migrationWrapper = new MigrationWrapper(mockLocalConfig);
    });

    it('should return migration configuration', () => {
      const config = migrationWrapper.getConfig();

      expect(config).toHaveProperty('database');
      expect(config).toHaveProperty('migrations');
      expect(config).toHaveProperty('execution');
      expect(config.database.host).toBe('localhost');
      expect(config.database.port).toBe(5432);
      expect(config.database.database).toBe('testdb');
      expect(config.database.user).toBe('test');
    });
  });

  describe('template variable replacement', () => {
    beforeEach(() => {
      migrationWrapper = new MigrationWrapper(mockLocalConfig);
    });

    it('should replace all template variables correctly', async () => {
      const mockAccess = vi.mocked(fs.access);
      const mockMkdir = vi.mocked(fs.mkdir);
      const mockWriteFile = vi.mocked(fs.writeFile);

      mockAccess
        .mockRejectedValueOnce({ code: 'ENOENT' })
        .mockRejectedValueOnce({ code: 'ENOENT' });

      mockMkdir.mockResolvedValueOnce(undefined);
      mockWriteFile.mockResolvedValueOnce();

      const options = {
        name: 'test_template_replacement',
        template: MigrationTemplateType.CREATE_TABLE,
        templateData: { 
          tableName: 'test_table',
          customVar: 'custom_value'
        }
      };

      const result = await migrationWrapper.createMigration(options);

      expect(result.content).toContain('test_table');
      expect(result.content).toContain('test_template_replacement');
      expect(result.content).not.toContain('{tableName}');
      expect(result.content).not.toContain('{name}');
      expect(result.content).not.toContain('{description}');
    });

    describe('error scenarios', () => {
      it('should handle node-pg-migrate import errors', async () => {
        mockMigrate.mockRejectedValue(new Error('Module not found'));

        const wrapper = new MigrationWrapper(mockLocalConfig);
        await expect(wrapper.up()).rejects.toThrow(Error);
      });
    });
  });
});