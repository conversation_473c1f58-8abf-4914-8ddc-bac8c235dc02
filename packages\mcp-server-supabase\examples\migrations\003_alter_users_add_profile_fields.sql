-- Migration: Alter users table - add profile fields
-- Description: Add additional profile fields to users table
-- Template: ALTER_TABLE
-- Best Practices Demonstrated:
--   - Use IF NOT EXISTS for idempotent column additions
--   - Add constraints after column creation
--   - Update existing data with sensible defaults
--   - Create indexes for new searchable fields

-- Up migration
-- Add new profile columns
ALTER TABLE users 
  ADD COLUMN IF NOT EXISTS bio TEXT,
  ADD COLUMN IF NOT EXISTS location VARCHAR(100),
  ADD COLUMN IF NOT EXISTS website_url TEXT,
  ADD COLUMN IF NOT EXISTS date_of_birth DATE,
  ADD COLUMN IF NOT EXISTS phone_number VARCHAR(20),
  ADD COLUMN IF NOT EXISTS timezone VARCHAR(50) DEFAULT 'UTC',
  ADD COLUMN IF NOT EXISTS language_preference VARCHAR(10) DEFAULT 'en',
  ADD COLUMN IF NOT EXISTS notification_preferences JSONB DEFAULT '{"email": true, "push": true, "sms": false}';

-- Add constraints for new fields
ALTER TABLE users ADD CONSTRAINT chk_users_website_url_format 
  CHECK (website_url IS NULL OR website_url ~* '^https?://.*');

ALTER TABLE users ADD CONSTRAINT chk_users_phone_format 
  CHECK (phone_number IS NULL OR phone_number ~* '^\+?[1-9]\d{1,14}$');

ALTER TABLE users ADD CONSTRAINT chk_users_language_code 
  CHECK (language_preference ~* '^[a-z]{2}(-[A-Z]{2})?$');

ALTER TABLE users ADD CONSTRAINT chk_users_age_reasonable 
  CHECK (date_of_birth IS NULL OR date_of_birth > '1900-01-01' AND date_of_birth < CURRENT_DATE);

-- Create indexes for searchable fields
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_location ON users (location) 
  WHERE location IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_language ON users (language_preference);

-- Create partial index for users with complete profiles
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_complete_profile ON users (id) 
  WHERE bio IS NOT NULL AND location IS NOT NULL;

-- Create GIN index for notification preferences
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_notification_prefs ON users 
  USING gin(notification_preferences);

-- Update existing users with default notification preferences if NULL
UPDATE users 
SET notification_preferences = '{"email": true, "push": true, "sms": false}'
WHERE notification_preferences IS NULL;

-- Down migration
-- ALTER TABLE users 
--   DROP COLUMN IF EXISTS bio,
--   DROP COLUMN IF EXISTS location,
--   DROP COLUMN IF EXISTS website_url,
--   DROP COLUMN IF EXISTS date_of_birth,
--   DROP COLUMN IF EXISTS phone_number,
--   DROP COLUMN IF EXISTS timezone,
--   DROP COLUMN IF EXISTS language_preference,
--   DROP COLUMN IF EXISTS notification_preferences;
