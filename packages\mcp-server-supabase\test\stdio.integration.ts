import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { describe, expect, test } from 'vitest';
import { ACCESS_TOKEN, MCP_CLIENT_NAME, MCP_CLIENT_VERSION } from './mocks.js';
import { LoggingMessageNotificationSchema } from '@modelcontextprotocol/sdk/types.js';

type SetupOptions = {
  anonKey?: string;
  serviceKey?: string;
  supabaseUrl?: string;
  readOnly?: boolean;
};

async function setup(options: SetupOptions = {}) {
  const {
    anonKey = process.env.SUPABASE_ANON_KEY,
    serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY,
    supabaseUrl = process.env.SUPABASE_URL,
    readOnly
  } = options;

  const client = new Client(
    {
      name: MCP_CLIENT_NAME,
      version: MCP_CLIENT_VERSION,
    },
    {
      capabilities: {},
    }
  );

  client.setNotificationHandler(LoggingMessageNotificationSchema, (message) => {
    const { level, data } = message.params;
    if (level === 'error') {
      // Log error data for test debugging
    } else {
      // Log success data for test verification
    }
  });

  const command = 'npx';
  const args = ['@supabase/mcp-server-supabase'];

  if (supabaseUrl) {
    args.push('--supabase-url', supabaseUrl);
  }

  if (anonKey) {
    args.push('--anon-key', anonKey);
  }

  if (serviceKey) {
    args.push('--service-key', serviceKey);
  }

  if (readOnly) {
    args.push('--read-only');
  }

  const clientTransport = new StdioClientTransport({
    command,
    args,
  });

  await client.connect(clientTransport);

  return { client, clientTransport };
}

describe('stdio', () => {
  test('server connects and lists tools', async () => {
    const { client } = await setup();

    const { tools } = await client.listTools();

    expect(tools.length).toBeGreaterThan(0);
  });

  test('missing anon key fails', async () => {
    const setupPromise = setup({ anonKey: undefined, serviceKey: undefined });

    await expect(setupPromise).rejects.toThrow('MCP error -32000');
  });
});
