/**
 * Performance Optimization Module
 * 
 * Comprehensive performance optimization suite including caching,
 * connection pooling, circuit breakers, and compression.
 */

// Cache Management
export {
  CacheManager,
  type CacheEntry,
  type CacheOptions,
  type CacheStats,
  type CacheManagerOptions,
} from './cache-manager.js';

// Connection Pooling
export {
  ConnectionPool,
  type ConnectionConfig,
  type PoolOptions,
  type Connection,
  type PoolStats,
  type ConnectionRequest,
} from './connection-pool.js';

// Circuit Breaker
export {
  CircuitBreaker,
  CircuitBreakerRegistry,
  type CircuitState,
  type CircuitBreakerOptions,
  type CircuitBreakerStats,
  type ExecutionResult,
} from './circuit-breaker.js';

// Compression and Streaming
export {
  CompressionMiddleware,
  ResponseOptimizer,
  type CompressionAlgorithm,
  type CompressionOptions,
  type StreamingOptions,
  type CacheHeaders,
  type CompressionStats,
} from './compression-middleware.js';

// Performance utilities and integrations
import { EventEmitter } from 'events';
import { contextLogger } from '../utils/logger.js';
import type { ConfigManager } from '../config/config-manager.js';
import { CacheManager } from './cache-manager.js';
import { ConnectionPool } from './connection-pool.js';
import { CircuitBreakerRegistry } from './circuit-breaker.js';
import { ResponseOptimizer } from './compression-middleware.js';

export interface PerformanceManagerOptions {
  enableCaching?: boolean;
  enableConnectionPooling?: boolean;
  enableCircuitBreakers?: boolean;
  enableCompression?: boolean;
  cacheOptions?: any;
  poolOptions?: any;
  circuitBreakerOptions?: any;
  compressionOptions?: any;
}

export interface PerformanceMetrics {
  cache: {
    enabled: boolean;
    stats?: any;
  };
  connectionPool: {
    enabled: boolean;
    stats?: any;
  };
  circuitBreakers: {
    enabled: boolean;
    stats?: Record<string, any>;
  };
  compression: {
    enabled: boolean;
    stats?: any;
  };
  overall: {
    uptime: number;
    memoryUsage: NodeJS.MemoryUsage;
    cpuUsage?: NodeJS.CpuUsage;
  };
}

/**
 * Unified performance management system
 */
export class PerformanceManager extends EventEmitter {
  private readonly logger = contextLogger.child({ component: 'PerformanceManager' });
  private readonly config?: ConfigManager;
  private readonly options: Required<PerformanceManagerOptions>;
  
  private cacheManager?: CacheManager;
  private connectionPools = new Map<string, ConnectionPool>();
  private startTime = new Date();
  private metricsTimer?: NodeJS.Timeout;
  private cpuUsageStart?: NodeJS.CpuUsage;

  constructor(options: PerformanceManagerOptions = {}, config?: ConfigManager) {
    super();
    
    this.config = config;
    this.options = {
      enableCaching: options.enableCaching ?? config?.get('PERFORMANCE_ENABLE_CACHING', true) ?? true,
      enableConnectionPooling: options.enableConnectionPooling ?? config?.get('PERFORMANCE_ENABLE_POOLING', true) ?? true,
      enableCircuitBreakers: options.enableCircuitBreakers ?? config?.get('PERFORMANCE_ENABLE_CIRCUIT_BREAKERS', true) ?? true,
      enableCompression: options.enableCompression ?? config?.get('PERFORMANCE_ENABLE_COMPRESSION', true) ?? true,
      cacheOptions: options.cacheOptions ?? {},
      poolOptions: options.poolOptions ?? {},
      circuitBreakerOptions: options.circuitBreakerOptions ?? {},
      compressionOptions: options.compressionOptions ?? {},
    };

    this.logger.info('Performance manager initialized', {
      enableCaching: this.options.enableCaching,
      enableConnectionPooling: this.options.enableConnectionPooling,
      enableCircuitBreakers: this.options.enableCircuitBreakers,
      enableCompression: this.options.enableCompression,
    });
  }

  /**
   * Initialize all performance components
   */
  async initialize(): Promise<void> {
    try {
      // Initialize caching
      if (this.options.enableCaching) {
        this.cacheManager = new CacheManager(this.options.cacheOptions, this.config);
        this.logger.info('Cache manager initialized');
      }

      // Initialize compression
      if (this.options.enableCompression) {
        ResponseOptimizer.initialize(this.options.compressionOptions, this.config);
        this.logger.info('Response optimizer initialized');
      }

      // Start metrics collection
      this.startMetricsCollection();
      this.cpuUsageStart = process.cpuUsage();

      this.logger.info('Performance manager initialization complete');
      this.emit('initialized');
    } catch (error) {
      this.logger.error('Failed to initialize performance manager', error instanceof Error ? error : undefined);
      throw error;
    }
  }

  /**
   * Get cache manager instance
   */
  getCacheManager(): CacheManager | undefined {
    return this.cacheManager;
  }

  /**
   * Create or get connection pool
   */
  getConnectionPool(name: string, connectionConfig?: any, poolOptions?: any): ConnectionPool | undefined {
    if (!this.options.enableConnectionPooling) {
      return undefined;
    }

    if (!this.connectionPools.has(name)) {
      if (!connectionConfig) {
        throw new Error(`Connection config required for new pool: ${name}`);
      }
      
      const pool = new ConnectionPool(
        connectionConfig,
        { ...this.options.poolOptions, ...poolOptions },
        this.config
      );
      
      this.connectionPools.set(name, pool);
      this.logger.info('Connection pool created', { name });
    }

    return this.connectionPools.get(name);
  }

  /**
   * Get circuit breaker
   */
  getCircuitBreaker(name: string, options?: any) {
    if (!this.options.enableCircuitBreakers) {
      return undefined;
    }

    return CircuitBreakerRegistry.getOrCreate(
      name,
      { ...this.options.circuitBreakerOptions, ...options },
      this.config
    );
  }

  /**
   * Get comprehensive performance metrics
   */
  async getMetrics(): Promise<PerformanceMetrics> {
    const now = Date.now();
    const uptime = now - this.startTime.getTime();
    const memoryUsage = process.memoryUsage();
    const cpuUsage = this.cpuUsageStart ? process.cpuUsage(this.cpuUsageStart) : undefined;

    const metrics: PerformanceMetrics = {
      cache: {
        enabled: this.options.enableCaching,
        stats: this.cacheManager?.getStats(),
      },
      connectionPool: {
        enabled: this.options.enableConnectionPooling,
        stats: this.getConnectionPoolStats(),
      },
      circuitBreakers: {
        enabled: this.options.enableCircuitBreakers,
        stats: CircuitBreakerRegistry.getAllStats(),
      },
      compression: {
        enabled: this.options.enableCompression,
        stats: ResponseOptimizer.getStats(),
      },
      overall: {
        uptime,
        memoryUsage,
        cpuUsage,
      },
    };

    return metrics;
  }

  /**
   * Optimize response using available performance features
   */
  async optimizeResponse(data: any, context: any = {}): Promise<any> {
    try {
      // Use compression if enabled
      if (this.options.enableCompression) {
        return await ResponseOptimizer.optimizeResponse(data, context);
      }

      // Fallback to basic response
      return {
        data: Buffer.from(typeof data === 'string' ? data : JSON.stringify(data)),
        headers: {
          'Content-Type': 'application/json; charset=utf-8',
        },
        statusCode: 200,
      };
    } catch (error) {
      this.logger.error('Failed to optimize response', error instanceof Error ? error : undefined);
      throw error;
    }
  }

  /**
   * Execute function with performance optimizations
   */
  async executeWithOptimizations<T>(
    fn: () => Promise<T>,
    options: {
      cacheKey?: string;
      cacheTtl?: number;
      circuitBreakerName?: string;
      useConnectionPool?: string;
    } = {}
  ): Promise<T> {
    // Try cache first
    if (options.cacheKey && this.cacheManager) {
      const cached = await this.cacheManager.get<T>(options.cacheKey);
      if (cached !== undefined) {
        this.logger.debug('Cache hit for optimized execution', { cacheKey: options.cacheKey });
        return cached;
      }
    }

    // Use circuit breaker if specified
    if (options.circuitBreakerName) {
      const circuitBreaker = this.getCircuitBreaker(options.circuitBreakerName);
      if (circuitBreaker) {
        const result = await circuitBreaker.execute(fn);
        
        // Cache result if successful
        if (options.cacheKey && this.cacheManager) {
          await this.cacheManager.set(options.cacheKey, result, { ttl: options.cacheTtl });
        }
        
        return result;
      }
    }

    // Execute normally
    const result = await fn();
    
    // Cache result
    if (options.cacheKey && this.cacheManager) {
      await this.cacheManager.set(options.cacheKey, result, { ttl: options.cacheTtl });
    }
    
    return result;
  }

  /**
   * Shutdown performance manager
   */
  async shutdown(): Promise<void> {
    try {
      // Stop metrics collection
      if (this.metricsTimer) {
        clearInterval(this.metricsTimer);
        this.metricsTimer = undefined;
      }

      // Shutdown cache manager
      if (this.cacheManager) {
        await this.cacheManager.shutdown();
        this.cacheManager = undefined;
      }

      // Shutdown connection pools
      for (const [name, pool] of this.connectionPools.entries()) {
        await pool.shutdown();
        this.logger.info('Connection pool shutdown', { name });
      }
      this.connectionPools.clear();

      // Shutdown circuit breakers
      CircuitBreakerRegistry.shutdown();

      this.removeAllListeners();
      
      this.logger.info('Performance manager shutdown complete');
      this.emit('shutdown');
    } catch (error) {
      this.logger.error('Error during performance manager shutdown', error instanceof Error ? error : undefined);
      throw error;
    }
  }

  /**
   * Get connection pool statistics
   */
  private getConnectionPoolStats(): Record<string, any> {
    const stats: Record<string, any> = {};
    
    for (const [name, pool] of this.connectionPools.entries()) {
      stats[name] = pool.getStats();
    }
    
    return stats;
  }

  /**
   * Start metrics collection
   */
  private startMetricsCollection(): void {
    this.metricsTimer = setInterval(async () => {
      try {
        const metrics = await this.getMetrics();
        this.emit('metrics', metrics);
        
        this.logger.debug('Performance metrics collected', {
          cacheHitRate: metrics.cache.stats?.hitRate,
          memoryUsage: Math.round(metrics.overall.memoryUsage.heapUsed / 1024 / 1024),
          uptime: Math.round(metrics.overall.uptime / 1000),
        });
      } catch (error) {
        this.logger.error('Failed to collect metrics', error instanceof Error ? error : undefined);
      }
    }, 60000); // Collect metrics every minute
  }
}
