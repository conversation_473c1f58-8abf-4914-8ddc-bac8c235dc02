import { EventEmitter } from 'events';
import { contextLogger } from '../utils/logger.js';
import { healthChecker } from '../utils/middleware.js';
import { configHealthMonitor } from '../config/config-health.js';
import { PerformanceManager } from '../performance/index.js';

/**
 * Alert severity levels
 */
export type AlertSeverity = 'info' | 'warning' | 'error' | 'critical';

/**
 * Alert rule configuration
 */
export interface AlertRule {
  id: string;
  name: string;
  description: string;
  severity: AlertSeverity;
  condition: (metrics: any) => boolean;
  threshold?: number;
  enabled: boolean;
  cooldownMs: number; // Minimum time between alerts
  tags: Record<string, string>;
}

/**
 * Alert instance
 */
export interface Alert {
  id: string;
  ruleId: string;
  name: string;
  description: string;
  severity: AlertSeverity;
  timestamp: Date;
  value?: number;
  threshold?: number;
  tags: Record<string, string>;
  resolved: boolean;
  resolvedAt?: Date;
  metadata: Record<string, any>;
}

/**
 * Alert handler function type
 */
export type AlertHandler = (alert: Alert) => Promise<void> | void;

/**
 * Alerting system for monitoring thresholds and conditions
 */
export class AlertingSystem extends EventEmitter {
  private rules = new Map<string, AlertRule>();
  private activeAlerts = new Map<string, Alert>();
  private alertHistory: Alert[] = [];
  private handlers = new Map<string, AlertHandler>();
  private lastAlertTime = new Map<string, number>();
  private logger = contextLogger.child({ component: 'alerting-system' });
  private checkInterval?: NodeJS.Timeout;
  private maxHistorySize = 1000;

  constructor() {
    super();
    this.setupDefaultRules();
    this.setupDefaultHandlers();
  }

  /**
   * Add an alert rule
   */
  addRule(rule: AlertRule): void {
    this.rules.set(rule.id, rule);
    this.logger.info('Alert rule added', {
      id: rule.id,
      name: rule.name,
      severity: rule.severity,
    });
  }

  /**
   * Remove an alert rule
   */
  removeRule(ruleId: string): boolean {
    const removed = this.rules.delete(ruleId);
    if (removed) {
      this.logger.info('Alert rule removed', { ruleId });
    }
    return removed;
  }

  /**
   * Enable or disable a rule
   */
  setRuleEnabled(ruleId: string, enabled: boolean): boolean {
    const rule = this.rules.get(ruleId);
    if (rule) {
      rule.enabled = enabled;
      this.logger.info('Alert rule updated', { ruleId, enabled });
      return true;
    }
    return false;
  }

  /**
   * Add an alert handler
   */
  addHandler(name: string, handler: AlertHandler): void {
    this.handlers.set(name, handler);
    this.logger.info('Alert handler added', { name });
  }

  /**
   * Remove an alert handler
   */
  removeHandler(name: string): boolean {
    const removed = this.handlers.delete(name);
    if (removed) {
      this.logger.info('Alert handler removed', { name });
    }
    return removed;
  }

  /**
   * Start monitoring and checking alert conditions
   */
  startMonitoring(intervalMs: number = 60000): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }

    this.checkInterval = setInterval(async () => {
      await this.checkAlertConditions();
    }, intervalMs);

    this.logger.info('Alert monitoring started', { intervalMs });
  }

  /**
   * Stop monitoring
   */
  stopMonitoring(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = undefined;
      this.logger.info('Alert monitoring stopped');
    }
  }

  /**
   * Manually trigger alert condition check
   */
  async checkAlertConditions(): Promise<void> {
    try {
      const metrics = await this.collectMetrics();
      
      for (const [ruleId, rule] of this.rules.entries()) {
        if (!rule.enabled) continue;
        
        try {
          const shouldAlert = rule.condition(metrics);
          const existingAlert = this.activeAlerts.get(ruleId);
          
          if (shouldAlert && !existingAlert) {
            // New alert condition met
            if (this.isInCooldown(ruleId)) {
              continue; // Skip due to cooldown
            }
            
            await this.triggerAlert(rule, metrics);
          } else if (!shouldAlert && existingAlert) {
            // Alert condition resolved
            await this.resolveAlert(ruleId);
          }
        } catch (error) {
          this.logger.error('Error checking alert rule', error instanceof Error ? error : undefined, {
            ruleId,
            ruleName: rule.name,
          });
        }
      }
    } catch (error) {
      this.logger.error('Error during alert condition check', error instanceof Error ? error : undefined);
    }
  }

  /**
   * Manually trigger an alert
   */
  async triggerAlert(rule: AlertRule, metrics: any): Promise<Alert> {
    const alert: Alert = {
      id: `${rule.id}-${Date.now()}`,
      ruleId: rule.id,
      name: rule.name,
      description: rule.description,
      severity: rule.severity,
      timestamp: new Date(),
      threshold: rule.threshold,
      tags: { ...rule.tags },
      resolved: false,
      metadata: { metrics },
    };

    // Store active alert
    this.activeAlerts.set(rule.id, alert);
    
    // Add to history
    this.alertHistory.unshift(alert);
    if (this.alertHistory.length > this.maxHistorySize) {
      this.alertHistory.pop();
    }
    
    // Update cooldown
    this.lastAlertTime.set(rule.id, Date.now());
    
    this.logger.warn('Alert triggered', {
      alertId: alert.id,
      ruleId: rule.id,
      name: rule.name,
      severity: rule.severity,
    });
    
    // Emit event
    this.emit('alert', alert);
    
    // Execute handlers
    await this.executeHandlers(alert);
    
    return alert;
  }

  /**
   * Resolve an active alert
   */
  async resolveAlert(ruleId: string): Promise<boolean> {
    const alert = this.activeAlerts.get(ruleId);
    if (!alert) return false;
    
    alert.resolved = true;
    alert.resolvedAt = new Date();
    
    this.activeAlerts.delete(ruleId);
    
    this.logger.info('Alert resolved', {
      alertId: alert.id,
      ruleId,
      duration: alert.resolvedAt.getTime() - alert.timestamp.getTime(),
    });
    
    // Emit event
    this.emit('alert-resolved', alert);
    
    return true;
  }

  /**
   * Get all active alerts
   */
  getActiveAlerts(): Alert[] {
    return Array.from(this.activeAlerts.values());
  }

  /**
   * Get alert history
   */
  getAlertHistory(limit?: number): Alert[] {
    return limit ? this.alertHistory.slice(0, limit) : [...this.alertHistory];
  }

  /**
   * Get alert statistics
   */
  getStats(): {
    totalRules: number;
    enabledRules: number;
    activeAlerts: number;
    totalAlerts: number;
    alertsByseverity: Record<AlertSeverity, number>;
  } {
    const alertsBySevertiy = this.alertHistory.reduce((acc, alert) => {
      acc[alert.severity] = (acc[alert.severity] || 0) + 1;
      return acc;
    }, {} as Record<AlertSeverity, number>);
    
    return {
      totalRules: this.rules.size,
      enabledRules: Array.from(this.rules.values()).filter(r => r.enabled).length,
      activeAlerts: this.activeAlerts.size,
      totalAlerts: this.alertHistory.length,
      alertsBySevertiy,
    };
  }

  /**
   * Check if a rule is in cooldown period
   */
  private isInCooldown(ruleId: string): boolean {
    const rule = this.rules.get(ruleId);
    const lastAlert = this.lastAlertTime.get(ruleId);
    
    if (!rule || !lastAlert) return false;
    
    return Date.now() - lastAlert < rule.cooldownMs;
  }

  /**
   * Collect metrics from various sources
   */
  private async collectMetrics(): Promise<any> {
    const [health, performanceMetrics, configHealth] = await Promise.all([
      healthChecker.getHealth(),
      performanceManager.getMetrics(),
      configHealthMonitor.checkHealth(),
    ]);

    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    return {
      health,
      performance: performanceMetrics,
      config: configHealth,
      system: {
        memory: memoryUsage,
        cpu: cpuUsage,
        uptime: process.uptime(),
      },
    };
  }

  /**
   * Execute all registered alert handlers
   */
  private async executeHandlers(alert: Alert): Promise<void> {
    const promises = Array.from(this.handlers.entries()).map(async ([name, handler]) => {
      try {
        await handler(alert);
      } catch (error) {
        this.logger.error('Alert handler failed', error instanceof Error ? error : undefined, {
          handlerName: name,
          alertId: alert.id,
        });
      }
    });
    
    await Promise.allSettled(promises);
  }

  /**
   * Setup default alert rules
   */
  private setupDefaultRules(): void {
    // High memory usage
    this.addRule({
      id: 'high-memory-usage',
      name: 'High Memory Usage',
      description: 'Memory usage exceeds 80% of heap limit',
      severity: 'warning',
      condition: (metrics) => {
        const { heapUsed, heapTotal } = metrics.system.memory;
        return (heapUsed / heapTotal) > 0.8;
      },
      threshold: 0.8,
      enabled: true,
      cooldownMs: 5 * 60 * 1000, // 5 minutes
      tags: { category: 'system', type: 'memory' },
    });

    // Database connection failure
    this.addRule({
      id: 'database-connection-failure',
      name: 'Database Connection Failure',
      description: 'Database health check is failing',
      severity: 'critical',
      condition: (metrics) => {
        return metrics.health.checks.database?.status === 'fail';
      },
      enabled: true,
      cooldownMs: 2 * 60 * 1000, // 2 minutes
      tags: { category: 'database', type: 'connectivity' },
    });

    // High error rate
    this.addRule({
      id: 'high-error-rate',
      name: 'High Error Rate',
      description: 'Error rate exceeds 5%',
      severity: 'error',
      condition: (metrics) => {
        const operations = metrics.performance?.operations || {};
        const totalErrors = Object.values(operations).reduce((sum: number, op: any) => 
          sum + (op.errors || 0), 0);
        const totalOps = Object.values(operations).reduce((sum: number, op: any) => 
          sum + (op.count || 0), 0);
        return totalOps > 0 && (totalErrors / totalOps) > 0.05;
      },
      threshold: 0.05,
      enabled: true,
      cooldownMs: 10 * 60 * 1000, // 10 minutes
      tags: { category: 'performance', type: 'errors' },
    });
  }

  /**
   * Setup default alert handlers
   */
  private setupDefaultHandlers(): void {
    // Console logger handler
    this.addHandler('console', (alert: Alert) => {
      const message = `[${alert.severity.toUpperCase()}] ${alert.name}: ${alert.description}`;
      
      switch (alert.severity) {
        case 'critical':
        case 'error':
          this.logger.error(message, undefined, { alert });
          break;
        case 'warning':
          this.logger.warn(message, { alert });
          break;
        default:
          this.logger.info(message, { alert });
      }
    });
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.stopMonitoring();
    this.rules.clear();
    this.activeAlerts.clear();
    this.handlers.clear();
    this.lastAlertTime.clear();
    this.logger.info('Alerting system destroyed');
  }
}

/**
 * Global alerting system instance
 */
export const alertingSystem = new AlertingSystem();
