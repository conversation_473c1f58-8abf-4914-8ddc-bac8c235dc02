-- Migration: <PERSON>reate posts table
-- Description: Blog posts table with user relationships and content management
-- Template: CREATE_TABLE
-- Best Practices Demonstrated:
--   - Foreign key relationships with proper constraints
--   - JSON/JSONB for flexible metadata
--   - Text search capabilities
--   - Soft delete pattern
--   - Status enum pattern

-- Up migration
-- Create enum for post status
CREATE TYPE post_status AS ENUM ('draft', 'published', 'archived', 'deleted');

CREATE TABLE IF NOT EXISTS posts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(255) NOT NULL,
  slug VARCHAR(255) UNIQUE NOT NULL,
  content TEXT,
  excerpt TEXT,
  status post_status DEFAULT 'draft',
  author_id UUID NOT NULL,
  featured_image_url TEXT,
  metadata JSONB DEFAULT '{}',
  tags TEXT[] DEFAULT '{}',
  view_count INTEGER DEFAULT 0,
  published_at TIMESTAMP WITH TIME ZONE,
  deleted_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Foreign key constraints
  CONSTRAINT fk_posts_author FOREIGN KEY (author_id) 
    REFERENCES users(id) ON DELETE CASCADE,
    
  -- Check constraints
  CONSTRAINT chk_posts_title_length CHECK (length(title) >= 1),
  CONSTRAINT chk_posts_slug_format CHECK (slug ~* '^[a-z0-9-]+$'),
  CONSTRAINT chk_posts_published_at CHECK (
    (status = 'published' AND published_at IS NOT NULL) OR 
    (status != 'published')
  )
);

-- Create indexes for performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_posts_author_id ON posts (author_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_posts_status ON posts (status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_posts_slug ON posts (slug);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_posts_published_at ON posts (published_at) 
  WHERE status = 'published';
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_posts_created_at ON posts (created_at);

-- Full-text search index
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_posts_search ON posts 
  USING gin(to_tsvector('english', title || ' ' || COALESCE(content, '') || ' ' || COALESCE(excerpt, '')));

-- JSON metadata index
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_posts_metadata ON posts USING gin(metadata);

-- Array tags index
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_posts_tags ON posts USING gin(tags);

-- Soft delete index (exclude deleted posts)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_posts_active ON posts (created_at) 
  WHERE deleted_at IS NULL;

-- Create trigger for updated_at
CREATE TRIGGER trigger_posts_updated_at
  BEFORE UPDATE ON posts
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Down migration
-- DROP TRIGGER IF EXISTS trigger_posts_updated_at ON posts;
-- DROP TABLE IF EXISTS posts;
-- DROP TYPE IF EXISTS post_status;
