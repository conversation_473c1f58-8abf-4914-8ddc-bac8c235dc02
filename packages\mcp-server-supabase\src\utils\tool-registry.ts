import { z } from 'zod';
import type { Tool } from '@supabase/mcp-utils';
import { contextLogger, type LogContext } from './logger.js';
import { ValidationError, ToolError, ErrorCode } from './errors.js';

/**
 * Enhanced tool metadata for better discovery and validation
 */
export interface ToolMetadata {
  name: string;
  description: string;
  category: string;
  version: string;
  author?: string;
  tags: string[];
  deprecated?: boolean;
  deprecationMessage?: string;
  examples?: Array<{
    name: string;
    description: string;
    input: Record<string, unknown>;
    expectedOutput?: string;
  }>;
  permissions?: {
    readOnly: boolean;
    requiresAuth: boolean;
    scopes?: string[];
  };
  performance?: {
    estimatedDuration?: number; // milliseconds
    complexity?: 'low' | 'medium' | 'high';
    cacheability?: 'none' | 'short' | 'long';
  };
}

/**
 * Enhanced tool interface with metadata
 */
export interface EnhancedTool<
  Params extends z.ZodObject<any> = z.ZodObject<any>,
  Result = unknown,
> extends Tool<Params, Result> {
  metadata: ToolMetadata;
  validate?: (params: z.infer<Params>) => Promise<ValidationResult>;
  preExecute?: (params: z.infer<Params>, context: LogContext) => Promise<void>;
  postExecute?: (params: z.infer<Params>, result: Result, context: LogContext) => Promise<void>;
}

/**
 * Validation result interface
 */
export interface ValidationResult {
  valid: boolean;
  errors: Array<{
    field: string;
    message: string;
    code: string;
  }>;
  warnings: Array<{
    field: string;
    message: string;
    code: string;
  }>;
}

/**
 * Tool discovery filter options
 */
export interface ToolDiscoveryFilter {
  category?: string;
  tags?: string[];
  deprecated?: boolean;
  readOnly?: boolean;
  search?: string;
  version?: string;
}

/**
 * Tool execution context
 */
export interface ToolExecutionContext extends LogContext {
  toolName: string;
  startTime: number;
  permissions?: {
    readOnly: boolean;
    scopes: string[];
  };
}

/**
 * Enhanced tool registry for better tool management
 */
export class ToolRegistry {
  private tools = new Map<string, EnhancedTool>();
  private categories = new Map<string, string[]>();
  private logger = contextLogger.child({ component: 'ToolRegistry' });

  /**
   * Register a tool with enhanced metadata
   */
  register(
    name: string,
    tool: EnhancedTool
  ): void {
    // Validate tool metadata
    this.validateToolMetadata(name, tool.metadata);

    // Check for name conflicts
    if (this.tools.has(name)) {
      throw new ValidationError(`Tool '${name}' is already registered`);
    }

    // Register the tool
    this.tools.set(name, tool);

    // Update category index
    const category = tool.metadata.category;
    if (!this.categories.has(category)) {
      this.categories.set(category, []);
    }
    this.categories.get(category)!.push(name);

    this.logger.debug(`Registered tool: ${name}`, {
      category: tool.metadata.category,
      version: tool.metadata.version,
      tags: tool.metadata.tags,
    });
  }

  /**
   * Register multiple tools from a tool group
   */
  registerGroup(
    groupName: string,
    tools: Record<string, Tool>,
    defaultMetadata: Partial<ToolMetadata> = {}
  ): void {
    const registeredCount = Object.keys(tools).length;
    const registeredTools: string[] = [];
    const errors: string[] = [];

    // First pass: validate all tools without registering
    const enhancedTools: Array<{ name: string; tool: EnhancedTool }> = [];

    for (const [name, tool] of Object.entries(tools)) {
      try {
        // Create enhanced tool with default metadata
        const enhancedTool: EnhancedTool = {
          ...tool,
          metadata: {
            name,
            description: tool.description,
            category: groupName,
            version: '1.0.0',
            tags: [],
            permissions: {
              readOnly: false,
              requiresAuth: false,
            },
            performance: {
              complexity: 'medium',
              cacheability: 'none',
            },
            ...defaultMetadata,
          },
        };

        // Validate tool metadata without registering
        this.validateToolMetadata(name, enhancedTool.metadata);

        // Check for name conflicts
        if (this.tools.has(name)) {
          throw new ValidationError(`Tool '${name}' is already registered`);
        }

        enhancedTools.push({ name, tool: enhancedTool });
      } catch (error) {
        const errorMessage = `Failed to validate tool '${name}': ${
          error instanceof Error ? error.message : String(error)
        }`;
        errors.push(errorMessage);
        this.logger.error(errorMessage, error instanceof Error ? error : undefined);
      }
    }

    // If any validation failed, don't register any tools
    if (errors.length > 0) {
      this.logger.error(`Tool group validation failed: ${groupName}`, undefined, {
        groupName,
        totalTools: registeredCount,
        errorCount: errors.length,
        errors,
      });

      throw new ToolError(
        `Failed to validate ${errors.length} tools in group '${groupName}': ${errors.join(', ')}`,
        ErrorCode.TOOL_EXECUTION_ERROR,
        { context: { groupName, totalTools: registeredCount, errorCount: errors.length } }
      );
    }

    // Second pass: register all validated tools
    for (const { name, tool } of enhancedTools) {
      try {
        // Register the tool (we know it's valid from first pass)
        this.tools.set(name, tool);

        // Update category index
        const category = tool.metadata.category;
        if (!this.categories.has(category)) {
          this.categories.set(category, []);
        }
        this.categories.get(category)!.push(name);

        registeredTools.push(name);

        this.logger.debug(`Registered tool: ${name}`, {
          category: tool.metadata.category,
          version: tool.metadata.version,
          tags: tool.metadata.tags,
        });
      } catch (error) {
        // This should not happen since we validated everything in the first pass
        const errorMessage = `Unexpected error registering tool '${name}': ${
          error instanceof Error ? error.message : String(error)
        }`;
        this.logger.error(errorMessage, error instanceof Error ? error : undefined);

        // Roll back any tools that were registered
        for (const registeredName of registeredTools) {
          this.tools.delete(registeredName);
          // Also clean up category index
          const registeredTool = this.tools.get(registeredName);
          if (registeredTool) {
            const category = registeredTool.metadata.category;
            const categoryTools = this.categories.get(category);
            if (categoryTools) {
              const index = categoryTools.indexOf(registeredName);
              if (index > -1) {
                categoryTools.splice(index, 1);
              }
              if (categoryTools.length === 0) {
                this.categories.delete(category);
              }
            }
          }
        }

        throw new ToolError(errorMessage, ErrorCode.TOOL_EXECUTION_ERROR, { context: { groupName, registeredTools } });
      }
    }

    this.logger.info(`Registered tool group: ${groupName}`, {
      groupName,
      totalTools: registeredCount,
      successCount: registeredTools.length,
      errorCount: 0,
    });
  }

  /**
   * Get a tool by name
   */
  get(name: string): EnhancedTool | undefined {
    return this.tools.get(name);
  }

  /**
   * Check if a tool exists
   */
  has(name: string): boolean {
    return this.tools.has(name);
  }

  /**
   * Discover tools based on filter criteria
   */
  discover(filter: ToolDiscoveryFilter = {}): EnhancedTool[] {
    let tools = Array.from(this.tools.values());

    // Apply filters
    if (filter.category) {
      tools = tools.filter(tool => tool.metadata.category === filter.category);
    }

    if (filter.tags && filter.tags.length > 0) {
      tools = tools.filter(tool =>
        filter.tags!.some(tag => tool.metadata.tags.includes(tag))
      );
    }

    if (filter.deprecated !== undefined) {
      tools = tools.filter(tool => !!tool.metadata.deprecated === filter.deprecated);
    }

    if (filter.readOnly !== undefined) {
      tools = tools.filter(tool => tool.metadata.permissions?.readOnly === filter.readOnly);
    }

    if (filter.search) {
      const searchLower = filter.search.toLowerCase();
      tools = tools.filter(tool =>
        tool.metadata.name.toLowerCase().includes(searchLower) ||
        tool.metadata.description.toLowerCase().includes(searchLower) ||
        tool.metadata.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    if (filter.version) {
      tools = tools.filter(tool => tool.metadata.version === filter.version);
    }

    return tools;
  }

  /**
   * Get all available categories
   */
  getCategories(): string[] {
    return Array.from(this.categories.keys());
  }

  /**
   * Get tools in a specific category
   */
  getByCategory(category: string): EnhancedTool[] {
    const toolNames = this.categories.get(category) || [];
    return toolNames.map(name => this.tools.get(name)!).filter(Boolean);
  }

  /**
   * Get tool statistics
   */
  getStatistics(): {
    totalTools: number;
    categories: Record<string, number>;
    deprecated: number;
    readOnly: number;
    versions: Record<string, number>;
  } {
    const tools = Array.from(this.tools.values());
    const categories: Record<string, number> = {};
    const versions: Record<string, number> = {};

    for (const tool of tools) {
      // Count by category
      const category = tool.metadata.category;
      categories[category] = (categories[category] || 0) + 1;

      // Count by version
      const version = tool.metadata.version;
      versions[version] = (versions[version] || 0) + 1;
    }

    return {
      totalTools: tools.length,
      categories,
      deprecated: tools.filter(tool => tool.metadata.deprecated).length,
      readOnly: tools.filter(tool => tool.metadata.permissions?.readOnly).length,
      versions,
    };
  }

  /**
   * Validate tool parameters
   */
  async validateToolParams(
    toolName: string,
    params: unknown
  ): Promise<ValidationResult> {
    const tool = this.tools.get(toolName);
    if (!tool) {
      return {
        valid: false,
        errors: [
          {
            field: 'tool',
            message: `Tool '${toolName}' not found`,
            code: 'TOOL_NOT_FOUND',
          },
        ],
        warnings: [],
      };
    }

    const result: ValidationResult = {
      valid: true,
      errors: [],
      warnings: [],
    };

    try {
      // Validate with Zod schema
      tool.parameters.parse(params);
    } catch (error) {
      if (error instanceof z.ZodError) {
        result.valid = false;
        result.errors = error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
          code: err.code,
        }));
      } else {
        result.valid = false;
        result.errors.push({
          field: 'params',
          message: error instanceof Error ? error.message : String(error),
          code: 'VALIDATION_ERROR',
        });
      }
    }

    // Run custom validation if available
    if (tool.validate && result.valid) {
      try {
        const customResult = await tool.validate(params as any);
        result.errors.push(...customResult.errors);
        result.warnings.push(...customResult.warnings);
        result.valid = result.valid && customResult.valid;
      } catch (error) {
        result.valid = false;
        result.errors.push({
          field: 'custom',
          message: `Custom validation failed: ${error instanceof Error ? error.message : String(error)}`,
          code: 'CUSTOM_VALIDATION_ERROR',
        });
      }
    }

    return result;
  }

  /**
   * Execute a tool with enhanced context and validation
   */
  async executeTool(
    toolName: string,
    params: unknown,
    context: Partial<ToolExecutionContext> = {}
  ): Promise<unknown> {
    const tool = this.tools.get(toolName);
    if (!tool) {
      throw new ToolError(`Tool '${toolName}' not found`, ErrorCode.TOOL_NOT_FOUND);
    }

    const executionContext: ToolExecutionContext = {
      toolName,
      startTime: Date.now(),
      requestId: context.requestId,
      ...context,
    };

    const logger = this.logger.child(executionContext);

    try {
      // Validate parameters
      const validation = await this.validateToolParams(toolName, params);
      if (!validation.valid) {
        throw new ValidationError(
          `Tool parameter validation failed: ${validation.errors.map(e => e.message).join(', ')}`
        );
      }

      // Log warnings if any
      if (validation.warnings.length > 0) {
        logger.warn('Tool parameter validation warnings', {
          warnings: validation.warnings,
        });
      }

      // Check if tool is deprecated
      if (tool.metadata.deprecated) {
        logger.warn('Using deprecated tool', {
          deprecationMessage: tool.metadata.deprecationMessage,
        });
      }

      // Run pre-execution hook
      if (tool.preExecute) {
        await tool.preExecute(params as any, executionContext);
      }

      logger.info('Executing tool', {
        category: tool.metadata.category,
        version: tool.metadata.version,
        estimatedDuration: tool.metadata.performance?.estimatedDuration,
      });

      // Execute the tool
      const result = await tool.execute(params as any);

      // Run post-execution hook
      if (tool.postExecute) {
        await tool.postExecute(params as any, result, executionContext);
      }

      const duration = Date.now() - executionContext.startTime;
      logger.info('Tool execution completed', { duration });

      return result;
    } catch (error) {
      const duration = Date.now() - executionContext.startTime;
      logger.error('Tool execution failed', error instanceof Error ? error : undefined, {
        duration,
      });
      throw error;
    }
  }

  /**
   * Convert tools to MCP format for tool listing
   */
  toMcpFormat(): Array<{
    name: string;
    description: string;
    inputSchema: any;
  }> {
    return Array.from(this.tools.entries()).map(([name, tool]) => ({
      name,
      description: tool.metadata.deprecated
        ? `[DEPRECATED] ${tool.description}${tool.metadata.deprecationMessage ? ` - ${tool.metadata.deprecationMessage}` : ''}`
        : tool.description,
      inputSchema: tool.parameters,
    }));
  }

  /**
   * Clear all registered tools
   */
  clear(): void {
    this.tools.clear();
    this.categories.clear();
    this.logger.info('Tool registry cleared');
  }

  /**
   * Validate tool metadata
   */
  private validateToolMetadata(name: string, metadata: ToolMetadata): void {
    if (!metadata.name || metadata.name !== name) {
      throw new ValidationError(`Tool metadata name '${metadata.name}' does not match tool name '${name}'`);
    }

    if (!metadata.description || metadata.description.length < 10) {
      throw new ValidationError(`Tool '${name}' must have a meaningful description (at least 10 characters)`);
    }

    if (!metadata.category) {
      throw new ValidationError(`Tool '${name}' must have a category`);
    }

    if (!metadata.version || !/^\d+\.\d+\.\d+/.test(metadata.version)) {
      throw new ValidationError(`Tool '${name}' must have a valid semantic version`);
    }

    if (!Array.isArray(metadata.tags)) {
      throw new ValidationError(`Tool '${name}' tags must be an array`);
    }

    // Validate name format
    if (!/^[a-z][a-z0-9_]*$/.test(name)) {
      throw new ValidationError(`Tool name '${name}' must follow snake_case convention`);
    }
  }
}

/**
 * Global tool registry instance
 */
export const toolRegistry = new ToolRegistry();
