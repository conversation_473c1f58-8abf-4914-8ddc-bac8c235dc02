import { describe, expect, test } from 'vitest';
import { z } from 'zod';
import {
  postgresIdentifierSchema,
  schemaNameSchema,
  tableNameSchema,
  columnNameSchema,
  columnValueSchema,
  whereOperatorSchema,
  whereConditionSchema,
  whereClauseSchema,
  orderBySchema,
  paginationSchema,
  tableSpecSchema,
  columnSelectionSchema,
  dataRecordSchema,
  conflictColumnsSchema,
  returningSchema,
  formatValidationErrors,
  safeParseWithDetails,
  type WhereClause,
} from './crud-validation.js';

/**
 * Comprehensive unit tests for CRUD validation schemas
 * 
 * This test suite validates:
 * - PostgreSQL identifier validation with reserved word checking
 * - Enhanced column value validation with size limits
 * - Comprehensive WHERE clause validation with logical operators
 * - Edge cases and error handling
 * - Validation error formatting
 */

describe('CRUD Validation Schemas', () => {
  describe('PostgreSQL Identifier Validation', () => {
    test('should accept valid identifiers', () => {
      const validIdentifiers = [
        'table_name',
        'column123',
        '_private_column',
        'user_id',
        'created_at',
        'CamelCase',
        'a',
        'a'.repeat(63), // Maximum length
      ];

      validIdentifiers.forEach(identifier => {
        const result = postgresIdentifierSchema.safeParse(identifier);
        expect(result.success).toBe(true);
      });
    });

    test('should reject invalid identifiers', () => {
      const invalidIdentifiers = [
        '', // Empty
        '123invalid', // Starts with number
        'invalid-name', // Contains hyphen
        'invalid name', // Contains space
        'invalid.name', // Contains dot
        'a'.repeat(64), // Too long
        'select', // Reserved word (lowercase)
        'SELECT', // Reserved word (uppercase)
        'table', // Reserved word
        'where', // Reserved word
      ];

      invalidIdentifiers.forEach(identifier => {
        const result = postgresIdentifierSchema.safeParse(identifier);
        expect(result.success).toBe(false);
      });
    });

    test('should validate schema names with additional constraints', () => {
      // Valid schema names
      expect(schemaNameSchema.safeParse('public').success).toBe(true);
      expect(schemaNameSchema.safeParse('my_schema').success).toBe(true);
      
      // Invalid schema names
      expect(schemaNameSchema.safeParse('information_schema').success).toBe(false);
      expect(schemaNameSchema.safeParse('pg_catalog').success).toBe(false);
      expect(schemaNameSchema.safeParse('pg_temp').success).toBe(false);
    });
  });

  describe('Column Value Validation', () => {
    test('should accept valid column values', () => {
      const validValues = [
        'text value',
        42,
        3.14,
        true,
        false,
        null,
        { key: 'value' },
        [1, 2, 3],
        { nested: { object: true } },
      ];

      validValues.forEach(value => {
        const result = columnValueSchema.safeParse(value);
        expect(result.success).toBe(true);
      });
    });

    test('should reject oversized values', () => {
      // String too large (> 1MB)
      const largeString = 'a'.repeat(1048577);
      expect(columnValueSchema.safeParse(largeString).success).toBe(false);

      // Array too large (> 10000 items)
      const largeArray = Array(10001).fill('item');
      expect(columnValueSchema.safeParse(largeArray).success).toBe(false);

      // Infinite number
      expect(columnValueSchema.safeParse(Number.POSITIVE_INFINITY).success).toBe(false);
      expect(columnValueSchema.safeParse(Number.NEGATIVE_INFINITY).success).toBe(false);
      expect(columnValueSchema.safeParse(Number.NaN).success).toBe(false);
    });

    test('should reject non-serializable values', () => {
      // Circular reference
      const circular: any = { a: 1 };
      circular.self = circular;
      expect(columnValueSchema.safeParse(circular).success).toBe(false);

      // Function (not JSON serializable)
      expect(columnValueSchema.safeParse(() => {}).success).toBe(false);
    });
  });

  describe('WHERE Clause Validation', () => {
    test('should validate simple WHERE conditions', () => {
      const validConditions = [
        { column: 'id', operator: '=', value: 1 },
        { column: 'name', operator: 'LIKE', value: '%test%' },
        { column: 'active', operator: 'IS NULL' },
        { column: 'age', operator: '>', value: 18 },
        { column: 'status', operator: 'IN', value: ['active', 'pending'] },
        { column: 'score', operator: 'BETWEEN', value: [10, 20] },
      ];

      validConditions.forEach(condition => {
        const result = whereConditionSchema.safeParse(condition);
        expect(result.success).toBe(true);
      });
    });

    test('should reject invalid WHERE conditions', () => {
      const invalidConditions = [
        { column: 'invalid-column', operator: '=', value: 1 }, // Invalid column name
        { column: 'id', operator: 'INVALID_OP', value: 1 }, // Invalid operator
        { column: 'id', operator: 'IS NULL', value: 1 }, // IS NULL with value
        { column: 'id', operator: '=', value: undefined }, // Missing value
        { column: 'id', operator: 'IN', value: 'not_array' }, // IN with non-array
        { column: 'id', operator: 'BETWEEN', value: [1] }, // BETWEEN with single value
        { column: 'id', operator: 'BETWEEN', value: [1, 2, 3] }, // BETWEEN with too many values
      ];

      invalidConditions.forEach(condition => {
        const result = whereConditionSchema.safeParse(condition);
        expect(result.success).toBe(false);
      });
    });

    test('should validate complex WHERE clauses with logical operators', () => {
      const complexConditions: WhereClause[] = [
        {
          AND: [
            { column: 'age', operator: '>', value: 18 },
            { column: 'status', operator: '=', value: 'active' },
          ],
        },
        {
          OR: [
            { column: 'role', operator: '=', value: 'admin' },
            { column: 'permissions', operator: 'LIKE', value: '%write%' },
          ],
        },
        {
          NOT: { column: 'deleted', operator: 'IS NULL' },
        },
        {
          AND: [
            { column: 'created_at', operator: '>', value: '2023-01-01' },
            {
              OR: [
                { column: 'type', operator: '=', value: 'premium' },
                { column: 'credits', operator: '>', value: 100 },
              ],
            },
          ],
        },
      ];

      complexConditions.forEach(condition => {
        const result = whereClauseSchema.safeParse(condition);
        expect(result.success).toBe(true);
      });
    });
  });

  describe('ORDER BY Validation', () => {
    test('should validate ORDER BY clauses', () => {
      const validOrderBy = [
        { column: 'name', direction: 'ASC' },
        { column: 'created_at', direction: 'DESC' },
        { column: 'score', direction: 'asc' }, // Should be normalized to uppercase
        { column: 'updated_at', direction: 'desc', nulls: 'FIRST' },
        { column: 'priority', direction: 'ASC', nulls: 'last' }, // Should be normalized
      ];

      validOrderBy.forEach(order => {
        const result = orderBySchema.safeParse(order);
        expect(result.success).toBe(true);
        if (result.success) {
          expect(['ASC', 'DESC']).toContain(result.data.direction);
          if (result.data.nulls) {
            expect(['FIRST', 'LAST']).toContain(result.data.nulls);
          }
        }
      });
    });

    test('should reject invalid ORDER BY clauses', () => {
      const invalidOrderBy = [
        { column: 'invalid-column', direction: 'ASC' }, // Invalid column name
        { column: 'name', direction: 'INVALID' }, // Invalid direction
        { column: 'name', direction: 'ASC', nulls: 'INVALID' }, // Invalid nulls position
      ];

      invalidOrderBy.forEach(order => {
        const result = orderBySchema.safeParse(order);
        expect(result.success).toBe(false);
      });
    });
  });

  describe('Pagination Validation', () => {
    test('should validate pagination parameters', () => {
      const validPagination = [
        { limit: 10, offset: 0 },
        { limit: 100 },
        { offset: 50 },
        { limit: 10000, offset: 1000000 }, // Maximum values
        {},
      ];

      validPagination.forEach(pagination => {
        const result = paginationSchema.safeParse(pagination);
        expect(result.success).toBe(true);
      });
    });

    test('should reject invalid pagination parameters', () => {
      const invalidPagination = [
        { limit: 0 }, // Zero limit
        { limit: -1 }, // Negative limit
        { limit: 10001 }, // Too large limit
        { offset: -1 }, // Negative offset
        { offset: 1000001 }, // Too large offset
        { limit: 3.14 }, // Non-integer limit
        { offset: 2.71 }, // Non-integer offset
      ];

      invalidPagination.forEach(pagination => {
        const result = paginationSchema.safeParse(pagination);
        expect(result.success).toBe(false);
      });
    });
  });

  describe('Table and Column Specifications', () => {
    test('should validate table specifications', () => {
      const validTableSpecs = [
        { schema: 'public', table: 'users' },
        { table: 'products' }, // Should default to public schema
      ];

      validTableSpecs.forEach(spec => {
        const result = tableSpecSchema.safeParse(spec);
        expect(result.success).toBe(true);
        if (result.success) {
          expect(result.data.schema).toBe('public');
        }
      });
    });

    test('should validate column selections', () => {
      const validSelections = [
        ['id'],
        ['id', 'name', 'email'],
        Array(100).fill(0).map((_, i) => `col${i}`), // Maximum 100 columns
      ];

      validSelections.forEach(selection => {
        const result = columnSelectionSchema.safeParse(selection);
        expect(result.success).toBe(true);
      });
    });

    test('should reject invalid column selections', () => {
      const invalidSelections = [
        [], // Empty selection
        ['id', 'id'], // Duplicate columns
        Array(101).fill(0).map((_, i) => `col${i}`), // Too many columns
        ['invalid-column'], // Invalid column name
      ];

      invalidSelections.forEach(selection => {
        const result = columnSelectionSchema.safeParse(selection);
        expect(result.success).toBe(false);
      });
    });
  });

  describe('Data Record Validation', () => {
    test('should validate data records', () => {
      const validRecords = [
        { id: 1, name: 'John' },
        { email: '<EMAIL>', active: true, score: 95.5 },
        Object.fromEntries(Array(100).fill(0).map((_, i) => [`col${i}`, i])), // Maximum 100 columns
      ];

      validRecords.forEach(record => {
        const result = dataRecordSchema.safeParse(record);
        expect(result.success).toBe(true);
      });
    });

    test('should reject invalid data records', () => {
      const invalidRecords = [
        {}, // Empty record
        Object.fromEntries(Array(101).fill(0).map((_, i) => [`col${i}`, i])), // Too many columns
      ];

      invalidRecords.forEach(record => {
        const result = dataRecordSchema.safeParse(record);
        expect(result.success).toBe(false);
      });
    });
  });

  describe('Conflict Columns Validation', () => {
    test('should validate conflict columns for upserts', () => {
      const validConflictColumns = [
        ['id'],
        ['email', 'username'],
        Array(10).fill(0).map((_, i) => `col${i}`), // Maximum 10 columns
      ];

      validConflictColumns.forEach(columns => {
        const result = conflictColumnsSchema.safeParse(columns);
        expect(result.success).toBe(true);
      });
    });

    test('should reject invalid conflict columns', () => {
      const invalidConflictColumns = [
        [], // Empty
        ['id', 'id'], // Duplicates
        Array(11).fill(0).map((_, i) => `col${i}`), // Too many columns
      ];

      invalidConflictColumns.forEach(columns => {
        const result = conflictColumnsSchema.safeParse(columns);
        expect(result.success).toBe(false);
      });
    });
  });

  describe('Error Formatting and Safe Parsing', () => {
    test('should format validation errors properly', () => {
      const schema = z.object({
        name: z.string().min(1),
        age: z.number().positive(),
      });

      const result = schema.safeParse({ name: '', age: -1 });
      expect(result.success).toBe(false);

      if (!result.success) {
        const formatted = formatValidationErrors(result.error);
        expect(formatted).toContain('name');
        expect(formatted).toContain('age');
        expect(typeof formatted).toBe('string');
        expect(formatted.length).toBeGreaterThan(0);
      }
    });

    test('should provide detailed error messages with context', () => {
      const testData = { invalid: 'data' };
      const result = safeParseWithDetails(
        z.object({ required: z.string() }),
        testData,
        'Test context'
      );

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error).toContain('Test context');
        expect(result.error).toContain('validation failed');
      }
    });

    test('should return successful results for valid data', () => {
      const testData = { required: 'value' };
      const result = safeParseWithDetails(
        z.object({ required: z.string() }),
        testData,
        'Test context'
      );

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data).toEqual(testData);
      }
    });
  });

  describe('Edge Cases and Security', () => {
    test('should handle malformed input gracefully', () => {
      const malformedInputs = [
        undefined,
        null,
        Symbol('test'),
        () => {},
        new Date(),
        /regex/,
      ];

      malformedInputs.forEach(input => {
        const result = postgresIdentifierSchema.safeParse(input);
        expect(result.success).toBe(false);
      });
    });

    test('should reject SQL injection attempts in identifiers', () => {
      const injectionAttempts = [
        "'; DROP TABLE users; --",
        "1' OR '1'='1",
        "admin'/*",
        "test; DELETE FROM users WHERE id = 1; --",
      ];

      injectionAttempts.forEach(attempt => {
        const result = postgresIdentifierSchema.safeParse(attempt);
        expect(result.success).toBe(false);
      });
    });

    test('should handle Unicode and special characters appropriately', () => {
      const unicodeStrings = [
        '测试', // Chinese characters
        'café', // Accented characters
        '🚀', // Emoji
        'test\n\r\t', // Control characters
        'a'.repeat(10000), // Very long string
      ];

      unicodeStrings.forEach(str => {
        // Should be rejected by identifier schema (only allows ASCII)
        expect(postgresIdentifierSchema.safeParse(str).success).toBe(false);
        
        // But should be accepted by column value schema (for data)
        expect(columnValueSchema.safeParse(str).success).toBe(true);
      });
    });
  });
});