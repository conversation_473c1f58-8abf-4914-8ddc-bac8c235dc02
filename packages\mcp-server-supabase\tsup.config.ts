import { defineConfig } from 'tsup';

export default defineConfig([
  {
    entry: ['src/index.ts', 'src/transports/stdio.ts', 'src/platform/index.ts', 'src/cli.ts'],
    format: ['cjs', 'esm'],
    outDir: 'dist',
    sourcemap: true,
    dts: true,
    minify: false,
    splitting: false,
    bundle: true,
    external: [
      '@supabase/supabase-js',
      'pino',
      'pino-pretty',
      'ws',
      'graphql',
      'ajv',
      'ajv-formats',
      'zod',
      'common-tags',
      'node-pg-migrate',
      '@modelcontextprotocol/sdk'
    ],
    loader: {
      '.sql': 'text',
    },
    esbuildOptions(options) {
      options.platform = 'node';
      options.target = 'node18';
    },
  },
]);
