#!/usr/bin/env node

import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { parseArgs } from 'node:util';
import packageJson from '../../package.json' with { type: 'json' };
import { createLocalSupabaseMcpServer } from '../server.js';

const { version } = packageJson;

async function main() {
  const {
    values: {
      ['supabase-url']: supabaseUrl,
      ['anon-key']: anonKey,
      ['service-key']: serviceKey,
      ['read-only']: readOnly,
      ['version']: showVersion,
    },
  } = parseArgs({
    options: {
      ['supabase-url']: {
        type: 'string',
      },
      ['anon-key']: {
        type: 'string',
      },
      ['service-key']: {
        type: 'string',
      },
      ['read-only']: {
        type: 'boolean',
        default: false,
      },
      ['version']: {
        type: 'boolean',
      },
    },
  });

  if (showVersion) {
    console.log(version);
    process.exit(0);
  }

  // Build configuration from CLI arguments and environment variables
  const config = {
    SUPABASE_URL: supabaseUrl || process.env.SUPABASE_URL || 'https://devdb.syncrobit.net',
    SUPABASE_ANON_KEY: anonKey || process.env.SUPABASE_ANON_KEY,
    SUPABASE_SERVICE_ROLE_KEY: serviceKey || process.env.SUPABASE_SERVICE_ROLE_KEY,
    READ_ONLY: readOnly || (process.env.READ_ONLY === 'true'),
    DEBUG_SQL: process.env.DEBUG_SQL === 'true',
  };

  // Validate required configuration
  if (!config.SUPABASE_ANON_KEY) {
    console.error(
      'Please provide the anonymous key with the --anon-key flag or set the SUPABASE_ANON_KEY environment variable'
    );
    process.exit(1);
  }

  if (!config.SUPABASE_SERVICE_ROLE_KEY) {
    console.error(
      'Please provide the service role key with the --service-key flag or set the SUPABASE_SERVICE_ROLE_KEY environment variable'
    );
    process.exit(1);
  }

  const server = await createLocalSupabaseMcpServer({
    config,
    readOnly,
  });

  const transport = new StdioServerTransport();

  try {
    await server.connect(transport);
  } catch (error) {
    // Re-throw stdio transport error with context
    throw new Error(`Stdio transport error: ${error instanceof Error ? error.message : String(error)}`);
  }
}

main().catch(console.error);
