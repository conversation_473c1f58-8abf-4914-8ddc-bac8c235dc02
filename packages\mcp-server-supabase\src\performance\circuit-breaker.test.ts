import { describe, expect, test, beforeEach, afterEach, vi } from 'vitest';
import { CircuitBreaker, CircuitBreakerRegistry } from './circuit-breaker.js';
import { ConfigManager } from '../config/config-manager.js';

describe('CircuitBreaker', () => {
  let circuitBreaker: CircuitBreaker;
  let mockConfig: ConfigManager;

  beforeEach(() => {
    mockConfig = {
      get: vi.fn((key: string, defaultValue?: any) => {
        const config: Record<string, any> = {
          CIRCUIT_FAILURE_THRESHOLD: 3,
          CIRCUIT_SUCCESS_THRESHOLD: 2,
          CIRCUIT_TIMEOUT: 5000,
          CIRCUIT_RESET_TIMEOUT: 10000,
          CIRCUIT_MONITORING_PERIOD: 60000,
          CIRCUIT_ENABLE_METRICS: true,
        };
        return config[key] ?? defaultValue;
      }),
    } as any;

    circuitBreaker = new CircuitBreaker({ name: 'test-circuit' }, mockConfig);
  });

  afterEach(() => {
    circuitBreaker.shutdown();
  });

  describe('Basic Functionality', () => {
    test('should execute successful functions', async () => {
      const successFn = vi.fn().mockResolvedValue('success');
      
      const result = await circuitBreaker.execute(successFn);
      
      expect(result).toBe('success');
      expect(successFn).toHaveBeenCalledOnce();
      expect(circuitBreaker.getState()).toBe('CLOSED');
    });

    test('should handle function failures', async () => {
      const errorFn = vi.fn().mockRejectedValue(new Error('Test error'));
      
      await expect(circuitBreaker.execute(errorFn)).rejects.toThrow('Test error');
      expect(circuitBreaker.getState()).toBe('CLOSED'); // Still closed after single failure
    });

    test('should track statistics', async () => {
      const successFn = vi.fn().mockResolvedValue('success');
      const errorFn = vi.fn().mockRejectedValue(new Error('Test error'));
      
      await circuitBreaker.execute(successFn);
      await expect(circuitBreaker.execute(errorFn)).rejects.toThrow();
      
      const stats = circuitBreaker.getStats();
      expect(stats.successes).toBe(1);
      expect(stats.failures).toBe(1);
      expect(stats.requests).toBe(2);
      expect(stats.successRate).toBe(0.5);
      expect(stats.failureRate).toBe(0.5);
    });
  });

  describe('State Transitions', () => {
    test('should transition to OPEN after failure threshold', async () => {
      const errorFn = vi.fn().mockRejectedValue(new Error('Test error'));
      
      // Fail 3 times (threshold)
      for (let i = 0; i < 3; i++) {
        await expect(circuitBreaker.execute(errorFn)).rejects.toThrow();
      }
      
      expect(circuitBreaker.getState()).toBe('OPEN');
    });

    test('should block requests when OPEN', async () => {
      const errorFn = vi.fn().mockRejectedValue(new Error('Test error'));
      const successFn = vi.fn().mockResolvedValue('success');
      
      // Trigger circuit to open
      for (let i = 0; i < 3; i++) {
        await expect(circuitBreaker.execute(errorFn)).rejects.toThrow();
      }
      
      expect(circuitBreaker.getState()).toBe('OPEN');
      
      // Should block subsequent requests
      await expect(circuitBreaker.execute(successFn)).rejects.toThrow(/Circuit breaker is OPEN/);
      expect(successFn).not.toHaveBeenCalled();
    });

    test('should transition to HALF_OPEN after reset timeout', async () => {
      // Use shorter timeout for testing
      const fastCircuit = new CircuitBreaker({ 
        name: 'fast-circuit',
        failureThreshold: 2,
        resetTimeout: 100,
      });
      
      try {
        const errorFn = vi.fn().mockRejectedValue(new Error('Test error'));
        
        // Trigger circuit to open
        for (let i = 0; i < 2; i++) {
          await expect(fastCircuit.execute(errorFn)).rejects.toThrow();
        }
        
        expect(fastCircuit.getState()).toBe('OPEN');
        
        // Wait for reset timeout
        await new Promise(resolve => setTimeout(resolve, 150));
        
        // Next request should transition to HALF_OPEN
        const successFn = vi.fn().mockResolvedValue('success');
        await fastCircuit.execute(successFn);
        
        expect(fastCircuit.getState()).toBe('CLOSED'); // Should close after success
      } finally {
        fastCircuit.shutdown();
      }
    });

    test('should transition from HALF_OPEN to CLOSED after success threshold', async () => {
      const fastCircuit = new CircuitBreaker({ 
        name: 'success-circuit',
        failureThreshold: 2,
        successThreshold: 2,
        resetTimeout: 100,
      });
      
      try {
        const errorFn = vi.fn().mockRejectedValue(new Error('Test error'));
        const successFn = vi.fn().mockResolvedValue('success');
        
        // Open the circuit
        for (let i = 0; i < 2; i++) {
          await expect(fastCircuit.execute(errorFn)).rejects.toThrow();
        }
        
        // Wait for reset
        await new Promise(resolve => setTimeout(resolve, 150));
        
        // Execute successful requests to close circuit
        await fastCircuit.execute(successFn);
        expect(fastCircuit.getState()).toBe('HALF_OPEN');
        
        await fastCircuit.execute(successFn);
        expect(fastCircuit.getState()).toBe('CLOSED');
      } finally {
        fastCircuit.shutdown();
      }
    });
  });

  describe('Timeout Handling', () => {
    test('should timeout long-running functions', async () => {
      const slowFn = vi.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(resolve, 10000))
      );
      
      const timeoutCircuit = new CircuitBreaker({ 
        name: 'timeout-circuit',
        timeout: 100,
      });
      
      try {
        await expect(timeoutCircuit.execute(slowFn)).rejects.toThrow(/timeout/);
      } finally {
        timeoutCircuit.shutdown();
      }
    });
  });

  describe('Fallback Execution', () => {
    test('should execute fallback when circuit is open', async () => {
      const errorFn = vi.fn().mockRejectedValue(new Error('Test error'));
      const fallbackFn = vi.fn().mockReturnValue('fallback-result');
      
      // Open the circuit
      for (let i = 0; i < 3; i++) {
        await expect(circuitBreaker.execute(errorFn)).rejects.toThrow();
      }
      
      const result = await circuitBreaker.executeWithFallback(errorFn, fallbackFn);
      
      expect(result).toBe('fallback-result');
      expect(fallbackFn).toHaveBeenCalledOnce();
    });

    test('should not execute fallback when circuit is closed', async () => {
      const successFn = vi.fn().mockResolvedValue('success');
      const fallbackFn = vi.fn().mockReturnValue('fallback-result');
      
      const result = await circuitBreaker.executeWithFallback(successFn, fallbackFn);
      
      expect(result).toBe('success');
      expect(fallbackFn).not.toHaveBeenCalled();
    });
  });

  describe('Manual Control', () => {
    test('should allow manual reset', async () => {
      const errorFn = vi.fn().mockRejectedValue(new Error('Test error'));
      
      // Open the circuit
      for (let i = 0; i < 3; i++) {
        await expect(circuitBreaker.execute(errorFn)).rejects.toThrow();
      }
      
      expect(circuitBreaker.getState()).toBe('OPEN');
      
      // Manual reset
      circuitBreaker.reset();
      
      expect(circuitBreaker.getState()).toBe('CLOSED');
      
      const stats = circuitBreaker.getStats();
      expect(stats.failures).toBe(0);
      expect(stats.successes).toBe(0);
    });
  });

  describe('Event Emission', () => {
    test('should emit events for state changes', async () => {
      const stateChangeHandler = vi.fn();
      const successHandler = vi.fn();
      const failureHandler = vi.fn();
      
      circuitBreaker.on('stateChange', stateChangeHandler);
      circuitBreaker.on('success', successHandler);
      circuitBreaker.on('failure', failureHandler);
      
      const errorFn = vi.fn().mockRejectedValue(new Error('Test error'));
      const successFn = vi.fn().mockResolvedValue('success');
      
      await circuitBreaker.execute(successFn);
      await expect(circuitBreaker.execute(errorFn)).rejects.toThrow();
      
      expect(successHandler).toHaveBeenCalled();
      expect(failureHandler).toHaveBeenCalled();
    });
  });
});

describe('CircuitBreakerRegistry', () => {
  afterEach(() => {
    CircuitBreakerRegistry.shutdown();
  });

  test('should create and manage circuit breakers', () => {
    const breaker1 = CircuitBreakerRegistry.getOrCreate('service1');
    const breaker2 = CircuitBreakerRegistry.getOrCreate('service2');
    
    expect(breaker1).toBeDefined();
    expect(breaker2).toBeDefined();
    expect(breaker1).not.toBe(breaker2);
    
    // Should return same instance for same name
    const breaker1Again = CircuitBreakerRegistry.getOrCreate('service1');
    expect(breaker1Again).toBe(breaker1);
  });

  test('should get circuit breaker by name', () => {
    CircuitBreakerRegistry.getOrCreate('test-service');
    
    const breaker = CircuitBreakerRegistry.get('test-service');
    expect(breaker).toBeDefined();
    
    const nonExistent = CircuitBreakerRegistry.get('non-existent');
    expect(nonExistent).toBeUndefined();
  });

  test('should get all circuit breakers', () => {
    CircuitBreakerRegistry.getOrCreate('service1');
    CircuitBreakerRegistry.getOrCreate('service2');
    
    const allBreakers = CircuitBreakerRegistry.getAll();
    expect(allBreakers.size).toBe(2);
    expect(allBreakers.has('service1')).toBe(true);
    expect(allBreakers.has('service2')).toBe(true);
  });

  test('should get statistics for all circuit breakers', async () => {
    const breaker1 = CircuitBreakerRegistry.getOrCreate('stats-service1');
    const breaker2 = CircuitBreakerRegistry.getOrCreate('stats-service2');
    
    // Execute some operations
    await breaker1.execute(() => Promise.resolve('success'));
    await expect(breaker2.execute(() => Promise.reject(new Error('error')))).rejects.toThrow();
    
    const allStats = CircuitBreakerRegistry.getAllStats();
    
    expect(allStats['stats-service1']).toBeDefined();
    expect(allStats['stats-service2']).toBeDefined();
    expect(allStats['stats-service1'].successes).toBe(1);
    expect(allStats['stats-service2'].failures).toBe(1);
  });

  test('should remove circuit breakers', () => {
    CircuitBreakerRegistry.getOrCreate('removable-service');
    
    expect(CircuitBreakerRegistry.get('removable-service')).toBeDefined();
    
    const removed = CircuitBreakerRegistry.remove('removable-service');
    expect(removed).toBe(true);
    expect(CircuitBreakerRegistry.get('removable-service')).toBeUndefined();
    
    const removedAgain = CircuitBreakerRegistry.remove('removable-service');
    expect(removedAgain).toBe(false);
  });

  test('should shutdown all circuit breakers', () => {
    CircuitBreakerRegistry.getOrCreate('shutdown-service1');
    CircuitBreakerRegistry.getOrCreate('shutdown-service2');
    
    expect(CircuitBreakerRegistry.getAll().size).toBe(2);
    
    CircuitBreakerRegistry.shutdown();
    
    expect(CircuitBreakerRegistry.getAll().size).toBe(0);
  });
});
