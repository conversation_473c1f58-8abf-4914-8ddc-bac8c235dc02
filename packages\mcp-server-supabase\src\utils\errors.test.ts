import { describe, expect, test, beforeEach } from 'vitest';
import {
  ErrorCode,
  McpServerError,
  ProtocolError,
  ValidationError,
  DatabaseError,
  ToolError,
  ConfigurationError,
  createErrorResponse,
  enumerateError,
} from './errors.js';

describe('Error Classes', () => {
  test('McpServerError should create error with all properties', () => {
    const requestId = 'test-request-id';
    const context = { userId: 'test-user' };
    const error = new McpServerError(
      ErrorCode.INTERNAL_ERROR,
      'Test error message',
      500,
      { requestId, context }
    );

    expect(error.name).toBe('McpServerError');
    expect(error.code).toBe(ErrorCode.INTERNAL_ERROR);
    expect(error.message).toBe('Test error message');
    expect(error.statusCode).toBe(500);
    expect(error.requestId).toBe(requestId);
    expect(error.context).toEqual(context);
    expect(error.timestamp).toBeInstanceOf(Date);
  });

  test('ProtocolError should extend McpServerError', () => {
    const error = new ProtocolError('Protocol error');
    
    expect(error).toBeInstanceOf(McpServerError);
    expect(error.name).toBe('ProtocolError');
    expect(error.code).toBe(ErrorCode.PROTOCOL_ERROR);
    expect(error.statusCode).toBe(400);
  });

  test('ValidationError should extend McpServerError', () => {
    const error = new ValidationError('Validation failed');
    
    expect(error).toBeInstanceOf(McpServerError);
    expect(error.name).toBe('ValidationError');
    expect(error.code).toBe(ErrorCode.VALIDATION_ERROR);
    expect(error.statusCode).toBe(400);
  });

  test('DatabaseError should extend McpServerError', () => {
    const error = new DatabaseError('Database connection failed');
    
    expect(error).toBeInstanceOf(McpServerError);
    expect(error.name).toBe('DatabaseError');
    expect(error.code).toBe(ErrorCode.DATABASE_ERROR);
    expect(error.statusCode).toBe(500);
  });

  test('ToolError should extend McpServerError', () => {
    const error = new ToolError('Tool execution failed');
    
    expect(error).toBeInstanceOf(McpServerError);
    expect(error.name).toBe('ToolError');
    expect(error.code).toBe(ErrorCode.TOOL_EXECUTION_ERROR);
    expect(error.statusCode).toBe(500);
  });

  test('ConfigurationError should extend McpServerError', () => {
    const error = new ConfigurationError('Invalid configuration');
    
    expect(error).toBeInstanceOf(McpServerError);
    expect(error.name).toBe('ConfigurationError');
    expect(error.code).toBe(ErrorCode.CONFIG_ERROR);
    expect(error.statusCode).toBe(500);
  });

  test('McpServerError.toJSON should return serializable object', () => {
    const error = new McpServerError(
      ErrorCode.INTERNAL_ERROR,
      'Test error',
      500,
      { requestId: 'test-id', context: { test: true } }
    );

    const json = error.toJSON();
    
    expect(json).toHaveProperty('name', 'McpServerError');
    expect(json).toHaveProperty('code', ErrorCode.INTERNAL_ERROR);
    expect(json).toHaveProperty('message', 'Test error');
    expect(json).toHaveProperty('statusCode', 500);
    expect(json).toHaveProperty('requestId', 'test-id');
    expect(json).toHaveProperty('context', { test: true });
    expect(json).toHaveProperty('timestamp');
    expect(json).toHaveProperty('stack');
  });
});

describe('Error Response Creation', () => {
  test('createErrorResponse should handle McpServerError', () => {
    const error = new McpServerError(
      ErrorCode.VALIDATION_ERROR,
      'Validation failed',
      400,
      { requestId: 'test-id' }
    );

    const response = createErrorResponse(error);
    
    expect(response.isError).toBe(true);
    expect(response.content).toHaveLength(1);
    expect(response.content[0].type).toBe('text');
    
    const errorData = JSON.parse(response.content[0].text);
    expect(errorData.error.code).toBe(ErrorCode.VALIDATION_ERROR);
    expect(errorData.error.message).toBe('Validation failed');
    expect(errorData.error.requestId).toBe('test-id');
  });

  test('createErrorResponse should handle unknown errors', () => {
    const error = new Error('Unknown error');
    const requestId = 'test-request-id';

    const response = createErrorResponse(error, requestId);
    
    expect(response.isError).toBe(true);
    expect(response.content).toHaveLength(1);
    
    const errorData = JSON.parse(response.content[0].text);
    expect(errorData.error.code).toBe(ErrorCode.INTERNAL_ERROR);
    expect(errorData.error.message).toBe('Unknown error');
    expect(errorData.error.requestId).toBe(requestId);
  });

  test('createErrorResponse should handle non-Error objects', () => {
    const error = 'String error';
    const requestId = 'test-request-id';

    const response = createErrorResponse(error, requestId);
    
    expect(response.isError).toBe(true);
    
    const errorData = JSON.parse(response.content[0].text);
    expect(errorData.error.code).toBe(ErrorCode.INTERNAL_ERROR);
    expect(errorData.error.message).toBe('String error');
    expect(errorData.error.requestId).toBe(requestId);
  });
});

describe('Error Enumeration', () => {
  test('enumerateError should handle McpServerError', () => {
    const error = new McpServerError(
      ErrorCode.DATABASE_ERROR,
      'Database error',
      500,
      { requestId: 'test-id', context: { table: 'users' } }
    );

    const enumerated = enumerateError(error);
    
    expect(enumerated).toHaveProperty('name', 'McpServerError');
    expect(enumerated).toHaveProperty('code', ErrorCode.DATABASE_ERROR);
    expect(enumerated).toHaveProperty('message', 'Database error');
    expect(enumerated).toHaveProperty('statusCode', 500);
    expect(enumerated).toHaveProperty('requestId', 'test-id');
    expect(enumerated).toHaveProperty('context', { table: 'users' });
    expect(enumerated).toHaveProperty('timestamp');
  });

  test('enumerateError should handle regular Error', () => {
    const error = new Error('Regular error');
    const requestId = 'test-request-id';

    const enumerated = enumerateError(error, requestId);
    
    expect(enumerated).toHaveProperty('name', 'Error');
    expect(enumerated).toHaveProperty('message', 'Regular error');
    expect(enumerated).toHaveProperty('requestId', requestId);
    expect(enumerated).toHaveProperty('timestamp');
  });

  test('enumerateError should handle primitive values', () => {
    const error = 'String error';
    const requestId = 'test-request-id';

    const enumerated = enumerateError(error, requestId);
    
    expect(enumerated).toHaveProperty('error', 'String error');
    expect(enumerated).toHaveProperty('requestId', requestId);
    expect(enumerated).toHaveProperty('timestamp');
  });

  test('enumerateError should handle null/undefined', () => {
    const enumerated = enumerateError(null);
    expect(enumerated).toBeNull();

    const enumerated2 = enumerateError(undefined);
    expect(enumerated2).toBeUndefined();
  });
});

describe('Error Codes', () => {
  test('should have all expected error codes', () => {
    const expectedCodes = [
      'PROTOCOL_ERROR',
      'INVALID_REQUEST',
      'METHOD_NOT_FOUND',
      'VALIDATION_ERROR',
      'SCHEMA_VALIDATION_ERROR',
      'PARAMETER_ERROR',
      'DATABASE_ERROR',
      'CONNECTION_ERROR',
      'QUERY_ERROR',
      'TOOL_NOT_FOUND',
      'TOOL_EXECUTION_ERROR',
      'TOOL_TIMEOUT',
      'CONFIG_ERROR',
      'MISSING_CREDENTIALS',
      'INTERNAL_ERROR',
      'SERVICE_UNAVAILABLE',
      'RATE_LIMIT_EXCEEDED',
    ];

    for (const code of expectedCodes) {
      expect(ErrorCode).toHaveProperty(code);
    }
  });
});
