#!/usr/bin/env python3
"""
Generate proper JWT tokens for Supabase MCP server
Usage: python generate_jwt_tokens.py <your_jwt_secret>
"""

import jwt
import sys
import time

def generate_supabase_tokens(jwt_secret):
    """Generate anon and service_role JWT tokens for Supabase"""
    
    # Current time and expiration (10 years from now)
    current_time = int(time.time())
    expiration_time = current_time + (10 * 365 * 24 * 60 * 60)  # 10 years
    
    # Anon key payload
    anon_payload = {
        "role": "anon",
        "iss": "supabase",
        "iat": current_time,
        "exp": expiration_time
    }
    
    # Service role key payload  
    service_role_payload = {
        "role": "service_role",
        "iss": "supabase", 
        "iat": current_time,
        "exp": expiration_time
    }
    
    # Generate tokens
    anon_token = jwt.encode(anon_payload, jwt_secret, algorithm="HS256")
    service_role_token = jwt.encode(service_role_payload, jwt_secret, algorithm="HS256")
    
    return anon_token, service_role_token

def main():
    if len(sys.argv) != 2:
        print("Usage: python generate_jwt_tokens.py <your_jwt_secret>")
        print("\nExample:")
        print("python generate_jwt_tokens.py 'your-super-secret-jwt-secret-here'")
        sys.exit(1)
    
    jwt_secret = sys.argv[1]
    
    try:
        anon_token, service_role_token = generate_supabase_tokens(jwt_secret)
        
        print("=" * 80)
        print("SUPABASE JWT TOKENS GENERATED SUCCESSFULLY")
        print("=" * 80)
        print()
        print("ANON KEY:")
        print(anon_token)
        print()
        print("SERVICE ROLE KEY:")
        print(service_role_token)
        print()
        print("=" * 80)
        print("UPDATE YOUR CLAUDE DESKTOP CONFIG WITH THESE NEW TOKENS")
        print("=" * 80)
        
    except Exception as e:
        print(f"Error generating tokens: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()