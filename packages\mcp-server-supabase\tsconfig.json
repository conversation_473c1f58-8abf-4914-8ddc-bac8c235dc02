{"extends": "@total-typescript/tsconfig/tsc/dom/library", "compilerOptions": {"moduleResolution": "NodeNext", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "resolveJsonModule": true, "baseUrl": ".", "outDir": "./dist", "target": "ES2020", "downlevelIteration": true, "paths": {"@/*": ["./src/*"]}}, "include": ["src/**/*.ts", "test/**/*.ts"], "exclude": ["dist", "node_modules", "**/*.js"]}