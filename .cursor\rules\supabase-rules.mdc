---
description: 
globs: 
alwaysApply: true
---
## Supabase Rules
- The actual supabase instance is running at http://*************:8000
- It can also be reached via Cloudflare Tunnel: https://devdb.syncrobit.net
- This MCP server is designed to connect to that supabase instance only.
- Strictly DO NOT create or install any supabase instance locally in localhost.
- This MCP server shall be installed in windows environment via installing the npm package locally for Cursor IDE and Claude <PERSON>op

- This MCP server shall be also installed in ubuntu linux environment for Cursor IDE only.
- The MCP config path for windows machine, Cursor IDE is here: C:\Users\<USER>\.cursor\mcp.json