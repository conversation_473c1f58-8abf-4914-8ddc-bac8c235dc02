import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { getLocalMonitoringTools } from '../src/tools/local-monitoring-tools.js';
import { createLocalSupabasePlatform } from '../src/platform/local-platform.js';
import { getLocalConfig } from '../src/config/index.js';
import type { SupabasePlatform } from '../src/platform/types.js';

describe('Local Monitoring Tools Integration Tests', () => {
  let platform: SupabasePlatform;
  let tools: ReturnType<typeof getLocalMonitoringTools>;
  const projectId = 'local-test';

  beforeAll(async () => {
    // Create platform with test configuration
    const config = getLocalConfig();
    platform = createLocalSupabasePlatform(config);
    
    // Initialize monitoring tools
    tools = getLocalMonitoringTools({ platform, projectId });
  });

  afterAll(async () => {
    // Cleanup if needed
  });

  describe('get_logs tool', () => {
    it('should be defined with correct structure', () => {
      expect(tools.get_logs).toBeDefined();
      expect(tools.get_logs.description).toBeDefined();
      expect(tools.get_logs.parameters).toBeDefined();
      expect(tools.get_logs.execute).toBeTypeOf('function');
    });

    it('should handle Docker not available gracefully', async () => {
      // This test will pass if Docker is not available, which is expected in some CI environments
      const result = await tools.get_logs.execute({
        service: 'db',
        lines: 10,
        level: 'all'
      });

      // Should either return logs or a Docker error
      expect(result).toBeDefined();
      if ('error' in result) {
        expect(result.error).toContain('Docker');
        expect(result.troubleshooting).toBeInstanceOf(Array);
      } else {
        expect(result.logs).toBeInstanceOf(Array);
        expect(result.service).toBe('db');
      }
    });

    it('should validate service parameter', async () => {
      const result = await tools.get_logs.execute({
        service: 'invalid' as any,
        lines: 10,
        level: 'all'
      });

      // Should handle invalid service gracefully
      expect(result).toBeDefined();
    });

    it('should respect lines parameter limit', async () => {
      const result = await tools.get_logs.execute({
        service: 'all',
        lines: 5,
        level: 'all'
      });

      expect(result).toBeDefined();
      if ('logs' in result && result.logs) {
        expect(result.logs.length).toBeLessThanOrEqual(5);
      }
    });
  });

  describe('get_advisors tool', () => {
    it('should be defined with correct structure', () => {
      expect(tools.get_advisors).toBeDefined();
      expect(tools.get_advisors.description).toBeDefined();
      expect(tools.get_advisors.parameters).toBeDefined();
      expect(tools.get_advisors.execute).toBeTypeOf('function');
    });

    it('should return advisory results for all categories', async () => {
      const result = await tools.get_advisors.execute({
        categories: ['all'],
        includeDetails: true
      });

      expect(result).toBeDefined();
      
      if ('error' in result) {
        // If there's an error, it should have troubleshooting
        expect(result.troubleshooting).toBeInstanceOf(Array);
      } else {
        // If successful, should have the expected structure
        expect(result.advisors).toBeInstanceOf(Array);
        expect(result.summary).toBeDefined();
        expect(result.summary.totalIssues).toBeTypeOf('number');
        expect(result.metadata).toBeDefined();
        if (result.metadata) {
          expect(result.metadata.checkedAt).toBeDefined();
        }
      }
    });

    it('should handle specific categories', async () => {
      const result = await tools.get_advisors.execute({
        categories: ['security'],
        includeDetails: false
      });

      expect(result).toBeDefined();
      
      if ('advisors' in result && result.advisors) {
        // Should only include security-related advisors if any
        result.advisors.forEach(advisor => {
          expect(['security', 'performance', 'maintenance']).toContain(advisor.category);
        });
      }
    });

    it('should provide helpful response when no issues found', async () => {
      const result = await tools.get_advisors.execute({
        categories: ['maintenance'],
        includeDetails: true
      });

      expect(result).toBeDefined();
      
      if ('advisors' in result && result.advisors && result.advisors.length === 0) {
        expect(result.message).toContain('No critical issues');
        expect(result.limitations).toBeInstanceOf(Array);
        expect(result.nextSteps).toBeInstanceOf(Array);
      }
    });
  });

  describe('get_docker_stats tool', () => {
    it('should be defined with correct structure', () => {
      expect(tools.get_docker_stats).toBeDefined();
      expect(tools.get_docker_stats.description).toBeDefined();
      expect(tools.get_docker_stats.parameters).toBeDefined();
      expect(tools.get_docker_stats.execute).toBeTypeOf('function');
    });

    it('should handle Docker not available gracefully', async () => {
      const result = await tools.get_docker_stats.execute({
        format: 'table'
      });

      expect(result).toBeDefined();
      
      if ('error' in result) {
        expect(result.error).toContain('Docker');
        expect(result.troubleshooting).toBeInstanceOf(Array);
      } else {
        expect(result.stats).toBeDefined();
        expect(result.timestamp).toBeDefined();
        expect(result.format).toBe('table');
      }
    });

    it('should support both table and json formats', async () => {
      const tableResult = await tools.get_docker_stats.execute({
        format: 'table'
      });
      
      const jsonResult = await tools.get_docker_stats.execute({
        format: 'json'
      });

      expect(tableResult).toBeDefined();
      expect(jsonResult).toBeDefined();

      if ('format' in tableResult) {
        expect(tableResult.format).toBe('table');
      }
      
      if ('format' in jsonResult) {
        expect(jsonResult.format).toBe('json');
      }
    });
  });

  describe('Integration Tests', () => {
    it('should have all expected tools', () => {
      const expectedTools = ['get_logs', 'get_advisors', 'get_docker_stats'];
      
      for (const toolName of expectedTools) {
        expect(tools[toolName as keyof typeof tools]).toBeDefined();
      }
    });

    it('should handle database connection issues gracefully', async () => {
      // Test that advisory tools handle connection issues properly
      const result = await tools.get_advisors.execute({
        categories: ['security'],
        includeDetails: true
      });

      expect(result).toBeDefined();
      
      // Should either succeed or fail gracefully with helpful error
      if ('error' in result) {
        expect(result.message).toBeDefined();
        expect(result.troubleshooting).toBeInstanceOf(Array);
      }
    });

    it('should provide meaningful error messages', async () => {
      // Test various error scenarios
      const tools_with_error = getLocalMonitoringTools({ 
        platform: {
          ...platform,
          executeSql: async () => {
            throw new Error('Test database error');
          }
        } as any, 
        projectId 
      });

      const result = await tools_with_error.get_advisors.execute({
        categories: ['all'],
        includeDetails: true
      });

      expect(result).toBeDefined();
      
      if ('error' in result) {
        expect(result.error).toBeDefined();
        expect(result.message).toContain('Test database error');
        expect(result.troubleshooting).toBeInstanceOf(Array);
      }
    });
  });

  describe('Tool Compliance', () => {
    it('should have proper parameter validation', () => {
      // Check that tools have proper zod schemas
      expect(tools.get_logs.parameters).toBeDefined();
      expect(tools.get_advisors.parameters).toBeDefined();
      expect(tools.get_docker_stats.parameters).toBeDefined();

      // Test parameter parsing
      const logsSchema = tools.get_logs.parameters;
      const validLogsParams = logsSchema.parse({
        service: 'db',
        lines: 50,
        level: 'error'
      });
      
      expect(validLogsParams.service).toBe('db');
      expect(validLogsParams.lines).toBe(50);
      expect(validLogsParams.level).toBe('error');
    });

    it('should handle invalid parameters gracefully', () => {
      const logsSchema = tools.get_logs.parameters;
      
      // Test invalid service
      expect(() => logsSchema.parse({
        service: 'invalid-service'
      })).toThrow();

      // Test invalid lines count
      expect(() => logsSchema.parse({
        service: 'db',
        lines: -1
      })).toThrow();
    });
  });
}); 