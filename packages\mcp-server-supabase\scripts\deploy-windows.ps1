# Windows Deployment Script for Local Supabase MCP Server
param(
    [string]$SupabaseUrl = "https://devdb.syncrobit.net",
    [string]$AnonKey,
    [string]$ServiceKey,
    [switch]$ReadOnly = $false,
    [string]$InstallPath = "$env:USERPROFILE\.local-supabase-mcp",
    [switch]$ConfigureCursor = $true,
    [switch]$ConfigureClaude = $false
)

Write-Host "🚀 Deploying Local Supabase MCP Server for Windows..." -ForegroundColor Green

# Validate required parameters
if (-not $AnonKey -or -not $ServiceKey) {
    Write-Host "❌ Please provide both -AnonKey and -ServiceKey parameters" -ForegroundColor Red
    Write-Host ""
    Write-Host "Usage example:" -ForegroundColor Yellow
    Write-Host ".\deploy-windows.ps1 -AnonKey 'your_anon_key' -ServiceKey 'your_service_key'" -ForegroundColor White
    exit 1
}

# Create installation directory
Write-Host "📁 Creating installation directory: $InstallPath" -ForegroundColor Cyan
if (-not (Test-Path $InstallPath)) {
    New-Item -ItemType Directory -Path $InstallPath -Force | Out-Null
}

# Build the project
Write-Host "🔨 Building MCP server..." -ForegroundColor Cyan
try {
    Push-Location
    Set-Location (Split-Path -Parent $PSScriptRoot)
    npm run build
    if ($LASTEXITCODE -ne 0) {
        throw "Build failed with exit code $LASTEXITCODE"
    }
} catch {
    Write-Host "❌ Build failed: $_" -ForegroundColor Red
    exit 1
} finally {
    Pop-Location
}

# Copy built files
Write-Host "📦 Copying files to installation directory..." -ForegroundColor Cyan
try {
    $sourcePath = Join-Path (Split-Path -Parent $PSScriptRoot) "dist"
    $destPath = Join-Path $InstallPath "dist"
    
    if (Test-Path $destPath) {
        Remove-Item $destPath -Recurse -Force
    }
    
    Copy-Item -Path $sourcePath -Destination $destPath -Recurse -Force
} catch {
    Write-Host "❌ Failed to copy files: $_" -ForegroundColor Red
    exit 1
}

# Create startup script
$startupScript = @"
@echo off
echo Starting Local Supabase MCP Server...
node "$InstallPath\dist\transports\stdio.cjs" %*
"@

$startupScriptPath = Join-Path $InstallPath "start-mcp-server.bat"
$startupScript | Set-Content $startupScriptPath# Create Cursor IDE configuration
if ($ConfigureCursor) {
    $cursorConfig = @{
        mcpServers = @{
            "supabase-local" = @{
                command = "node"
                args = @("$InstallPath\dist\transports\stdio.cjs")
                env = @{
                    SUPABASE_URL = $SupabaseUrl
                    SUPABASE_ANON_KEY = $AnonKey
                    SUPABASE_SERVICE_ROLE_KEY = $ServiceKey
                    READ_ONLY = $ReadOnly.ToString().ToLower()
                    DEBUG_SQL = "false"
                }
            }
        }
    }

    $cursorConfigPath = Join-Path $InstallPath "cursor-config.json"
    $cursorConfig | ConvertTo-Json -Depth 5 | Set-Content $cursorConfigPath
    Write-Host "✅ Cursor IDE configuration created: $cursorConfigPath" -ForegroundColor Green
}

# Create Claude Desktop configuration
if ($ConfigureClaude) {
    $claudeConfig = @{
        '$schema' = "https://modelcontextprotocol.io/schemas/claude-desktop-config.json"
        mcpServers = @{
            "supabase-local" = @{
                command = "node"
                args = @("$InstallPath\dist\transports\stdio.cjs")
                env = @{
                    SUPABASE_URL = $SupabaseUrl
                    SUPABASE_ANON_KEY = $AnonKey
                    SUPABASE_SERVICE_ROLE_KEY = $ServiceKey
                    READ_ONLY = $ReadOnly.ToString().ToLower()
                    DEBUG_SQL = "false"
                }
            }
        }
    }

    $claudeConfigPath = Join-Path $InstallPath "claude-desktop-config.json"
    $claudeConfig | ConvertTo-Json -Depth 5 | Set-Content $claudeConfigPath
    
    # Try to copy to Claude Desktop config location
    $claudeAppDataPath = Join-Path $env:APPDATA "Claude\claude_desktop_config.json"
    if (Test-Path (Split-Path $claudeAppDataPath)) {
        try {
            Copy-Item $claudeConfigPath $claudeAppDataPath -Force
            Write-Host "✅ Claude Desktop configuration installed: $claudeAppDataPath" -ForegroundColor Green
        } catch {
            Write-Host "⚠️  Could not automatically install Claude Desktop config. Manual copy required." -ForegroundColor Yellow
            Write-Host "   Source: $claudeConfigPath" -ForegroundColor White
            Write-Host "   Target: $claudeAppDataPath" -ForegroundColor White
        }
    }
}

Write-Host ""
Write-Host "✅ Deployment completed successfully!" -ForegroundColor Green
Write-Host "📁 Installation path: $InstallPath" -ForegroundColor Yellow
Write-Host ""

if ($ConfigureCursor) {
    Write-Host "🔧 Next steps for Cursor IDE:" -ForegroundColor Cyan
    Write-Host "1. Open Cursor IDE settings (Ctrl+,)" -ForegroundColor White
    Write-Host "2. Go to 'Extensions' > 'MCP Servers'" -ForegroundColor White
    Write-Host "3. Add the configuration from: $InstallPath\cursor-config.json" -ForegroundColor White
    Write-Host "4. Restart Cursor IDE" -ForegroundColor White
    Write-Host ""
}

Write-Host "🧪 Test the installation:" -ForegroundColor Cyan
Write-Host "node `"$InstallPath\dist\transports\stdio.cjs`" --version" -ForegroundColor White