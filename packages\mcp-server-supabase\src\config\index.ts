/**
 * Configuration exports
 */

// Legacy configuration (for backward compatibility)
export * from './local-config.js';

// Enhanced configuration management
export * from './config-manager.js';
export * from './config-factory.js';
export * from './config-utils.js';

// Re-export commonly used types and utilities
export type {
  ConfigProfile,
  ConfigSource,
  ConfigValue,
  ConfigChangeEvent,
  ConfigValidationResult,
  FeatureFlag,
} from './config-manager.js';

export type {
  ConfigFactoryOptions,
} from './config-factory.js';

export type {
  ConfigComparison,
  ConfigBackup,
} from './config-utils.js';

export {
  CONFIG_PRESETS,
  ConfigFactory,
  createDefaultConfig,
  getDefaultConfig,
  createConfigFromEnv,
} from './config-factory.js';

export {
  ConfigUtils,
  Config,
  ConfigWatcher,
} from './config-utils.js';

export {
  ConfigHealthMonitor,
  configHealthMonitor,
  startConfigHealthMonitoring,
  getConfigHealth,
  getConfigHealthSummary,
} from './config-health.js';

export type {
  ConfigHealthStatus,
} from './config-health.js';
