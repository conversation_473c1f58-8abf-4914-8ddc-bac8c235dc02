import { describe, expect, test, beforeEach, afterEach, vi } from 'vitest';
import { CacheManager } from './cache-manager.js';
import { ConfigManager } from '../config/config-manager.js';

describe('CacheManager', () => {
  let cacheManager: CacheManager;
  let mockConfig: ConfigManager;

  beforeEach(() => {
    mockConfig = {
      get: vi.fn((key: string, defaultValue?: any) => {
        const config: Record<string, any> = {
          CACHE_TTL: 300000,
          CACHE_MAX_MEMORY: 100 * 1024 * 1024,
          CACHE_MAX_ENTRIES: 10000,
          CACHE_CLEANUP_INTERVAL: 60000,
          CACHE_ENABLE_METRICS: true,
          CACHE_ENABLE_COMPRESSION: false,
          CACHE_COMPRESSION_THRESHOLD: 1024,
        };
        return config[key] ?? defaultValue;
      }),
    } as any;

    cacheManager = new CacheManager({}, mockConfig);
  });

  afterEach(async () => {
    await cacheManager.shutdown();
  });

  describe('Basic Operations', () => {
    test('should set and get values', async () => {
      await cacheManager.set('test-key', 'test-value');
      const value = await cacheManager.get('test-key');
      expect(value).toBe('test-value');
    });

    test('should return undefined for non-existent keys', async () => {
      const value = await cacheManager.get('non-existent');
      expect(value).toBeUndefined();
    });

    test('should delete values', async () => {
      await cacheManager.set('delete-key', 'delete-value');
      const deleted = await cacheManager.delete('delete-key');
      expect(deleted).toBe(true);
      
      const value = await cacheManager.get('delete-key');
      expect(value).toBeUndefined();
    });

    test('should clear all values', async () => {
      await cacheManager.set('key1', 'value1');
      await cacheManager.set('key2', 'value2');
      
      await cacheManager.clear();
      
      const value1 = await cacheManager.get('key1');
      const value2 = await cacheManager.get('key2');
      expect(value1).toBeUndefined();
      expect(value2).toBeUndefined();
    });
  });

  describe('TTL and Expiration', () => {
    test('should expire entries after TTL', async () => {
      await cacheManager.set('ttl-key', 'ttl-value', { ttl: 100 });
      
      // Should be available immediately
      let value = await cacheManager.get('ttl-key');
      expect(value).toBe('ttl-value');
      
      // Wait for expiration
      await new Promise(resolve => setTimeout(resolve, 150));
      
      value = await cacheManager.get('ttl-key');
      expect(value).toBeUndefined();
    });

    test('should use default TTL when not specified', async () => {
      await cacheManager.set('default-ttl', 'value');
      const entries = cacheManager.getEntries();
      expect(entries[0].ttl).toBe(300000); // Default from config
    });
  });

  describe('Statistics', () => {
    test('should track cache hits and misses', async () => {
      await cacheManager.set('stats-key', 'stats-value');
      
      // Hit
      await cacheManager.get('stats-key');
      
      // Miss
      await cacheManager.get('non-existent');
      
      const stats = cacheManager.getStats();
      expect(stats.hits).toBe(1);
      expect(stats.misses).toBe(1);
      expect(stats.sets).toBe(1);
      expect(stats.hitRate).toBe(0.5);
    });

    test('should track entry count and size', async () => {
      await cacheManager.set('size-key', 'test-value');
      
      const stats = cacheManager.getStats();
      expect(stats.entryCount).toBe(1);
      expect(stats.totalSize).toBeGreaterThan(0);
    });
  });

  describe('Tags and Bulk Operations', () => {
    test('should clear entries by tags', async () => {
      await cacheManager.set('tag1', 'value1', { tags: ['group1', 'group2'] });
      await cacheManager.set('tag2', 'value2', { tags: ['group1'] });
      await cacheManager.set('tag3', 'value3', { tags: ['group3'] });
      
      const cleared = await cacheManager.clearByTags(['group1']);
      expect(cleared).toBe(2);
      
      const value1 = await cacheManager.get('tag1');
      const value2 = await cacheManager.get('tag2');
      const value3 = await cacheManager.get('tag3');
      
      expect(value1).toBeUndefined();
      expect(value2).toBeUndefined();
      expect(value3).toBe('value3');
    });

    test('should warm cache with multiple entries', async () => {
      const entries = [
        { key: 'warm1', value: 'value1' },
        { key: 'warm2', value: 'value2', options: { ttl: 60000 } },
        { key: 'warm3', value: 'value3', options: { tags: ['warm'] } },
      ];
      
      await cacheManager.warmCache(entries);
      
      const value1 = await cacheManager.get('warm1');
      const value2 = await cacheManager.get('warm2');
      const value3 = await cacheManager.get('warm3');
      
      expect(value1).toBe('value1');
      expect(value2).toBe('value2');
      expect(value3).toBe('value3');
    });
  });

  describe('LRU Eviction', () => {
    test('should evict least recently used entries when at capacity', async () => {
      // Create cache with small capacity
      const smallCache = new CacheManager({ maxEntries: 2 });
      
      try {
        await smallCache.set('key1', 'value1');
        await smallCache.set('key2', 'value2');
        
        // Access key1 to make it more recently used
        await smallCache.get('key1');
        
        // Add key3, should evict key2 (least recently used)
        await smallCache.set('key3', 'value3');
        
        const value1 = await smallCache.get('key1');
        const value2 = await smallCache.get('key2');
        const value3 = await smallCache.get('key3');
        
        expect(value1).toBe('value1');
        expect(value2).toBeUndefined(); // Evicted
        expect(value3).toBe('value3');
      } finally {
        await smallCache.shutdown();
      }
    });
  });

  describe('Event Emission', () => {
    test('should emit events for cache operations', async () => {
      const setHandler = vi.fn();
      const deleteHandler = vi.fn();
      const clearHandler = vi.fn();
      
      cacheManager.on('set', setHandler);
      cacheManager.on('delete', deleteHandler);
      cacheManager.on('clear', clearHandler);
      
      await cacheManager.set('event-key', 'event-value');
      await cacheManager.delete('event-key');
      await cacheManager.clear();
      
      expect(setHandler).toHaveBeenCalledWith('event-key', 'event-value', {});
      expect(deleteHandler).toHaveBeenCalledWith('event-key');
      expect(clearHandler).toHaveBeenCalledWith(0); // 0 entries cleared since already deleted
    });
  });

  describe('Error Handling', () => {
    test('should handle serialization errors gracefully', async () => {
      const circularObj: any = {};
      circularObj.self = circularObj;
      
      // Should not throw, but should handle gracefully
      await expect(cacheManager.set('circular', circularObj)).resolves.not.toThrow();
    });

    test('should continue operating after errors', async () => {
      // This test ensures the cache manager remains functional after errors
      await cacheManager.set('normal-key', 'normal-value');
      
      const value = await cacheManager.get('normal-key');
      expect(value).toBe('normal-value');
    });
  });

  describe('Memory Management', () => {
    test('should respect memory limits', async () => {
      // Create cache with very small memory limit
      const memoryLimitedCache = new CacheManager({ maxMemorySize: 1024 }); // 1KB
      
      try {
        // Add large values that exceed memory limit
        const largeValue = 'x'.repeat(500); // 500 bytes each
        
        await memoryLimitedCache.set('large1', largeValue);
        await memoryLimitedCache.set('large2', largeValue);
        await memoryLimitedCache.set('large3', largeValue); // Should trigger eviction
        
        const stats = memoryLimitedCache.getStats();
        expect(stats.totalSize).toBeLessThanOrEqual(1024);
      } finally {
        await memoryLimitedCache.shutdown();
      }
    });
  });

  describe('Configuration', () => {
    test('should use config values when provided', () => {
      expect(mockConfig.get).toHaveBeenCalledWith('CACHE_TTL', 300000);
      expect(mockConfig.get).toHaveBeenCalledWith('CACHE_MAX_MEMORY', 100 * 1024 * 1024);
      expect(mockConfig.get).toHaveBeenCalledWith('CACHE_MAX_ENTRIES', 10000);
    });

    test('should work without config manager', () => {
      const standaloneCache = new CacheManager();
      expect(standaloneCache).toBeDefined();
      standaloneCache.shutdown();
    });
  });
});
