-- Migration: Create performance indexes
-- Description: Add specialized indexes for common query patterns
-- Template: CREATE_INDEX
-- Best Practices Demonstrated:
--   - Use CONCURRENTLY for production safety
--   - Create composite indexes for multi-column queries
--   - Use partial indexes for filtered queries
--   - Include covering indexes for query optimization

-- Up migration

-- Composite index for user posts by status and date
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_posts_author_status_published 
  ON posts (author_id, status, published_at DESC) 
  WHERE status = 'published';

-- Composite index for post search with status filter
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_posts_status_created 
  ON posts (status, created_at DESC) 
  WHERE deleted_at IS NULL;

-- Covering index for post list queries (includes commonly selected columns)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_posts_list_covering 
  ON posts (status, published_at DESC) 
  INCLUDE (id, title, slug, excerpt, author_id, view_count)
  WHERE status = 'published' AND deleted_at IS NULL;

-- Partial index for active users with recent activity
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_active_recent 
  ON users (last_login_at DESC) 
  WHERE is_active = true AND last_login_at > (NOW() - INTERVAL '30 days');

-- Composite index for user search by name
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_name_search 
  ON users (first_name, last_name) 
  WHERE is_active = true;

-- GIN index for tag-based post searches
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_posts_tags_gin 
  ON posts USING gin(tags) 
  WHERE status = 'published' AND deleted_at IS NULL;

-- Expression index for case-insensitive email searches
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email_lower 
  ON users (lower(email)) 
  WHERE is_active = true;

-- Expression index for case-insensitive username searches
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_username_lower 
  ON users (lower(username)) 
  WHERE is_active = true;

-- Partial index for posts needing moderation (draft posts older than 7 days)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_posts_stale_drafts 
  ON posts (created_at) 
  WHERE status = 'draft' AND created_at < (NOW() - INTERVAL '7 days');

-- Composite index for analytics queries (posts by author and date range)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_posts_analytics 
  ON posts (author_id, created_at, status) 
  INCLUDE (view_count, published_at)
  WHERE deleted_at IS NULL;

-- Down migration
-- DROP INDEX CONCURRENTLY IF EXISTS idx_posts_author_status_published;
-- DROP INDEX CONCURRENTLY IF EXISTS idx_posts_status_created;
-- DROP INDEX CONCURRENTLY IF EXISTS idx_posts_list_covering;
-- DROP INDEX CONCURRENTLY IF EXISTS idx_users_active_recent;
-- DROP INDEX CONCURRENTLY IF EXISTS idx_users_name_search;
-- DROP INDEX CONCURRENTLY IF EXISTS idx_posts_tags_gin;
-- DROP INDEX CONCURRENTLY IF EXISTS idx_users_email_lower;
-- DROP INDEX CONCURRENTLY IF EXISTS idx_users_username_lower;
-- DROP INDEX CONCURRENTLY IF EXISTS idx_posts_stale_drafts;
-- DROP INDEX CONCURRENTLY IF EXISTS idx_posts_analytics;
