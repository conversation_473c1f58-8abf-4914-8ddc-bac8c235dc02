# 🚀 Local Supabase MCP Server

> **Production-ready MCP server for connecting local and remote Supabase instances to AI assistants like <PERSON><PERSON><PERSON>, <PERSON>, and Windsurf.**

![supabase-mcp-demo](https://github.com/user-attachments/assets/3fce101a-b7d4-482f-9182-0be70ed1ad56)

The [Model Context Protocol](https://modelcontextprotocol.io/introduction) (MCP) standardizes how Large Language Models (LLMs) communicate with external services. This server provides a comprehensive interface between AI assistants and your Supabase projects, enabling powerful database operations, schema management, and development workflows.

## 🎯 Features

- **🗄️ Database Operations**: Full CRUD operations, SQL execution, schema inspection
- **🔧 DDL Operations**: Safe schema modifications with constraint validation
- **⚡ Edge Functions**: Deploy and manage Supabase Edge Functions
- **🛠️ Development Tools**: TypeScript generation, project configuration access
- **🔒 Security**: Built-in validation, constraint checking, and read-only mode
- **🐳 Docker Support**: Containerized deployment options
- **📝 Migration Management**: Track and apply database migrations
- **🌐 Flexible Deployment**: Works with local and remote Supabase instances

## 📋 Prerequisites

- **Node.js 18+** - [Download here](https://nodejs.org/)
- **Supabase Instance** - Local via CLI or remote hosted project
- **MCP Client** - Cursor, Claude Desktop, Windsurf, or other MCP-compatible AI assistant

## 🚀 Quick Start

### Option 1: Package Installation (Recommended)

```bash
# Install the package globally
npm install -g packages/mcp-server-supabase/supabase-mcp-server-supabase-0.5.0.tgz

# Verify installation
mcp-server-supabase --version
```

### Option 2: Development Setup

```bash
# Clone and build
git clone <repository-url>
cd local-supabase-mcp
npm install
npm run build
```

## ⚙️ Configuration

### Environment Variables

Create a `.env` file or set environment variables:

```bash
# Required: Your Supabase instance details
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIs...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIs...

# Optional: Configuration flags
READ_ONLY=false                    # Set to true for read-only access
DEBUG_SQL=false                    # Enable SQL query logging
```

### MCP Client Configuration

#### Cursor IDE

Create or update your `cursor-mcp-config.json`:

```json
{
  "mcpServers": {
    "supabase-local": {
      "command": "node",
      "args": ["/absolute/path/to/dist/transports/stdio.cjs"],
      "env": {
        "SUPABASE_URL": "https://your-project.supabase.co",
        "SUPABASE_ANON_KEY": "your-anon-key",
        "SUPABASE_SERVICE_ROLE_KEY": "your-service-key",
        "READ_ONLY": "false"
      }
    }
  }
}
```

#### Claude Desktop

Update your `claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "supabase-local": {
      "command": "mcp-server-supabase",
      "args": [
        "--supabase-url=https://your-project.supabase.co",
        "--anon-key=your-anon-key",
        "--service-key=your-service-key"
      ]
    }
  }
}
```

## 🛠️ Available Tools

### 📊 Database Operations
- `list_tables` - List all tables in specified schemas
- `list_extensions` - View installed PostgreSQL extensions
- `execute_sql` - Execute raw SQL queries
- `list_migrations` - Show migration history
- `apply_migration` - Apply new database migrations

### �� Development Tools
- `get_project_url` - Retrieve project API URL
- `get_anon_key` - Get anonymous API key
- `generate_typescript_types` - Generate TypeScript definitions from schema

### ⚡ Edge Functions
- `list_edge_functions` - List deployed Edge Functions
- `deploy_edge_function` - Deploy new Edge Functions

### 🔍 Monitoring & Health
- `check_local_connection` - Verify database connectivity
- `get_local_config` - Retrieve current configuration
- `search_docs` - Search Supabase documentation

## 🐳 Docker Deployment

### Using Docker Compose

```yaml
# docker-compose.yml
version: '3.8'
services:
  mcp-server:
    build: ./packages/mcp-server-supabase
    environment:
      - SUPABASE_URL=https://your-project.supabase.co
      - SUPABASE_ANON_KEY=your-anon-key
      - SUPABASE_SERVICE_ROLE_KEY=your-service-key
      - READ_ONLY=false
    container_name: supabase-mcp-server
```

```bash
# Start the container
docker-compose up -d

# Connect from MCP client
docker exec -i supabase-mcp-server node /app/dist/transports/stdio.cjs
```

## 🔒 Security & Best Practices

### Read-Only Mode

For production environments or sensitive data:

```bash
# Enable read-only mode
export READ_ONLY=true

# Or via command line
mcp-server-supabase --read-only
```

### Environment Security

- Store sensitive keys in environment variables, not configuration files
- Use strong service role keys with minimal required permissions
- Enable RLS (Row Level Security) policies in your Supabase project
- Regularly rotate API keys

## 🧪 Testing & Validation

```bash
# Run test suite
npm test

# Test database connection
npm run test:connection

# Validate configuration
npm run test:config
```

## 📖 Local Development

### Setting up Local Supabase

```bash
# Install Supabase CLI
npm install -g supabase

# Initialize project
supabase init

# Start local services
supabase start

# Get connection details
supabase status
```

### Development Workflow

```bash
# Install dependencies
npm install

# Start development server
npm run dev:local

# Build for production
npm run build

# Test the build
npm run test
```

## 🔧 Troubleshooting

### Common Issues

**Connection Errors**
- Verify your Supabase URL and keys are correct
- Check network connectivity to your Supabase instance
- Ensure your service role key has necessary permissions

**MCP Client Integration**
- Use `.cjs` files for CommonJS compatibility
- Restart your AI assistant completely after configuration changes
- Verify file paths are absolute and use forward slashes

**Environment Variables**
- Check that all required environment variables are set
- Ensure no trailing spaces in environment variable values
- Verify environment variables are accessible to the Node.js process

### Debug Mode

Enable detailed logging:

```bash
export DEBUG_SQL=true
mcp-server-supabase
```

## 📚 Advanced Usage

### Custom Schemas

```json
{
  "schemas": ["public", "auth", "custom_schema"]
}
```

### Batch Operations

The server supports efficient batch operations for bulk data processing while maintaining ACID transaction guarantees.

### Migration Management

- Track schema changes through the built-in migration system
- Apply migrations safely with rollback capabilities
- Validate migrations before deployment

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-tool`
3. Commit changes: `git commit -am 'Add new tool'`
4. Push to branch: `git push origin feature/new-tool`
5. Submit a Pull Request

## 📄 License

This project is licensed under the Apache License 2.0 - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- 📖 [Deployment Guide](DEPLOYMENT_GUIDE.md) - Complete installation instructions
- 🐛 [Issues](https://github.com/your-repo/issues) - Report bugs or request features
- 💬 [Discussions](https://github.com/your-repo/discussions) - Community support

---

**Made with ❤️ for the Supabase and MCP communities**
