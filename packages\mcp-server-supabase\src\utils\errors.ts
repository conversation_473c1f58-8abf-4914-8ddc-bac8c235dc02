/**
 * Custom error classes for the MCP server
 */

export enum ErrorCode {
  // Protocol errors
  PROTOCOL_ERROR = 'PROTOCOL_ERROR',
  INVALID_REQUEST = 'INVALID_REQUEST',
  METHOD_NOT_FOUND = 'METHOD_NOT_FOUND',
  
  // Validation errors
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  SCHEMA_VALIDATION_ERROR = 'SCHEMA_VALIDATION_ERROR',
  PARAMETER_ERROR = 'PARAMETER_ERROR',
  
  // Database errors
  DATABASE_ERROR = 'DATABASE_ERROR',
  CONNECTION_ERROR = 'CONNECTION_ERROR',
  QUERY_ERROR = 'QUERY_ERROR',
  TRANSACTION_ERROR = 'TRANSACTION_ERROR',
  
  // Specific database error codes
  CONNECTION_POOL_EXHAUSTED = 'CONNECTION_POOL_EXHAUSTED',
  CONNECTION_ACQUISITION_FAILED = 'CONNECTION_ACQUISITION_FAILED',
  CONNECTION_TIMEOUT = 'CONNECTION_TIMEOUT',
  QUERY_TIMEOUT = 'QUERY_TIMEOUT',
  INVALID_QUERY = 'INVALID_QUERY',
  CONSTRAINT_VIOLATION = 'CONSTRAINT_VIOLATION',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  TABLE_NOT_FOUND = 'TABLE_NOT_FOUND',
  COLUMN_NOT_FOUND = 'COLUMN_NOT_FOUND',
  FOREIGN_KEY_VIOLATION = 'FOREIGN_KEY_VIOLATION',
  UNIQUE_VIOLATION = 'UNIQUE_VIOLATION',
  CHECK_VIOLATION = 'CHECK_VIOLATION',
  NOT_NULL_VIOLATION = 'NOT_NULL_VIOLATION',
  INVALID_TRANSACTION = 'INVALID_TRANSACTION',
  DEADLOCK_DETECTED = 'DEADLOCK_DETECTED',
  
  // Network and connectivity errors
  NETWORK_ERROR = 'NETWORK_ERROR',
  DNS_RESOLUTION_ERROR = 'DNS_RESOLUTION_ERROR',
  SSL_ERROR = 'SSL_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  
  // Tool errors
  TOOL_NOT_FOUND = 'TOOL_NOT_FOUND',
  TOOL_EXECUTION_ERROR = 'TOOL_EXECUTION_ERROR',
  TOOL_TIMEOUT = 'TOOL_TIMEOUT',
  
  // Configuration errors
  CONFIG_ERROR = 'CONFIG_ERROR',
  MISSING_CREDENTIALS = 'MISSING_CREDENTIALS',
  INVALID_CONFIGURATION = 'INVALID_CONFIGURATION',
  
  // Server errors
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  CIRCUIT_BREAKER_OPEN = 'CIRCUIT_BREAKER_OPEN',
  
  // Timeout errors
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  REQUEST_TIMEOUT = 'REQUEST_TIMEOUT',
  OPERATION_TIMEOUT = 'OPERATION_TIMEOUT',
}

export class McpServerError extends Error {
  public readonly code: ErrorCode;
  public readonly statusCode: number;
  public readonly requestId?: string;
  public readonly context?: Record<string, unknown>;
  public readonly timestamp: Date;
  public override readonly cause?: Error;

  constructor(
    code: ErrorCode,
    message: string,
    statusCode: number = 500,
    options: {
      requestId?: string;
      context?: Record<string, unknown>;
      cause?: Error;
    } = {}
  ) {
    super(message);
    this.name = 'McpServerError';
    this.code = code;
    this.statusCode = statusCode;
    this.requestId = options.requestId;
    this.context = options.context;
    this.timestamp = new Date();
    this.cause = options.cause;
    
    // Maintain proper stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, McpServerError);
    }
  }

  toJSON() {
    return {
      name: this.name,
      code: this.code,
      message: this.message,
      statusCode: this.statusCode,
      requestId: this.requestId,
      context: this.context,
      timestamp: this.timestamp.toISOString(),
      stack: this.stack,
    };
  }
}

export class ProtocolError extends McpServerError {
  constructor(message: string, options?: { requestId?: string; context?: Record<string, unknown>; cause?: Error }) {
    super(ErrorCode.PROTOCOL_ERROR, message, 400, options);
    this.name = 'ProtocolError';
  }
}

export class ValidationError extends McpServerError {
  constructor(message: string, options?: { requestId?: string; context?: Record<string, unknown>; cause?: Error }) {
    super(ErrorCode.VALIDATION_ERROR, message, 400, options);
    this.name = 'ValidationError';
  }
}

export class DatabaseError extends McpServerError {
  public readonly originalCode?: string;
  public readonly severity?: string;
  public readonly detail?: string;
  public readonly hint?: string;
  public readonly position?: string;
  public readonly internalPosition?: string;
  public readonly internalQuery?: string;
  public readonly where?: string;
  public readonly schema?: string;
  public readonly table?: string;
  public readonly column?: string;
  public readonly dataType?: string;
  public readonly constraint?: string;

  constructor(
    message: string, 
    code: string | ErrorCode = ErrorCode.DATABASE_ERROR,
    options?: { 
      requestId?: string; 
      context?: Record<string, unknown>; 
      cause?: Error;
      originalCode?: string;
      severity?: string;
      detail?: string;
      hint?: string;
      position?: string;
      internalPosition?: string;
      internalQuery?: string;
      where?: string;
      schema?: string;
      table?: string;
      column?: string;
      dataType?: string;
      constraint?: string;
    }
  ) {
    const errorCode = typeof code === 'string' ? ErrorCode.DATABASE_ERROR : code;
    super(errorCode, message, 500, options);
    this.name = 'DatabaseError';
    
    // PostgreSQL/Supabase specific error details
    this.originalCode = options?.originalCode || (typeof code === 'string' ? code : undefined);
    this.severity = options?.severity;
    this.detail = options?.detail;
    this.hint = options?.hint;
    this.position = options?.position;
    this.internalPosition = options?.internalPosition;
    this.internalQuery = options?.internalQuery;
    this.where = options?.where;
    this.schema = options?.schema;
    this.table = options?.table;
    this.column = options?.column;
    this.dataType = options?.dataType;
    this.constraint = options?.constraint;
  }
}

export class ConnectionError extends DatabaseError {
  constructor(message: string, options?: { requestId?: string; context?: Record<string, unknown>; cause?: Error }) {
    super(message, ErrorCode.CONNECTION_ERROR, options);
    this.name = 'ConnectionError';
  }
}

export class QueryError extends DatabaseError {
  constructor(message: string, options?: { requestId?: string; context?: Record<string, unknown>; cause?: Error }) {
    super(message, ErrorCode.QUERY_ERROR, options);
    this.name = 'QueryError';
  }
}

export class TimeoutError extends McpServerError {
  public readonly timeoutType: 'request' | 'query' | 'connection' | 'operation';
  public readonly timeoutValue: number;

  constructor(
    message: string, 
    timeoutType: 'request' | 'query' | 'connection' | 'operation' = 'operation',
    timeoutValue?: number,
    options?: { requestId?: string; context?: Record<string, unknown>; cause?: Error }
  ) {
    const code = timeoutType === 'request' ? ErrorCode.REQUEST_TIMEOUT :
                 timeoutType === 'query' ? ErrorCode.QUERY_TIMEOUT :
                 timeoutType === 'connection' ? ErrorCode.CONNECTION_TIMEOUT :
                 ErrorCode.OPERATION_TIMEOUT;
    
    super(code, message, 408, options);
    this.name = 'TimeoutError';
    this.timeoutType = timeoutType;
    this.timeoutValue = timeoutValue || 0;
  }
}

export class CircuitBreakerError extends McpServerError {
  public readonly state: string;
  public readonly failures: number;

  constructor(
    message: string, 
    state: string,
    failures: number,
    options?: { requestId?: string; context?: Record<string, unknown>; cause?: Error }
  ) {
    super(ErrorCode.CIRCUIT_BREAKER_OPEN, message, 503, options);
    this.name = 'CircuitBreakerError';
    this.state = state;
    this.failures = failures;
  }
}

export class RetryableError extends DatabaseError {
  public readonly retryAfter?: number;
  public readonly maxRetries?: number;
  public readonly attempt?: number;

  constructor(
    message: string, 
    code: ErrorCode = ErrorCode.DATABASE_ERROR,
    options?: { 
      requestId?: string; 
      context?: Record<string, unknown>; 
      cause?: Error;
      retryAfter?: number;
      maxRetries?: number;
      attempt?: number;
    }
  ) {
    super(message, code, options);
    this.name = 'RetryableError';
    this.retryAfter = options?.retryAfter;
    this.maxRetries = options?.maxRetries;
    this.attempt = options?.attempt;
  }
}

export class ToolError extends McpServerError {
  constructor(message: string, code: ErrorCode = ErrorCode.TOOL_EXECUTION_ERROR, options?: { requestId?: string; context?: Record<string, unknown>; cause?: Error }) {
    super(code, message, 500, options);
    this.name = 'ToolError';
  }
}

export class ConfigurationError extends McpServerError {
  constructor(message: string, options?: { requestId?: string; context?: Record<string, unknown>; cause?: Error }) {
    super(ErrorCode.CONFIG_ERROR, message, 500, options);
    this.name = 'ConfigurationError';
  }
}

/**
 * Utility function to create standardized error responses
 */
export function createErrorResponse(error: unknown, requestId?: string) {
  if (error instanceof McpServerError) {
    return {
      isError: true,
      content: [
        {
          type: 'text',
          text: JSON.stringify({
            error: {
              code: error.code,
              message: error.message,
              requestId: error.requestId || requestId,
              timestamp: error.timestamp.toISOString(),
              context: error.context,
            },
          }),
        },
      ],
    };
  }

  // Handle unknown errors
  const unknownError = new McpServerError(
    ErrorCode.INTERNAL_ERROR,
    error instanceof Error ? error.message : String(error),
    500,
    { requestId, cause: error instanceof Error ? error : undefined }
  );

  return {
    isError: true,
    content: [
      {
        type: 'text',
        text: JSON.stringify({
          error: {
            code: unknownError.code,
            message: unknownError.message,
            requestId: unknownError.requestId,
            timestamp: unknownError.timestamp.toISOString(),
          },
        }),
      },
    ],
  };
}

/**
 * Enhanced error enumeration function with better context
 */
export function enumerateError(error: unknown, requestId?: string): Record<string, unknown> | unknown {
  // Handle null/undefined by returning as-is
  if (error === null || error === undefined) {
    return error;
  }

  if (error instanceof McpServerError) {
    return {
      name: error.name,
      code: error.code,
      message: error.message,
      statusCode: error.statusCode,
      requestId: error.requestId || requestId,
      timestamp: error.timestamp.toISOString(),
      context: error.context,
    };
  }

  if (error instanceof Error) {
    return {
      name: error.name,
      message: error.message,
      requestId,
      timestamp: new Date().toISOString(),
    };
  }

  return {
    error: error,
    requestId,
    timestamp: new Date().toISOString(),
  };
}

/**
 * Error classification and mapping utilities
 */
export class ErrorClassifier {
  /**
   * Classify a Supabase error and map it to our error codes
   */
  static classifySupabaseError(error: any): {
    code: ErrorCode;
    isRetryable: boolean;
    retryAfter?: number;
    details?: Record<string, any>;
  } {
    if (!error) {
      return { code: ErrorCode.INTERNAL_ERROR, isRetryable: false };
    }

    const errorCode = error.code || error.error_code || '';
    const message = error.message || error.error_description || '';
    const details = error.details || {};

    // Network and connectivity errors (retryable)
    if (errorCode.includes('NETWORK') || errorCode.includes('ECONNREFUSED') || 
        errorCode.includes('ETIMEDOUT') || errorCode.includes('ENOTFOUND')) {
      return {
        code: ErrorCode.NETWORK_ERROR,
        isRetryable: true,
        retryAfter: 1000,
        details
      };
    }

    // Authentication/Authorization errors (not retryable)
    if (errorCode.includes('PGRST301') || errorCode.includes('PGRST116') || 
        message.includes('JWT') || message.includes('authentication')) {
      return {
        code: ErrorCode.AUTHENTICATION_ERROR,
        isRetryable: false,
        details
      };
    }

    // Permission errors (not retryable)
    if (errorCode.includes('PGRST301') || message.includes('permission') || 
        message.includes('insufficient')) {
      return {
        code: ErrorCode.PERMISSION_DENIED,
        isRetryable: false,
        details
      };
    }

    // Constraint violations (not retryable)
    if (errorCode.includes('23505')) { // Unique violation
      return {
        code: ErrorCode.UNIQUE_VIOLATION,
        isRetryable: false,
        details
      };
    }

    if (errorCode.includes('23503')) { // Foreign key violation
      return {
        code: ErrorCode.FOREIGN_KEY_VIOLATION,
        isRetryable: false,
        details
      };
    }

    if (errorCode.includes('23514')) { // Check violation
      return {
        code: ErrorCode.CHECK_VIOLATION,
        isRetryable: false,
        details
      };
    }

    if (errorCode.includes('23502')) { // Not null violation
      return {
        code: ErrorCode.NOT_NULL_VIOLATION,
        isRetryable: false,
        details
      };
    }

    // Table/column not found (not retryable)
    if (errorCode.includes('42P01') || message.includes('does not exist')) {
      return {
        code: ErrorCode.TABLE_NOT_FOUND,
        isRetryable: false,
        details
      };
    }

    if (errorCode.includes('42703')) {
      return {
        code: ErrorCode.COLUMN_NOT_FOUND,
        isRetryable: false,
        details
      };
    }

    // Timeout errors (retryable)
    if (message.includes('timeout') || errorCode.includes('TIMEOUT')) {
      return {
        code: ErrorCode.QUERY_TIMEOUT,
        isRetryable: true,
        retryAfter: 2000,
        details
      };
    }

    // Rate limiting (retryable with longer delay)
    if (errorCode.includes('429') || message.includes('rate limit')) {
      return {
        code: ErrorCode.RATE_LIMIT_EXCEEDED,
        isRetryable: true,
        retryAfter: 5000,
        details
      };
    }

    // Service unavailable (retryable)
    if (errorCode.includes('503') || message.includes('unavailable')) {
      return {
        code: ErrorCode.SERVICE_UNAVAILABLE,
        isRetryable: true,
        retryAfter: 3000,
        details
      };
    }

    // Default to database error (not retryable unless explicitly known)
    return {
      code: ErrorCode.DATABASE_ERROR,
      isRetryable: false,
      details
    };
  }

  /**
   * Create a standardized database error from a Supabase error
   */
  static createDatabaseError(
    error: any, 
    context?: Record<string, unknown>,
    requestId?: string
  ): DatabaseError {
    const classification = this.classifySupabaseError(error);
    
    const options = {
      requestId,
      context,
      cause: error,
      originalCode: error.code || error.error_code,
      severity: error.severity,
      detail: error.detail || error.details,
      hint: error.hint,
      position: error.position,
      internalPosition: error.internalPosition,
      internalQuery: error.internalQuery,
      where: error.where,
      schema: error.schema_name,
      table: error.table_name,
      column: error.column_name,
      dataType: error.datatype_name,
      constraint: error.constraint_name,
    };

    if (classification.isRetryable) {
      return new RetryableError(
        error.message || 'Database operation failed',
        classification.code,
        {
          ...options,
          retryAfter: classification.retryAfter,
        }
      );
    }

    return new DatabaseError(
      error.message || 'Database operation failed',
      classification.code,
      options
    );
  }
}

/**
 * Retry mechanism for database operations
 */
export class RetryManager {
  private static readonly DEFAULT_MAX_RETRIES = 3;
  private static readonly DEFAULT_BASE_DELAY = 1000;
  private static readonly DEFAULT_MAX_DELAY = 10000;

  /**
   * Execute an operation with retry logic
   */
  static async executeWithRetry<T>(
    operation: () => Promise<T>,
    options: {
      maxRetries?: number;
      baseDelay?: number;
      maxDelay?: number;
      shouldRetry?: (error: any, attempt: number) => boolean;
      onRetry?: (error: any, attempt: number, delay: number) => void;
      requestId?: string;
    } = {}
  ): Promise<T> {
    const {
      maxRetries = this.DEFAULT_MAX_RETRIES,
      baseDelay = this.DEFAULT_BASE_DELAY,
      maxDelay = this.DEFAULT_MAX_DELAY,
      shouldRetry = this.defaultShouldRetry,
      onRetry,
      requestId,
    } = options;

    let lastError: any;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;

        // Don't retry on the last attempt
        if (attempt === maxRetries) {
          break;
        }

        // Check if we should retry this error
        if (!shouldRetry(error, attempt)) {
          break;
        }

        // Calculate delay with exponential backoff and jitter
        const exponentialDelay = Math.min(baseDelay * Math.pow(2, attempt), maxDelay);
        const jitterDelay = exponentialDelay * (0.5 + Math.random() * 0.5);
        const finalDelay = Math.min(jitterDelay, maxDelay);

        // Call retry callback if provided
        if (onRetry) {
          onRetry(error, attempt + 1, finalDelay);
        }

        // Wait before retrying
        await this.delay(finalDelay);
      }
    }

    // If we get here, all retries failed
    throw ErrorClassifier.createDatabaseError(lastError, {
      maxRetries,
      finalAttempt: maxRetries + 1,
    }, requestId);
  }

  /**
   * Default retry logic
   */
  private static defaultShouldRetry(error: any, attempt: number): boolean {
    // Don't retry after too many attempts
    if (attempt >= 3) {
      return false;
    }

    // If it's a RetryableError, use its guidance
    if (error instanceof RetryableError) {
      return true;
    }

    // If it's a known error type, check if it's retryable
    const classification = ErrorClassifier.classifySupabaseError(error);
    return classification.isRetryable;
  }

  /**
   * Delay utility
   */
  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * Error recovery strategies
 */
export class ErrorRecovery {
  /**
   * Get recovery suggestions for an error
   */
  static getRecoverySuggestions(error: any): string[] {
    const suggestions: string[] = [];

    if (error instanceof ConnectionError) {
      suggestions.push('Check network connectivity to the Supabase instance');
      suggestions.push('Verify the Supabase URL is correct and accessible');
      suggestions.push('Check if the Supabase service is currently available');
    }

    if (error instanceof DatabaseError) {
      if (error.code === ErrorCode.AUTHENTICATION_ERROR) {
        suggestions.push('Verify the Supabase API keys are correct');
        suggestions.push('Check if the service role key has proper permissions');
      }

      if (error.code === ErrorCode.PERMISSION_DENIED) {
        suggestions.push('Check Row Level Security (RLS) policies');
        suggestions.push('Verify the user has the required permissions');
        suggestions.push('Consider using a service role key for privileged operations');
      }

      if (error.code === ErrorCode.TABLE_NOT_FOUND) {
        suggestions.push('Verify the table name is spelled correctly');
        suggestions.push('Check if the table exists in the correct schema');
        suggestions.push('Ensure database migrations have been applied');
      }

      if (error.originalCode?.startsWith('23')) {
        suggestions.push('Review the data being inserted/updated for constraint violations');
        suggestions.push('Check foreign key relationships and referenced data');
        suggestions.push('Validate data types and formats');
      }
    }

    if (error instanceof TimeoutError) {
      suggestions.push('Consider increasing timeout values');
      suggestions.push('Optimize query performance');
      suggestions.push('Check network latency to the Supabase instance');
    }

    if (error instanceof CircuitBreakerError) {
      suggestions.push('Wait for the service to recover');
      suggestions.push('Check Supabase service status');
      suggestions.push('Consider implementing fallback mechanisms');
    }

    return suggestions;
  }
}

/**
 * Error reporting and logging utilities
 */
export class ErrorReporter {
  /**
   * Create a comprehensive error report
   */
  static createErrorReport(error: any, context?: Record<string, unknown>): {
    error: Record<string, any>;
    context?: Record<string, unknown>;
    suggestions: string[];
    classification: any;
    timestamp: string;
  } {
    const errorData = enumerateError(error) as Record<string, any>;
    const classification = ErrorClassifier.classifySupabaseError(error);
    const suggestions = ErrorRecovery.getRecoverySuggestions(error);

    return {
      error: errorData,
      context,
      suggestions,
      classification,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Format error for user-friendly display
   */
  static formatUserError(error: any): string {
    if (error instanceof DatabaseError) {
      if (error.code === ErrorCode.PERMISSION_DENIED) {
        return 'Access denied. Please check your permissions.';
      }
      
      if (error.code === ErrorCode.TABLE_NOT_FOUND) {
        return `Table not found: ${error.table || 'unknown'}`;
      }

      if (error.code === ErrorCode.UNIQUE_VIOLATION) {
        return 'This record already exists. Please use unique values.';
      }

      if (error.code === ErrorCode.FOREIGN_KEY_VIOLATION) {
        return 'Referenced record does not exist. Please check your data relationships.';
      }
    }

    if (error instanceof ConnectionError) {
      return 'Unable to connect to the database. Please try again later.';
    }

    if (error instanceof TimeoutError) {
      return 'The operation timed out. Please try again.';
    }

    return error.message || 'An unexpected error occurred.';
  }
}
