/**
 * WebSocket Transport Layer for Real-time Protocol Communication
 * 
 * Provides WebSocket-based real-time communication for MCP protocol messages,
 * notifications, and events.
 */

import { WebSocketServer, WebSocket } from 'ws';
import { EventEmitter } from 'events';
import { contextLogger } from '../utils/logger.js';
import type { ConfigManager } from '../config/config-manager.js';
import type { ProtocolMessage, ClientConnection } from './protocol-communication.js';
import type { NotificationPayload } from './notification-manager.js';

export interface WebSocketTransportOptions {
  port?: number;
  host?: string;
  path?: string;
  maxConnections?: number;
  heartbeatInterval?: number;
  messageTimeout?: number;
  enableCompression?: boolean;
  enableAuth?: boolean;
}

export interface WebSocketClient extends ClientConnection {
  socket: WebSocket;
  subscriptions: Set<string>;
  lastPing?: Date;
  lastPong?: Date;
}

/**
 * WebSocket transport for real-time MCP protocol communication
 */
export class WebSocketTransport extends EventEmitter {
  private readonly logger = contextLogger.child({ component: 'WebSocketTransport' });
  private readonly config?: ConfigManager;
  private readonly options: Required<WebSocketTransportOptions>;
  
  private server?: WebSocketServer;
  private clients = new Map<string, WebSocketClient>();
  private heartbeatInterval?: NodeJS.Timeout;
  private isRunning = false;

  constructor(options: WebSocketTransportOptions = {}, config?: ConfigManager) {
    super();
    
    this.config = config;
    this.options = {
      port: options.port ?? config?.get('WEBSOCKET_PORT', 8080) ?? 8080,
      host: options.host ?? config?.get('WEBSOCKET_HOST', 'localhost') ?? 'localhost',
      path: options.path ?? config?.get('WEBSOCKET_PATH', '/ws') ?? '/ws',
      maxConnections: options.maxConnections ?? config?.get('WEBSOCKET_MAX_CONNECTIONS', 100) ?? 100,
      heartbeatInterval: options.heartbeatInterval ?? config?.get('WEBSOCKET_HEARTBEAT_INTERVAL', 30000) ?? 30000,
      messageTimeout: options.messageTimeout ?? config?.get('WEBSOCKET_MESSAGE_TIMEOUT', 10000) ?? 10000,
      enableCompression: options.enableCompression ?? config?.get('WEBSOCKET_ENABLE_COMPRESSION', true) ?? true,
      enableAuth: options.enableAuth ?? config?.get('WEBSOCKET_ENABLE_AUTH', false) ?? false,
    };

    this.logger.info('WebSocket transport initialized', {
      port: this.options.port,
      host: this.options.host,
      path: this.options.path,
      maxConnections: this.options.maxConnections,
    });
  }

  /**
   * Start the WebSocket server
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('WebSocket transport already running');
      return;
    }

    try {
      this.server = new WebSocketServer({
        port: this.options.port,
        host: this.options.host,
        path: this.options.path,
        perMessageDeflate: this.options.enableCompression,
        maxPayload: 1024 * 1024, // 1MB max message size
      });

      this.setupServerEventHandlers();
      this.startHeartbeat();
      
      this.isRunning = true;
      
      this.logger.info('WebSocket server started', {
        port: this.options.port,
        host: this.options.host,
        path: this.options.path,
      });

      this.emit('started');
    } catch (error) {
      this.logger.error('Failed to start WebSocket server', error instanceof Error ? error : undefined);
      throw error;
    }
  }

  /**
   * Stop the WebSocket server
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    try {
      // Stop heartbeat
      if (this.heartbeatInterval) {
        clearInterval(this.heartbeatInterval);
        this.heartbeatInterval = undefined;
      }

      // Close all client connections
      for (const client of this.clients.values()) {
        client.socket.close(1000, 'Server shutting down');
      }
      this.clients.clear();

      // Close server
      if (this.server) {
        await new Promise<void>((resolve, reject) => {
          this.server!.close((error) => {
            if (error) {
              reject(error);
            } else {
              resolve();
            }
          });
        });
        this.server = undefined;
      }

      this.isRunning = false;
      
      this.logger.info('WebSocket server stopped');
      this.emit('stopped');
    } catch (error) {
      this.logger.error('Error stopping WebSocket server', error instanceof Error ? error : undefined);
      throw error;
    }
  }

  /**
   * Send a protocol message to a specific client or all clients
   */
  async sendMessage(message: ProtocolMessage, targetClientId?: string): Promise<void> {
    try {
      const messageData = JSON.stringify(message);
      
      if (targetClientId) {
        const client = this.clients.get(targetClientId);
        if (client && client.socket.readyState === WebSocket.OPEN) {
          client.socket.send(messageData);
          client.lastActivity = new Date();
          
          this.logger.debug('Message sent to client', {
            clientId: targetClientId,
            messageId: message.id,
            type: message.type,
          });
        } else {
          this.logger.warn('Target client not found or not connected', { targetClientId });
        }
      } else {
        // Broadcast to all connected clients
        let sentCount = 0;
        for (const [clientId, client] of this.clients.entries()) {
          if (client.socket.readyState === WebSocket.OPEN) {
            client.socket.send(messageData);
            client.lastActivity = new Date();
            sentCount++;
          }
        }
        
        this.logger.debug('Message broadcast to clients', {
          messageId: message.id,
          type: message.type,
          clientCount: sentCount,
        });
      }
    } catch (error) {
      this.logger.error('Failed to send WebSocket message', error instanceof Error ? error : undefined, {
        messageId: message.id,
        targetClientId,
      });
      throw error;
    }
  }

  /**
   * Send a notification to subscribed clients
   */
  async sendNotification(notification: NotificationPayload, targetClientId?: string): Promise<void> {
    const message: ProtocolMessage = {
      id: this.generateMessageId(),
      type: 'notification',
      method: 'notification',
      params: {
        type: notification.type,
        data: notification.data,
        timestamp: notification.timestamp,
        source: notification.source,
        priority: notification.priority,
      },
      timestamp: new Date(),
    };

    await this.sendMessage(message, targetClientId);
  }

  /**
   * Get connected client information
   */
  getClients(): WebSocketClient[] {
    return Array.from(this.clients.values());
  }

  /**
   * Get client by ID
   */
  getClient(clientId: string): WebSocketClient | undefined {
    return this.clients.get(clientId);
  }

  /**
   * Check if transport is running
   */
  isActive(): boolean {
    return this.isRunning;
  }

  /**
   * Get connection statistics
   */
  getStats(): {
    isRunning: boolean;
    connectedClients: number;
    totalConnections: number;
    port: number;
    host: string;
  } {
    return {
      isRunning: this.isRunning,
      connectedClients: this.clients.size,
      totalConnections: this.clients.size, // Could track historical total
      port: this.options.port,
      host: this.options.host,
    };
  }

  /**
   * Setup WebSocket server event handlers
   */
  private setupServerEventHandlers(): void {
    if (!this.server) {
      return;
    }

    this.server.on('connection', (socket, request) => {
      this.handleNewConnection(socket, request);
    });

    this.server.on('error', (error) => {
      this.logger.error('WebSocket server error', error);
      this.emit('error', error);
    });

    this.server.on('listening', () => {
      this.logger.info('WebSocket server listening', {
        port: this.options.port,
        host: this.options.host,
      });
    });
  }

  /**
   * Handle new WebSocket connection
   */
  private handleNewConnection(socket: WebSocket, request: any): void {
    // Check connection limit
    if (this.clients.size >= this.options.maxConnections) {
      this.logger.warn('Connection limit reached, rejecting new connection');
      socket.close(1013, 'Server overloaded');
      return;
    }

    const clientId = this.generateClientId();
    const client: WebSocketClient = {
      id: clientId,
      socket,
      isActive: true,
      connectedAt: new Date(),
      lastActivity: new Date(),
      subscriptions: new Set(),
      metadata: {
        userAgent: request.headers['user-agent'],
        remoteAddress: request.socket.remoteAddress,
      },
    };

    this.clients.set(clientId, client);
    
    this.logger.info('New WebSocket client connected', {
      clientId,
      remoteAddress: client.metadata.remoteAddress,
      totalClients: this.clients.size,
    });

    this.setupClientEventHandlers(client);
    this.emit('clientConnected', client);
  }

  /**
   * Setup event handlers for a WebSocket client
   */
  private setupClientEventHandlers(client: WebSocketClient): void {
    const { socket, id: clientId } = client;

    socket.on('message', (data) => {
      this.handleClientMessage(client, data);
    });

    socket.on('close', (code, reason) => {
      this.handleClientDisconnect(client, code, reason);
    });

    socket.on('error', (error) => {
      this.logger.error('WebSocket client error', error, { clientId });
      this.emit('clientError', client, error);
    });

    socket.on('pong', () => {
      client.lastPong = new Date();
      client.lastActivity = new Date();
    });
  }

  /**
   * Handle message from WebSocket client
   */
  private handleClientMessage(client: WebSocketClient, data: any): void {
    try {
      client.lastActivity = new Date();
      
      const message = JSON.parse(data.toString()) as ProtocolMessage;
      
      this.logger.debug('Received message from client', {
        clientId: client.id,
        messageId: message.id,
        type: message.type,
        method: message.method,
      });

      this.emit('messageReceived', client, message);
    } catch (error) {
      this.logger.error('Failed to parse client message', error instanceof Error ? error : undefined, {
        clientId: client.id,
      });
      
      // Send error response
      const errorMessage: ProtocolMessage = {
        id: this.generateMessageId(),
        type: 'error',
        error: {
          code: -32700,
          message: 'Parse error',
          data: 'Invalid JSON',
        },
        timestamp: new Date(),
      };
      
      client.socket.send(JSON.stringify(errorMessage));
    }
  }

  /**
   * Handle client disconnect
   */
  private handleClientDisconnect(client: WebSocketClient, code: number, reason: Buffer): void {
    this.clients.delete(client.id);
    client.isActive = false;
    
    this.logger.info('WebSocket client disconnected', {
      clientId: client.id,
      code,
      reason: reason.toString(),
      totalClients: this.clients.size,
    });

    this.emit('clientDisconnected', client, code, reason);
  }

  /**
   * Start heartbeat mechanism
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      this.performHeartbeat();
    }, this.options.heartbeatInterval);
  }

  /**
   * Perform heartbeat check
   */
  private performHeartbeat(): void {
    const now = new Date();
    const staleThreshold = this.options.heartbeatInterval * 2;

    for (const [clientId, client] of this.clients.entries()) {
      if (client.socket.readyState === WebSocket.OPEN) {
        const timeSinceLastActivity = now.getTime() - client.lastActivity.getTime();
        
        if (timeSinceLastActivity > staleThreshold) {
          this.logger.warn('Client connection stale, terminating', {
            clientId,
            timeSinceLastActivity,
          });
          client.socket.terminate();
        } else {
          // Send ping
          client.socket.ping();
          client.lastPing = now;
        }
      }
    }
  }

  /**
   * Generate unique client ID
   */
  private generateClientId(): string {
    return `ws_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate unique message ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
