import { z } from 'zod';
import type { SupabasePlatform } from '../platform/types.js';
import { injectableTool } from './util.js';

export type EdgeFunctionToolsOptions = {
  platform: SupabasePlatform;
  projectId?: string;
};

export function getEssentialEdgeFunctionTools({
  platform,
  projectId,
}: EdgeFunctionToolsOptions) {
  const project_id = projectId;

  return {
    list_edge_functions: injectableTool({
      description: 'Lists all Edge Functions in a Supabase project.',
      parameters: z.object({
        project_id: z.string(),
      }),
      inject: { project_id },
      execute: async ({ project_id }) => {
        return await platform.listEdgeFunctions(project_id);
      },
    }),

    deploy_edge_function: injectableTool({
      description: 'Deploys an Edge Function to a Supabase project. If the function already exists, this will create a new version.',
      parameters: z.object({
        project_id: z.string(),
        function_name: z
          .string()
          .min(1, 'Function name cannot be empty')
          .max(63, 'Function name cannot exceed 63 characters')
          .regex(/^[a-z0-9]([a-z0-9-]*[a-z0-9])?$/, 'Function name must be lowercase alphanumeric with hyphens')
          .describe('Edge function name'),
        files: z
          .array(
            z.object({
              name: z
                .string()
                .min(1, 'File name cannot be empty')
                .max(255, 'File name cannot exceed 255 characters')
                .regex(/^[\w\/.~-]+$/, 'Invalid file name format')
                .describe('File name/path'),
              content: z
                .string()
                .max(1048576, 'File content cannot exceed 1MB')
                .describe('File content'),
            })
          )
          .min(1, 'Must provide at least one file')
          .max(50, 'Cannot upload more than 50 files at once')
          .describe('Files to upload including entrypoint and dependencies'),
        entrypoint_path: z
          .string()
          .min(1, 'Entrypoint path cannot be empty')
          .max(255, 'Entrypoint path cannot exceed 255 characters')
          .regex(/^[\w\/.~-]+\.(ts|js)$/, 'Entrypoint must be a TypeScript or JavaScript file')
          .default('index.ts')
          .describe('Edge function entrypoint file path'),
        import_map_path: z
          .string()
          .max(255, 'Import map path cannot exceed 255 characters')
          .optional()
          .describe('Import map file path'),
      }),
      inject: { project_id },
      execute: async ({ project_id, function_name, files, entrypoint_path, import_map_path }) => {
        // Validate that entrypoint exists in files
        const entrypointExists = files.some(f => f.name === entrypoint_path);
        if (!entrypointExists) {
          throw new Error(`Entrypoint file '${entrypoint_path}' not found in uploaded files`);
        }

        // Validate import map if provided
        if (import_map_path) {
          const importMapExists = files.some(f => f.name === import_map_path);
          if (!importMapExists) {
            throw new Error(`Import map file '${import_map_path}' not found in uploaded files`);
          }
        }

        return await platform.deployEdgeFunction(project_id, {
          name: function_name,
          files,
          entrypoint_path,
          import_map_path,
        });
      },
    }),
  };
} 