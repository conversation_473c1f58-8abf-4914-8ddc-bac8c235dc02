/**
 * Universal error handling for all MCP tools
 * 
 * This module provides comprehensive error handling that extends the CRUD error handling
 * pattern to all MCP tools, ensuring consistent error responses, classification, and
 * user-friendly error messages across the entire tool ecosystem.
 */

import { contextLogger, RequestTracker, type LogContext } from '../utils/logger.js';
import {
  McpServerError,
  DatabaseError,
  ValidationError,
  ToolError,
  TimeoutError,
  RetryableError,
  ErrorCode,
  ErrorClassifier,
  ErrorRecovery,
  ErrorReporter,
  RetryManager,
} from '../utils/errors.js';
import { 
  ErrorLoggingValidator, 
  createErrorLoggingValidator,
  extractValidationErrors,
  type ValidationErrorDetail,
  type ErrorLoggingContext 
} from '../utils/error-logging-validator.js';
import { z } from 'zod';

export interface ToolErrorContext extends LogContext {
  toolName: string;
  operation: string;
  category: 'CRUD' | 'DDL' | 'DATABASE' | 'JSON' | 'EDGE_FUNCTION' | 'MONITORING' | 'DEVELOPMENT' | 'TRANSACTION' | 'CONSTRAINT' | 'DOCS';
  parameters?: Record<string, unknown>;
  projectId?: string;
  schema?: string;
  table?: string;
  columns?: string[];
  recordCount?: number;
  query?: string;
  validation?: {
    field: string;
    error: string;
    value?: any;
  }[];
}

export class UniversalErrorHandler {
  private logger = contextLogger.child({ component: 'UniversalErrorHandler' });
  private errorLoggingValidator: ErrorLoggingValidator;
  private requestId?: string;

  constructor(requestId?: string) {
    this.requestId = requestId || RequestTracker.generateRequestId();
    this.errorLoggingValidator = createErrorLoggingValidator(this.requestId);
  }

  /**
   * Handle and classify MCP tool errors with full context
   */
  handleError(error: unknown, context: ToolErrorContext): never {
    const enrichedContext = {
      ...context,
      requestId: this.requestId,
      timestamp: Date.now(),
    };

    // Enhanced error logging with validation details
    this.logErrorWithEnhancedContext(error, enrichedContext);

    // Ensure validation details are included for Zod errors
    if (error instanceof z.ZodError) {
      // Use existing validation errors if provided, otherwise extract from ZodError
      enrichedContext.validation = enrichedContext.validation || extractValidationErrors(error);
    }

    // DEBUG: Log what we're passing to the logger (in test mode only)
    if (process.env.NODE_ENV === 'test') {
      console.log('DEBUG: enrichedContext.validation:', enrichedContext.validation);
      console.log('DEBUG: error type:', error?.constructor?.name);
    }

    // Log the error with full context (existing behavior)
    this.logger.error(`${context.toolName} operation failed`, error as Error, enrichedContext);

    // Create enhanced error based on type
    if (error instanceof McpServerError) {
      // Already a structured error, add tool context
      const enhancedError = new McpServerError(
        error.code,
        error.message,
        error.statusCode,
        {
          requestId: error.requestId || this.requestId,
          context: { ...error.context, ...enrichedContext },
          cause: error.cause,
        }
      );
      throw enhancedError;
    }

    // Handle validation errors
    if (this.isValidationError(error, context)) {
      throw this.createValidationError(error, enrichedContext);
    }

    // Handle database/platform errors
    if (this.isDatabaseError(error)) {
      throw this.createDatabaseError(error, enrichedContext);
    }

    // Handle timeout errors
    if (this.isTimeoutError(error)) {
      throw this.createTimeoutError(error, enrichedContext);
    }

    // Handle read-only mode errors
    if (this.isReadOnlyError(error, context)) {
      throw this.createReadOnlyError(error, enrichedContext);
    }

    // Default to tool execution error
    throw new ToolError(
      this.extractErrorMessage(error),
      ErrorCode.TOOL_EXECUTION_ERROR,
      {
        requestId: this.requestId,
        context: enrichedContext,
        cause: error instanceof Error ? error : undefined,
      }
    );
  }

  /**
   * Execute operation with retry logic and enhanced error handling
   */
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    context: ToolErrorContext,
    options: {
      maxRetries?: number;
      baseDelay?: number;
      maxDelay?: number;
      shouldRetry?: (error: any, attempt: number) => boolean;
    } = {}
  ): Promise<T> {
    const {
      maxRetries = 2,
      baseDelay = 1000,
      maxDelay = 10000,
      shouldRetry = (error, attempt) => this.shouldRetryOperation(error, context, attempt),
    } = options;

    return await RetryManager.executeWithRetry(operation, {
      maxRetries,
      baseDelay,
      maxDelay,
      shouldRetry,
      onRetry: (error, attempt, delay) => {
        // Enhanced retry logging
        this.logRetryAttempt(error, context, {
          attempt,
          maxAttempts: maxRetries,
          delay,
          reason: this.extractErrorMessage(error)
        });

        // Existing retry logging
        this.logger.warn(`Retrying ${context.toolName} operation`, {
          attempt,
          delay,
          error: this.extractErrorMessage(error),
          context,
        });
      },
      requestId: this.requestId,
    });
  }

  /**
   * Create user-friendly error message with context and suggestions
   */
  createUserFriendlyMessage(error: unknown, context: ToolErrorContext): string {
    const baseMessage = this.extractErrorMessage(error);
    const operationContext = this.getOperationContext(context);
    
    return `${operationContext}: ${baseMessage}`;
  }

  /**
   * Validate operation parameters before execution
   */
  validateOperation(context: ToolErrorContext): void {
    if (!context.toolName) {
      throw new ValidationError('Tool name is required', {
        requestId: this.requestId,
        context: { component: 'TOOL_VALIDATOR' },
      });
    }

    if (!context.operation) {
      throw new ValidationError('Operation name is required', {
        requestId: this.requestId,
        context: { component: 'TOOL_VALIDATOR' },
      });
    }

    // Category-specific validations
    if (context.category === 'DDL' && context.operation !== 'LIST_HISTORY' && context.operation !== 'VALIDATE_PERMISSIONS') {
      if (!context.schema && !context.table) {
        throw new ValidationError('Schema or table context required for DDL operations', {
          requestId: this.requestId,
          context: { component: 'DDL_VALIDATOR', operation: context.operation },
        });
      }
    }

    if (context.category === 'CRUD' && ['UPDATE', 'DELETE'].includes(context.operation)) {
      // For CRUD operations, WHERE clause requirements are handled in the CRUD-specific handler
    }
  }

  /**
   * Log successful operation with metrics
   */
  logSuccess(context: ToolErrorContext, result: any, duration?: number): void {
    // Enhanced success logging with detailed metrics
    this.errorLoggingValidator.logOperationSuccess(
      this.convertToErrorLoggingContext(context),
      result,
      { 
        duration,
        recordCount: context.recordCount,
        queryCount: 1 // Assumption: most operations involve 1 query
      }
    );

    // Existing logging behavior
    this.logger.info(`${context.toolName} operation completed successfully`, {
      operation: context.operation,
      category: context.category,
      duration,
      recordCount: context.recordCount,
      requestId: this.requestId,
      resultSize: result && typeof result === 'object' ? Object.keys(result).length : undefined,
    });
  }

  /**
   * Create comprehensive error report for debugging
   */
  createErrorReport(error: unknown, context: ToolErrorContext): object {
    return ErrorReporter.createErrorReport(error, {
      ...context,
      requestId: this.requestId,
      toolCategory: context.category,
      suggestions: this.getToolRecoverySuggestions(error, context),
    });
  }

  /**
   * Check if error indicates validation failure
   */
  private isValidationError(error: unknown, context: ToolErrorContext): boolean {
    if (error instanceof ValidationError) return true;
    
    const message = this.extractErrorMessage(error);
    const validationIndicators = [
      'validation failed',
      'invalid parameter',
      'required field',
      'must be',
      'expected',
      'schema validation',
      'zod',
    ];
    
    return validationIndicators.some(indicator => 
      message.toLowerCase().includes(indicator.toLowerCase())
    );
  }

  /**
   * Check if error is database-related
   */
  private isDatabaseError(error: unknown): boolean {
    return ErrorClassifier.classifySupabaseError(error).code !== ErrorCode.INTERNAL_ERROR;
  }

  /**
   * Check if error indicates timeout
   */
  private isTimeoutError(error: unknown): boolean {
    if (error instanceof TimeoutError) return true;
    
    const message = this.extractErrorMessage(error);
    return message.toLowerCase().includes('timeout') || 
           message.toLowerCase().includes('timed out');
  }

  /**
   * Check if error is due to read-only mode
   */
  private isReadOnlyError(error: unknown, context: ToolErrorContext): boolean {
    const message = this.extractErrorMessage(error);
    return message.toLowerCase().includes('read-only') ||
           message.toLowerCase().includes('cannot update') ||
           message.toLowerCase().includes('cannot create') ||
           message.toLowerCase().includes('cannot delete');
  }

  /**
   * Create validation error with enhanced context
   */
  private createValidationError(error: unknown, context: ToolErrorContext): ValidationError {
    return new ValidationError(
      `${context.toolName} validation failed: ${this.extractErrorMessage(error)}`,
      {
        requestId: this.requestId,
        context,
        cause: error instanceof Error ? error : undefined,
      }
    );
  }

  /**
   * Create database error with enhanced context
   */
  private createDatabaseError(error: unknown, context: ToolErrorContext): DatabaseError {
    return ErrorClassifier.createDatabaseError(error, context, this.requestId);
  }

  /**
   * Create timeout error with enhanced context
   */
  private createTimeoutError(error: unknown, context: ToolErrorContext): TimeoutError {
    return new TimeoutError(
      `${context.toolName} operation timed out: ${this.extractErrorMessage(error)}`,
      'operation',
      undefined,
      {
        requestId: this.requestId,
        context,
        cause: error instanceof Error ? error : undefined,
      }
    );
  }

  /**
   * Create read-only error with enhanced context
   */
  private createReadOnlyError(error: unknown, context: ToolErrorContext): ToolError {
    return new ToolError(
      `${context.toolName} operation not allowed: ${this.extractErrorMessage(error)}`,
      ErrorCode.PERMISSION_DENIED,
      {
        requestId: this.requestId,
        context,
        cause: error instanceof Error ? error : undefined,
      }
    );
  }

  /**
   * Determine if operation should be retried
   */
  private shouldRetryOperation(error: unknown, context: ToolErrorContext, attempt: number): boolean {
    // Don't retry after too many attempts
    if (attempt >= 3) return false;

    // Don't retry validation errors
    if (this.isValidationError(error, context)) return false;

    // Don't retry read-only errors
    if (this.isReadOnlyError(error, context)) return false;

    // Category-specific retry logic
    switch (context.category) {
      case 'CRUD':
      case 'DDL':
      case 'DATABASE':
        // Don't retry constraint violations
        if (error instanceof DatabaseError) {
          const nonRetryableCodes = [
            ErrorCode.UNIQUE_VIOLATION,
            ErrorCode.FOREIGN_KEY_VIOLATION,
            ErrorCode.CHECK_VIOLATION,
            ErrorCode.NOT_NULL_VIOLATION,
            ErrorCode.PERMISSION_DENIED,
            ErrorCode.TABLE_NOT_FOUND,
            ErrorCode.COLUMN_NOT_FOUND,
          ];
          
          if (nonRetryableCodes.includes(error.code)) return false;
        }
        break;
      
      case 'JSON':
        // Don't retry JSON syntax errors
        if (this.extractErrorMessage(error).toLowerCase().includes('json')) return false;
        break;
      
      case 'EDGE_FUNCTION':
        // Don't retry deployment errors
        if (this.extractErrorMessage(error).toLowerCase().includes('deployment')) return false;
        break;
    }

    // Retry network errors, timeouts, and retryable database errors
    return this.isTimeoutError(error) || 
           error instanceof RetryableError ||
           this.extractErrorMessage(error).includes('connection') ||
           this.extractErrorMessage(error).includes('network');
  }

  /**
   * Generate operation context description
   */
  private getOperationContext(context: ToolErrorContext): string {
    const table = context.table ? `${context.schema || 'public'}.${context.table}` : '';
    
    switch (context.category) {
      case 'CRUD':
        return `${context.operation} operation on ${table || 'table'}`;
      case 'DDL':
        return `${context.operation} DDL operation on ${table || 'database object'}`;
      case 'DATABASE':
        return `${context.operation} database operation`;
      case 'JSON':
        return `${context.operation} JSON operation`;
      case 'EDGE_FUNCTION':
        return `${context.operation} edge function operation`;
      case 'MONITORING':
        return `${context.operation} monitoring operation`;
      case 'DEVELOPMENT':
        return `${context.operation} development operation`;
      case 'TRANSACTION':
        return `${context.operation} transaction operation`;
      case 'CONSTRAINT':
        return `${context.operation} constraint operation`;
      case 'DOCS':
        return `${context.operation} documentation operation`;
      default:
        return `${context.operation} operation`;
    }
  }

  /**
   * Get tool-specific recovery suggestions
   */
  private getToolRecoverySuggestions(error: unknown, context: ToolErrorContext): string[] {
    const suggestions = ErrorRecovery.getRecoverySuggestions(error);
    
    // Add category-specific suggestions
    switch (context.category) {
      case 'DDL':
        if (this.isValidationError(error, context)) {
          suggestions.push('Verify table and column names follow PostgreSQL naming conventions');
          suggestions.push('Check that the database object exists before modification');
          suggestions.push('Ensure proper permissions for DDL operations');
        }
        break;
      
      case 'JSON':
        if (this.extractErrorMessage(error).toLowerCase().includes('json')) {
          suggestions.push('Validate JSON syntax using a JSON validator');
          suggestions.push('Check for common JSON errors: missing quotes, trailing commas');
          suggestions.push('Ensure proper escaping of special characters');
        }
        break;
      
      case 'EDGE_FUNCTION':
        suggestions.push('Verify function code syntax and imports');
        suggestions.push('Check that all required files are included');
        suggestions.push('Ensure function name follows naming conventions');
        break;
      
      case 'MONITORING':
        suggestions.push('Check service status and availability');
        suggestions.push('Verify monitoring permissions and access');
        break;
      
      case 'DEVELOPMENT':
        suggestions.push('Verify project ID and credentials');
        suggestions.push('Check API endpoint availability');
        break;
    }

    return suggestions;
  }

  /**
   * Extract error message from various error types
   */
  private extractErrorMessage(error: unknown): string {
    if (error instanceof Error) {
      return error.message;
    }
    return String(error);
  }

  /**
   * Enhanced error logging with detailed validation context
   */
  private logErrorWithEnhancedContext(error: unknown, context: ToolErrorContext): void {
    const errorLoggingContext = this.convertToErrorLoggingContext(context);

    if (error instanceof z.ZodError) {
      // Log Zod validation errors with detailed field information
      this.errorLoggingValidator.logValidationError(error, {
        ...errorLoggingContext,
        validationErrors: extractValidationErrors(error)
      });
    } else if (this.isDatabaseError(error)) {
      // Log database errors with query context
      this.errorLoggingValidator.logDatabaseError(error as Error, errorLoggingContext, {
        sql: context.query,
        affectedRows: context.recordCount,
        executionTime: context.duration
      });
    } else if (this.isValidationError(error, context)) {
      // Log general validation errors
      this.errorLoggingValidator.logValidationError(error as Error, {
        ...errorLoggingContext,
        validationErrors: context.validation || extractValidationErrors(error)
      });
    } else if (this.isSecurityRelatedError(error, context)) {
      // Log security errors with elevated attention
      this.errorLoggingValidator.logSecurityError(error as Error, errorLoggingContext, {
        attemptedOperation: context.operation,
        resourceAccessed: context.table ? `${context.schema || 'public'}.${context.table}` : context.schema,
        riskLevel: this.assessSecurityRiskLevel(error, context)
      });
    } else {
      // Log general tool errors
      this.errorLoggingValidator.logOperationSuccess(errorLoggingContext, null, {
        duration: context.duration,
        recordCount: context.recordCount
      });
    }
  }

  /**
   * Log retry attempts with enhanced context
   */
  logRetryAttempt(error: unknown, context: ToolErrorContext, retryInfo: {
    attempt: number;
    maxAttempts: number;
    delay: number;
    reason: string;
  }): void {
    this.errorLoggingValidator.logRetryAttempt(
      error as Error,
      this.convertToErrorLoggingContext(context),
      retryInfo
    );
  }

  /**
   * Convert ToolErrorContext to ErrorLoggingContext
   */
  private convertToErrorLoggingContext(context: ToolErrorContext): ErrorLoggingContext {
    return {
      ...context,
      requestId: this.requestId,
      errorClassification: this.classifyErrorType(context)
    };
  }

  /**
   * Classify error type for logging purposes
   */
  private classifyErrorType(context: ToolErrorContext): 'validation' | 'database' | 'timeout' | 'tool' | 'security' | 'configuration' {
    if (context.validation && context.validation.length > 0) return 'validation';
    if (context.category === 'DATABASE' || context.category === 'CRUD' || context.category === 'DDL') return 'database';
    if (context.operation.includes('TIMEOUT') || context.operation.includes('RETRY')) return 'timeout';
    if (context.operation.includes('AUTH') || context.operation.includes('PERMISSION')) return 'security';
    if (context.operation.includes('CONFIG') || context.operation.includes('SETUP')) return 'configuration';
    return 'tool';
  }

  /**
   * Check if error is security-related
   */
  private isSecurityRelatedError(error: unknown, context: ToolErrorContext): boolean {
    const message = this.extractErrorMessage(error).toLowerCase();
    const securityKeywords = [
      'permission denied',
      'access denied',
      'unauthorized',
      'forbidden',
      'authentication',
      'invalid credentials',
      'security',
      'privilege'
    ];

    return securityKeywords.some(keyword => message.includes(keyword)) ||
           context.operation.includes('AUTH') ||
           context.operation.includes('PERMISSION');
  }

  /**
   * Assess security risk level of error
   */
  private assessSecurityRiskLevel(error: unknown, context: ToolErrorContext): 'low' | 'medium' | 'high' | 'critical' {
    const message = this.extractErrorMessage(error).toLowerCase();

    if (message.includes('sql injection') || message.includes('malicious')) return 'critical';
    if (message.includes('permission denied') || message.includes('unauthorized')) return 'high';
    if (message.includes('authentication') || message.includes('access')) return 'medium';
    return 'low';
  }

  /**
   * Create validation error summary for debugging
   */
  createValidationErrorSummary(error: z.ZodError): {
    summary: string;
    fieldErrors: ValidationErrorDetail[];
    errorCount: number;
    affectedFields: string[];
  } {
    return this.errorLoggingValidator.createValidationErrorSummary(error);
  }
}

/**
 * Factory function to create universal error handlers with request context
 */
export function createUniversalErrorHandler(requestId?: string): UniversalErrorHandler {
  return new UniversalErrorHandler(requestId);
}

/**
 * Utility functions for common MCP tool error scenarios
 */
export class ToolErrorUtils {
  /**
   * Create a parameter validation error
   */
  static createParameterError(paramName: string, expectedType: string, actualValue: any, requestId?: string): ValidationError {
    return new ValidationError(
      `Parameter '${paramName}' validation failed: expected ${expectedType}, got ${typeof actualValue}`,
      {
        requestId,
        context: { 
          component: 'PARAMETER_VALIDATOR',
          paramName,
          expectedType,
          actualType: typeof actualValue,
        },
      }
    );
  }

  /**
   * Create a tool execution error with context
   */
  static createToolExecutionError(
    toolName: string,
    operation: string,
    error: unknown,
    requestId?: string
  ): ToolError {
    return new ToolError(
      `${toolName} ${operation} failed: ${error instanceof Error ? error.message : String(error)}`,
      ErrorCode.TOOL_EXECUTION_ERROR,
      {
        requestId,
        context: { toolName, operation },
        cause: error instanceof Error ? error : undefined,
      }
    );
  }

  /**
   * Create a configuration error for tools
   */
  static createConfigurationError(
    toolName: string,
    configIssue: string,
    requestId?: string
  ): ToolError {
    return new ToolError(
      `${toolName} configuration error: ${configIssue}`,
      ErrorCode.CONFIG_ERROR,
      {
        requestId,
        context: { toolName, configIssue },
      }
    );
  }
} 