import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { getTransactionManagementTools } from '../src/tools/transaction-management-tools.js';
import { createLocalSupabasePlatform } from '../src/platform/local-platform.js';
import { getLocalConfig } from '../src/config/index.js';
import type { SupabasePlatform } from '../src/platform/types.js';

describe('Transaction Management Tools Integration', () => {
  let platform: SupabasePlatform;
  let transactionTools: ReturnType<typeof getTransactionManagementTools>;

  beforeAll(async () => {
    // Setup local platform for testing
    const config = getLocalConfig();
    platform = createLocalSupabasePlatform(config);
    
    // Initialize transaction management tools
    transactionTools = getTransactionManagementTools({
      platform,
      projectId: 'test-project',
      readOnly: false,
    });
  });

  afterAll(async () => {
    // Cleanup if needed
    if (platform && typeof platform.close === 'function') {
      await platform.close();
    }
  });

  describe('Basic Transaction Operations', () => {
    it('should begin a transaction successfully', async () => {
      const result = await transactionTools.begin_transaction.execute({
        project_id: 'test-project',
        isolation_level: 'READ_COMMITTED',
        timeout_seconds: 300,
      });

      expect(result.success).toBe(true);
      expect(result.transaction_id).toBeTruthy();
      expect(result.isolation_level).toBe('READ_COMMITTED');
      expect(result.timeout_seconds).toBe(300);
      expect(result.started_at).toBeTruthy();
      expect(result.error_message).toBeNull();

      // Clean up - rollback the transaction
      if (result.transaction_id) {
        await transactionTools.rollback_transaction.execute({
          project_id: 'test-project',
          transaction_id: result.transaction_id,
        });
      }
    });

    it('should commit a transaction successfully', async () => {
      // Begin transaction
      const beginResult = await transactionTools.begin_transaction.execute({
        project_id: 'test-project',
      });

      expect(beginResult.success).toBe(true);
      expect(beginResult.transaction_id).toBeTruthy();

      // Commit transaction
      const commitResult = await transactionTools.commit_transaction.execute({
        project_id: 'test-project',
        transaction_id: beginResult.transaction_id!,
      });

      expect(commitResult.success).toBe(true);
      expect(commitResult.status).toBe('COMMITTED');
      expect(commitResult.duration_ms).toBeGreaterThan(0);
      expect(commitResult.error_message).toBeNull();
    });

    it('should rollback a transaction successfully', async () => {
      // Begin transaction
      const beginResult = await transactionTools.begin_transaction.execute({
        project_id: 'test-project',
      });

      expect(beginResult.success).toBe(true);
      expect(beginResult.transaction_id).toBeTruthy();

      // Rollback transaction
      const rollbackResult = await transactionTools.rollback_transaction.execute({
        project_id: 'test-project',
        transaction_id: beginResult.transaction_id!,
      });

      expect(rollbackResult.success).toBe(true);
      expect(rollbackResult.status).toBe('ROLLED_BACK');
      expect(rollbackResult.duration_ms).toBeGreaterThan(0);
      expect(rollbackResult.error_message).toBeNull();
    });
  });

  describe('Savepoint Management', () => {
    it('should create and manage savepoints', async () => {
      // Begin transaction
      const beginResult = await transactionTools.begin_transaction.execute({
        project_id: 'test-project',
      });

      expect(beginResult.success).toBe(true);
      const transactionId = beginResult.transaction_id!;

      // Create savepoint
      const savepointResult = await transactionTools.create_savepoint.execute({
        project_id: 'test-project',
        transaction_id: transactionId,
        savepoint_name: 'test_savepoint',
      });

      expect(savepointResult.success).toBe(true);
      expect(savepointResult.savepoint_name).toBe('test_savepoint');
      expect(savepointResult.created_at).toBeTruthy();

      // Check transaction status includes savepoint
      const statusResult = await transactionTools.get_transaction_status.execute({
        project_id: 'test-project',
        transaction_id: transactionId,
      });

      expect(statusResult.success).toBe(true);
      expect(statusResult.savepoints).toContain('test_savepoint');

      // Clean up
      await transactionTools.rollback_transaction.execute({
        project_id: 'test-project',
        transaction_id: transactionId,
      });
    });

    it('should rollback to savepoint', async () => {
      // Begin transaction
      const beginResult = await transactionTools.begin_transaction.execute({
        project_id: 'test-project',
      });

      const transactionId = beginResult.transaction_id!;

      // Create savepoint
      await transactionTools.create_savepoint.execute({
        project_id: 'test-project',
        transaction_id: transactionId,
        savepoint_name: 'rollback_test',
      });

      // Rollback to savepoint
      const rollbackResult = await transactionTools.rollback_to_savepoint.execute({
        project_id: 'test-project',
        transaction_id: transactionId,
        savepoint_name: 'rollback_test',
      });

      expect(rollbackResult.success).toBe(true);
      expect(rollbackResult.savepoint_name).toBe('rollback_test');
      expect(rollbackResult.rolled_back_at).toBeTruthy();

      // Clean up
      await transactionTools.rollback_transaction.execute({
        project_id: 'test-project',
        transaction_id: transactionId,
      });
    });

    it('should release savepoint', async () => {
      // Begin transaction
      const beginResult = await transactionTools.begin_transaction.execute({
        project_id: 'test-project',
      });

      const transactionId = beginResult.transaction_id!;

      // Create savepoint
      await transactionTools.create_savepoint.execute({
        project_id: 'test-project',
        transaction_id: transactionId,
        savepoint_name: 'release_test',
      });

      // Release savepoint
      const releaseResult = await transactionTools.release_savepoint.execute({
        project_id: 'test-project',
        transaction_id: transactionId,
        savepoint_name: 'release_test',
      });

      expect(releaseResult.success).toBe(true);
      expect(releaseResult.savepoint_name).toBe('release_test');
      expect(releaseResult.released_at).toBeTruthy();

      // Clean up
      await transactionTools.rollback_transaction.execute({
        project_id: 'test-project',
        transaction_id: transactionId,
      });
    });
  });

  describe('Transaction Monitoring', () => {
    it('should get transaction status', async () => {
      // Begin transaction
      const beginResult = await transactionTools.begin_transaction.execute({
        project_id: 'test-project',
        isolation_level: 'REPEATABLE_READ',
        timeout_seconds: 600,
      });

      const transactionId = beginResult.transaction_id!;

      // Get status
      const statusResult = await transactionTools.get_transaction_status.execute({
        project_id: 'test-project',
        transaction_id: transactionId,
      });

      expect(statusResult.success).toBe(true);
      expect(statusResult.status).toBe('ACTIVE');
      expect(statusResult.isolation_level).toBe('REPEATABLE_READ');
      expect(statusResult.timeout_seconds).toBe(600);
      expect(statusResult.started_at).toBeTruthy();
      expect(statusResult.duration_ms).toBeGreaterThan(0);

      // Clean up
      await transactionTools.rollback_transaction.execute({
        project_id: 'test-project',
        transaction_id: transactionId,
      });
    });

    it('should list active transactions', async () => {
      // Begin multiple transactions
      const tx1 = await transactionTools.begin_transaction.execute({
        project_id: 'test-project',
      });
      const tx2 = await transactionTools.begin_transaction.execute({
        project_id: 'test-project',
      });

      // List active transactions
      const listResult = await transactionTools.list_active_transactions.execute({
        project_id: 'test-project',
      });

      expect(listResult.success).toBe(true);
      expect(listResult.total_count).toBeGreaterThanOrEqual(2);
      expect(listResult.active_transactions).toHaveLength(listResult.total_count);

      // Verify transaction details
      const txIds = listResult.active_transactions.map(tx => tx.transaction_id);
      expect(txIds).toContain(tx1.transaction_id);
      expect(txIds).toContain(tx2.transaction_id);

      // Clean up
      if (tx1.transaction_id) {
        await transactionTools.rollback_transaction.execute({
          project_id: 'test-project',
          transaction_id: tx1.transaction_id,
        });
      }
      if (tx2.transaction_id) {
        await transactionTools.rollback_transaction.execute({
          project_id: 'test-project',
          transaction_id: tx2.transaction_id,
        });
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid transaction ID', async () => {
      const result = await transactionTools.get_transaction_status.execute({
        project_id: 'test-project',
        transaction_id: 'invalid-transaction-id',
      });

      expect(result.success).toBe(false);
      expect(result.error_message).toContain('not found');
    });

    it('should handle read-only mode restrictions', async () => {
      const readOnlyTools = getTransactionManagementTools({
        platform,
        projectId: 'test-project',
        readOnly: true,
      });

      const result = await readOnlyTools.begin_transaction.execute({
        project_id: 'test-project',
      });

      expect(result.success).toBe(false);
      expect(result.error_message).toContain('read-only mode');
    });
  });
});
