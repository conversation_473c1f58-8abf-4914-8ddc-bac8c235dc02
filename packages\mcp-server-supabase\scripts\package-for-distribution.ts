#!/usr/bin/env node

import { execSync } from 'child_process';
import { writeFileSync, mkdirSync, existsSync, rmSync } from 'fs';
import path from 'path';

const DIST_DIR = 'dist-package';
const PKG_NAME = '@local/supabase-mcp-server';

console.log('🚀 Creating distribution package...');

// Clean and create distribution directory
if (existsSync(DIST_DIR)) {
  console.log('🧹 Cleaning existing distribution directory...');
  rmSync(DIST_DIR, { recursive: true, force: true });
}
mkdirSync(DIST_DIR, { recursive: true });

// Build the project
console.log('🔨 Building project...');
try {
  execSync('npm run build', { stdio: 'inherit' });
} catch (error) {
  console.error('❌ Build failed:', error);
  process.exit(1);
}

// Create standalone package.json for distribution
const distributionPackage = {
  name: PKG_NAME,
  version: '1.0.0',
  description: 'Local Supabase MCP Server for Claude Desktop and Cursor IDE',
  bin: {
    'local-supabase-mcp': './dist/transports/stdio.cjs'
  },
  main: './dist/index.cjs',
  type: 'module',
  engines: {
    node: '>=18.0.0'
  },
  dependencies: {
    '@modelcontextprotocol/sdk': '^1.11.0',
    '@supabase/supabase-js': '^2.45.0',
    'zod': '^3.24.1',
    'pino': '^9.7.0',
    'ws': '^8.18.0',
    'common-tags': '^1.8.2'
  }
};

writeFileSync(
  path.join(DIST_DIR, 'package.json'),
  JSON.stringify(distributionPackage, null, 2)
);

// Copy built files
console.log('📦 Copying built files...');
try {
  execSync(`xcopy /E /I /Y dist ${DIST_DIR}\\dist`, { stdio: 'inherit' });
} catch (error) {
  // Fallback to robocopy if xcopy fails
  try {
    execSync(`robocopy dist ${DIST_DIR}\\dist /E`, { stdio: 'inherit' });
  } catch (robocopyError) {
    console.error('❌ Failed to copy files:', error);
    process.exit(1);
  }
}

// Create platform-specific startup scripts
const createStartupScript = (platform: 'linux' | 'windows') => {
  const ext = platform === 'windows' ? '.bat' : '.sh';
  const nodeCmd = platform === 'windows' ? 'node.exe' : 'node';
  
  const script = platform === 'windows' 
    ? `@echo off\n"${nodeCmd}" "%~dp0dist\\transports\\stdio.cjs" %*`
    : `#!/bin/bash\n"${nodeCmd}" "$(dirname "$0")/dist/transports/stdio.cjs" "$@"`;
    
  writeFileSync(path.join(DIST_DIR, `start-mcp-server${ext}`), script);
  
  console.log(`✅ Created ${platform} startup script: start-mcp-server${ext}`);
};

createStartupScript('linux');
createStartupScript('windows');

// Create README for distribution
const distributionReadme = `# Local Supabase MCP Server

## Quick Start

### Windows
\`\`\`cmd
start-mcp-server.bat --supabase-url="YOUR_URL" --anon-key="YOUR_KEY" --service-key="YOUR_SERVICE_KEY"
\`\`\`

### Linux/macOS
\`\`\`bash
./start-mcp-server.sh --supabase-url="YOUR_URL" --anon-key="YOUR_KEY" --service-key="YOUR_SERVICE_KEY"
\`\`\`

## Installation

1. Extract this package to your desired location
2. Ensure Node.js 18+ is installed
3. Run the appropriate startup script for your platform

## Configuration

Set these environment variables or use CLI arguments:
- SUPABASE_URL: Your Supabase instance URL
- SUPABASE_ANON_KEY: Anonymous key
- SUPABASE_SERVICE_ROLE_KEY: Service role key
- READ_ONLY: Set to "true" for read-only mode (optional)
`;

writeFileSync(path.join(DIST_DIR, 'README.md'), distributionReadme);

console.log('✅ Distribution package created in:', DIST_DIR);
console.log('📋 Package includes:');
console.log('   - Built MCP server files');
console.log('   - Platform-specific startup scripts');
console.log('   - Standalone package.json');
console.log('   - Installation README');