/**
 * Error Logging Validation Utilities
 * 
 * Enhanced error logging utilities that extend the existing logger framework
 * with specialized validation error handling, context enrichment, and debugging
 * capabilities for MCP tools.
 */

import { contextLogger, RequestTracker, type LogContext } from './logger.js';
import { z } from 'zod';

export interface ValidationErrorDetail {
  field: string;
  error: string;
  value?: any;
  path?: string[];
  code?: string;
}

export interface ErrorLoggingContext extends LogContext {
  toolName: string;
  operation: string;
  category: string;
  validationErrors?: ValidationErrorDetail[];
  parameters?: Record<string, unknown>;
  query?: string;
  schema?: string;
  table?: string;
  columns?: string[];
  recordCount?: number;
  duration?: number;
  retryAttempt?: number;
  errorCode?: string;
  errorClassification?: 'validation' | 'database' | 'timeout' | 'tool' | 'security' | 'configuration';
}

/**
 * Enhanced error logging validator with structured validation error capture
 */
export class ErrorLoggingValidator {
  private logger = contextLogger.child({ component: 'ErrorLoggingValidator' });
  private requestId: string;

  constructor(requestId?: string) {
    this.requestId = requestId || RequestTracker.generateRequestId();
  }

  /**
   * Log validation errors with detailed context and field-level information
   */
  logValidationError(
    error: z.ZodError | Error,
    context: ErrorLoggingContext
  ): void {
    const enrichedContext = this.enrichErrorContext(context);

    if (error instanceof z.ZodError) {
      const validationDetails = this.extractZodValidationDetails(error);
      
      this.logger.error('Validation error detected', {
        ...enrichedContext,
        validationErrors: validationDetails,
        errorType: 'ZOD_VALIDATION',
        fieldCount: validationDetails.length,
        message: this.formatValidationErrorMessage(validationDetails)
      });
    } else {
      this.logger.error('General validation error', {
        ...enrichedContext,
        errorType: 'GENERAL_VALIDATION',
        message: error.message,
        stack: error.stack
      });
    }
  }

  /**
   * Log database operation errors with query context
   */
  logDatabaseError(
    error: Error,
    context: ErrorLoggingContext,
    queryDetails?: {
      sql?: string;
      parameters?: any[];
      affectedRows?: number;
      executionTime?: number;
    }
  ): void {
    const enrichedContext = this.enrichErrorContext(context);

    this.logger.error('Database operation failed', {
      ...enrichedContext,
      errorType: 'DATABASE_ERROR',
      message: error.message,
      query: queryDetails?.sql,
      queryParameters: queryDetails?.parameters,
      affectedRows: queryDetails?.affectedRows,
      executionTime: queryDetails?.executionTime,
      stack: error.stack,
      databaseErrorCode: this.extractDatabaseErrorCode(error),
      isConnectionError: this.isDatabaseConnectionError(error),
      isConstraintViolation: this.isConstraintViolationError(error)
    });
  }

  /**
   * Log successful operations with performance metrics
   */
  logOperationSuccess(
    context: ErrorLoggingContext,
    result: any,
    metrics?: {
      duration?: number;
      recordCount?: number;
      queryCount?: number;
      cacheHits?: number;
    }
  ): void {
    const enrichedContext = this.enrichErrorContext(context);

    this.logger.info('Operation completed successfully', {
      ...enrichedContext,
      operationType: 'SUCCESS',
      duration: metrics?.duration,
      recordCount: metrics?.recordCount || context.recordCount,
      queryCount: metrics?.queryCount,
      cacheHits: metrics?.cacheHits,
      resultSize: this.calculateResultSize(result)
    });
  }

  /**
   * Log retry attempts with context
   */
  logRetryAttempt(
    error: Error,
    context: ErrorLoggingContext,
    retryInfo: {
      attempt: number;
      maxAttempts: number;
      delay: number;
      reason: string;
    }
  ): void {
    const enrichedContext = this.enrichErrorContext(context);

    this.logger.warn('Retrying operation after failure', {
      ...enrichedContext,
      operationType: 'RETRY',
      retryAttempt: retryInfo.attempt,
      maxRetries: retryInfo.maxAttempts,
      retryDelay: retryInfo.delay,
      retryReason: retryInfo.reason,
      errorMessage: error.message,
      isRetryable: retryInfo.attempt < retryInfo.maxAttempts
    });
  }

  /**
   * Log security-related errors with additional context
   */
  logSecurityError(
    error: Error,
    context: ErrorLoggingContext,
    securityDetails?: {
      attemptedOperation?: string;
      resourceAccessed?: string;
      userContext?: string;
      riskLevel?: 'low' | 'medium' | 'high' | 'critical';
    }
  ): void {
    const enrichedContext = this.enrichErrorContext(context);

    this.logger.error('Security error detected', {
      ...enrichedContext,
      errorType: 'SECURITY_ERROR',
      message: error.message,
      attemptedOperation: securityDetails?.attemptedOperation,
      resourceAccessed: securityDetails?.resourceAccessed,
      userContext: securityDetails?.userContext,
      riskLevel: securityDetails?.riskLevel || 'medium',
      requiresAttention: securityDetails?.riskLevel === 'high' || securityDetails?.riskLevel === 'critical'
    });
  }

  /**
   * Create structured validation error summary for debugging
   */
  createValidationErrorSummary(error: z.ZodError): {
    summary: string;
    fieldErrors: ValidationErrorDetail[];
    errorCount: number;
    affectedFields: string[];
  } {
    const validationDetails = this.extractZodValidationDetails(error);
    const affectedFields = [...new Set(validationDetails.map(detail => detail.field))];

    return {
      summary: this.formatValidationErrorMessage(validationDetails),
      fieldErrors: validationDetails,
      errorCount: validationDetails.length,
      affectedFields
    };
  }

  /**
   * Extract detailed validation information from Zod errors
   */
  private extractZodValidationDetails(error: z.ZodError): ValidationErrorDetail[] {
    return error.issues.map(issue => ({
      field: issue.path.join('.') || 'root',
      error: issue.message,
      value: 'received' in issue ? issue.received : undefined,
      path: issue.path,
      code: issue.code
    }));
  }

  /**
   * Format validation errors into a human-readable message
   */
  private formatValidationErrorMessage(details: ValidationErrorDetail[]): string {
    if (details.length === 0) return 'Unknown validation error';
    if (details.length === 1) return `${details[0].field}: ${details[0].error}`;
    
    const fieldCount = new Set(details.map(d => d.field)).size;
    return `Validation failed for ${fieldCount} field(s): ${details.map(d => d.field).join(', ')}`;
  }

  /**
   * Enrich error context with debugging information
   */
  private enrichErrorContext(context: ErrorLoggingContext): ErrorLoggingContext & {
    requestId: string;
    timestamp: number;
    component: string;
  } {
    return {
      ...context,
      requestId: this.requestId,
      timestamp: Date.now(),
      component: 'ErrorLoggingValidator'
    };
  }

  /**
   * Extract database-specific error codes
   */
  private extractDatabaseErrorCode(error: Error): string | undefined {
    // PostgreSQL error codes (e.g., 23505 for unique violation)
    const pgErrorMatch = error.message.match(/error code (\d{5})/);
    if (pgErrorMatch) return pgErrorMatch[1];

    // Common PostgreSQL errors by message pattern
    if (error.message.includes('duplicate key value violates unique constraint')) return '23505';
    if (error.message.includes('violates foreign key constraint')) return '23503';
    if (error.message.includes('violates not-null constraint')) return '23502';
    if (error.message.includes('violates check constraint')) return '23514';
    if (error.message.includes('connection refused')) return 'CONNECTION_REFUSED';
    if (error.message.includes('timeout')) return 'TIMEOUT';

    return undefined;
  }

  /**
   * Detect database connection errors
   */
  private isDatabaseConnectionError(error: Error): boolean {
    const connectionPatterns = [
      'connection refused',
      'connection terminated',
      'server closed the connection',
      'connect ECONNREFUSED',
      'timeout',
      'ENOTFOUND',
      'ETIMEDOUT'
    ];

    return connectionPatterns.some(pattern => 
      error.message.toLowerCase().includes(pattern.toLowerCase())
    );
  }

  /**
   * Detect constraint violation errors
   */
  private isConstraintViolationError(error: Error): boolean {
    const constraintPatterns = [
      'violates unique constraint',
      'violates foreign key constraint', 
      'violates not-null constraint',
      'violates check constraint',
      'duplicate key value'
    ];

    return constraintPatterns.some(pattern =>
      error.message.toLowerCase().includes(pattern.toLowerCase())
    );
  }

  /**
   * Calculate result size for logging
   */
  private calculateResultSize(result: any): number {
    if (!result) return 0;
    if (Array.isArray(result)) return result.length;
    if (typeof result === 'object' && result.data && Array.isArray(result.data)) {
      return result.data.length;
    }
    if (typeof result === 'object' && result.count !== undefined) {
      return result.count;
    }
    return 1;
  }
}

/**
 * Create validation error logging utility with request context
 */
export function createErrorLoggingValidator(requestId?: string): ErrorLoggingValidator {
  return new ErrorLoggingValidator(requestId);
}

/**
 * Helper function to extract validation errors from any error type
 */
export function extractValidationErrors(error: unknown): ValidationErrorDetail[] {
  if (error instanceof z.ZodError) {
    return error.issues.map(issue => ({
      field: issue.path.join('.') || 'root',
      error: issue.message,
      value: 'received' in issue ? issue.received : undefined,
      path: issue.path,
      code: issue.code
    }));
  }

  if (error instanceof Error) {
    return [{ 
      field: 'general', 
      error: error.message,
      value: undefined 
    }];
  }

  return [{ 
    field: 'unknown', 
    error: String(error),
    value: undefined 
  }];
}

/**
 * Schema for validating error logging context
 */
export const errorLoggingContextSchema = z.object({
  toolName: z.string().min(1),
  operation: z.string().min(1),
  category: z.enum(['CRUD', 'DDL', 'DATABASE', 'JSON', 'EDGE_FUNCTION', 'MONITORING', 'DEVELOPMENT', 'TRANSACTION', 'CONSTRAINT', 'DOCS']),
  validationErrors: z.array(z.object({
    field: z.string(),
    error: z.string(),
    value: z.any().optional(),
    path: z.array(z.string()).optional(),
    code: z.string().optional()
  })).optional(),
  parameters: z.record(z.unknown()).optional(),
  query: z.string().optional(),
  schema: z.string().optional(),
  table: z.string().optional(),
  columns: z.array(z.string()).optional(),
  recordCount: z.number().optional(),
  duration: z.number().optional(),
  retryAttempt: z.number().optional(),
  errorCode: z.string().optional(),
  errorClassification: z.enum(['validation', 'database', 'timeout', 'tool', 'security', 'configuration']).optional()
});

export type ValidatedErrorLoggingContext = z.infer<typeof errorLoggingContextSchema>;