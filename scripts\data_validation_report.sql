-- Comprehensive Data Validation Report
-- This script validates the existing data quality and consistency

-- 1. Overall Data Summary
SELECT 
  'DATA_SUMMARY' as report_section,
  'users' as table_name,
  COUNT(*) as record_count,
  COUNT(DISTINCT id) as unique_ids,
  COUNT(CASE WHEN metadata IS NOT NULL THEN 1 END) as records_with_metadata
FROM memory_master.users

UNION ALL

SELECT 
  'DATA_SUMMARY' as report_section,
  'apps' as table_name,
  COUNT(*) as record_count,
  COUNT(DISTINCT id) as unique_ids,
  COUNT(CASE WHEN metadata IS NOT NULL THEN 1 END) as records_with_metadata
FROM memory_master.apps

UNION ALL

SELECT 
  'DATA_SUMMARY' as report_section,
  'memories' as table_name,
  COUNT(*) as record_count,
  COUNT(DISTINCT id) as unique_ids,
  COUNT(CASE WHEN metadata IS NOT NULL THEN 1 END) as records_with_metadata
FROM memory_master.memories

UNION ALL

SELECT 
  'DATA_SUMMARY' as report_section,
  'memory_access_logs' as table_name,
  COUNT(*) as record_count,
  COUNT(DISTINCT id) as unique_ids,
  COUNT(CASE WHEN metadata IS NOT NULL THEN 1 END) as records_with_metadata
FROM memory_master.memory_access_logs

UNION ALL

SELECT 
  'DATA_SUMMARY' as report_section,
  'categories' as table_name,
  COUNT(*) as record_count,
  COUNT(DISTINCT id) as unique_ids,
  0 as records_with_metadata
FROM memory_master.categories

ORDER BY table_name;
