import type { Server } from '@modelcontextprotocol/sdk/server/index.js';
import type { 
  CallToolRequest, 
  ListToolsRequest, 
  ReadResourceRequest, 
  ListResourcesRequest 
} from '@modelcontextprotocol/sdk/types.js';
import { RequestTracker, ContextualLogger, contextLogger } from './logger.js';
import { createErrorResponse, McpServerError } from './errors.js';

/**
 * Request middleware for MCP server that adds logging and error handling
 */
export class RequestMiddleware {
  private logger: ContextualLogger;

  constructor(logger: ContextualLogger = contextLogger) {
    this.logger = logger;
  }

  /**
   * Wraps a request handler with logging and error handling
   */
  wrapHandler<TRequest, TResponse>(
    handlerName: string,
    handler: (request: TRequest, requestId: string) => Promise<TResponse>
  ) {
    return async (request: TRequest): Promise<TResponse> => {
      const requestId = RequestTracker.generateRequestId();
      const requestLogger = this.logger.withRequestId(requestId);
      const timer = requestLogger.startTimer(`${handlerName}_handler`);

      try {
        // Set up request context
        RequestTracker.setContext(requestId, {
          requestId,
          handler: handlerName,
          timestamp: Date.now(),
        });

        requestLogger.info(`${handlerName} request started`, {
          requestId,
          handler: handlerName,
          type: 'request_start',
        });

        // Execute the handler
        const result = await handler(request, requestId);

        timer();
        requestLogger.info(`${handlerName} request completed successfully`, {
          requestId,
          handler: handlerName,
          type: 'request_success',
        });

        return result;
      } catch (error) {
        timer();
        
        // Log the error with context
        requestLogger.error(`${handlerName} request failed`, error instanceof Error ? error : undefined, {
          requestId,
          handler: handlerName,
          type: 'request_error',
          errorType: error instanceof Error ? error.constructor.name : typeof error,
        });

        // Create standardized error response
        const errorResponse = createErrorResponse(error, requestId);
        
        // For MCP protocol, we need to return the error in the expected format
        return errorResponse as TResponse;
      } finally {
        // Clean up request context
        RequestTracker.clearContext(requestId);
      }
    };
  }

  /**
   * Wraps tool execution with enhanced logging
   */
  wrapToolHandler<TArgs, TResult>(
    toolName: string,
    handler: (args: TArgs, requestId: string) => Promise<TResult>
  ) {
    return async (args: TArgs, requestId?: string): Promise<TResult> => {
      const actualRequestId = requestId || RequestTracker.generateRequestId();
      const requestLogger = this.logger.withRequestId(actualRequestId);
      const startTime = Date.now();

      try {
        // Update context with tool information
        RequestTracker.updateContext(actualRequestId, {
          toolName,
          operation: 'tool_execution',
        });

        requestLogger.info(`Tool execution started: ${toolName}`, {
          toolName,
          requestId: actualRequestId,
          args: this.sanitizeArgs(args),
          type: 'tool_start',
        });

        const result = await handler(args, actualRequestId);
        const duration = Date.now() - startTime;

        requestLogger.logToolExecution(toolName, actualRequestId, true, duration);

        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        
        requestLogger.logToolExecution(
          toolName, 
          actualRequestId, 
          false, 
          duration, 
          error instanceof Error ? error : new Error(String(error))
        );

        throw error;
      }
    };
  }

  /**
   * Sanitize arguments for logging (remove sensitive data)
   */
  private sanitizeArgs(args: unknown): unknown {
    if (typeof args !== 'object' || args === null) {
      return args;
    }

    const sensitiveKeys = ['password', 'token', 'key', 'secret', 'auth', 'credential'];
    const sanitized: Record<string, unknown> = {};

    for (const [key, value] of Object.entries(args as Record<string, unknown>)) {
      if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
        sanitized[key] = '[REDACTED]';
      } else if (typeof value === 'object' && value !== null) {
        sanitized[key] = this.sanitizeArgs(value);
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  /**
   * Apply middleware to an MCP server instance
   */
  applyToServer(server: Server) {
    // Note: This is a conceptual implementation
    // The actual MCP SDK may require different integration patterns
    
    this.logger.info('Request middleware applied to MCP server', {
      type: 'middleware_init',
    });

    // Set up global error handler
    process.on('unhandledRejection', (reason, promise) => {
      this.logger.error('Unhandled promise rejection', reason instanceof Error ? reason : undefined, {
        promise: promise.toString(),
        type: 'unhandled_rejection',
      });
    });

    process.on('uncaughtException', (error) => {
      this.logger.fatal('Uncaught exception', error, {
        type: 'uncaught_exception',
      });
      
      // Give time for logs to flush before exiting
      setTimeout(() => {
        process.exit(1);
      }, 1000);
    });

    return server;
  }
}

/**
 * Default middleware instance
 */
export const requestMiddleware = new RequestMiddleware();

/**
 * Utility function to create a request-aware logger
 */
export function createRequestLogger(requestId?: string): ContextualLogger {
  if (requestId) {
    return contextLogger.withRequestId(requestId);
  }
  return contextLogger;
}

/**
 * Performance monitoring utilities
 */
export class PerformanceMonitor {
  private static metrics = new Map<string, {
    count: number;
    totalDuration: number;
    minDuration: number;
    maxDuration: number;
    errors: number;
  }>();

  static recordMetric(operation: string, duration: number, success: boolean = true) {
    const existing = this.metrics.get(operation) || {
      count: 0,
      totalDuration: 0,
      minDuration: Infinity,
      maxDuration: 0,
      errors: 0,
    };

    existing.count++;
    existing.totalDuration += duration;
    existing.minDuration = Math.min(existing.minDuration, duration);
    existing.maxDuration = Math.max(existing.maxDuration, duration);
    
    if (!success) {
      existing.errors++;
    }

    this.metrics.set(operation, existing);
  }

  static getMetrics() {
    const result: Record<string, any> = {};
    
    for (const [operation, metrics] of this.metrics.entries()) {
      result[operation] = {
        ...metrics,
        averageDuration: metrics.totalDuration / metrics.count,
        errorRate: metrics.errors / metrics.count,
        successRate: (metrics.count - metrics.errors) / metrics.count,
      };
    }

    return result;
  }

  static reset() {
    this.metrics.clear();
  }
}

/**
 * Health check utilities
 */
export interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  uptime: number;
  version: string;
  metrics: Record<string, any>;
  checks: Record<string, {
    status: 'pass' | 'fail';
    message?: string;
    duration?: number;
  }>;
}

export class HealthChecker {
  private checks = new Map<string, () => Promise<{ status: 'pass' | 'fail'; message?: string }>>();
  private startTime = Date.now();

  addCheck(name: string, check: () => Promise<{ status: 'pass' | 'fail'; message?: string }>) {
    this.checks.set(name, check);
  }

  async getHealth(): Promise<HealthStatus> {
    const checks: Record<string, any> = {};
    let overallStatus: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';

    for (const [name, check] of this.checks.entries()) {
      const start = Date.now();
      try {
        const result = await check();
        checks[name] = {
          ...result,
          duration: Date.now() - start,
        };

        if (result.status === 'fail') {
          overallStatus = overallStatus === 'healthy' ? 'degraded' : 'unhealthy';
        }
      } catch (error) {
        checks[name] = {
          status: 'fail',
          message: error instanceof Error ? error.message : String(error),
          duration: Date.now() - start,
        };
        overallStatus = 'unhealthy';
      }
    }

    return {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: Date.now() - this.startTime,
      version: process.env.npm_package_version || 'unknown',
      metrics: PerformanceMonitor.getMetrics(),
      checks,
    };
  }
}

export const healthChecker = new HealthChecker();
