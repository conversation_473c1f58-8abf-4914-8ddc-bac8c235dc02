-- Comprehensive Test Data Validation and Generation Script
-- This script validates existing data and generates clean test data with proper JSON syntax

-- 1. VAL<PERSON>ATION QUERIES
-- Check for JSON syntax errors in existing data
SELECT 'VALIDATION: JSON Syntax Check' as check_type;

-- Check memories table JSO<PERSON> fields
SELECT 
  'memories' as table_name,
  COUNT(*) as total_records,
  COUNT(CASE WHEN metadata IS NOT NULL THEN 1 END) as records_with_metadata,
  COUNT(CASE WHEN metadata IS NOT NULL AND metadata::text != 'null' THEN 1 END) as valid_json_metadata
FROM memory_master.memories;

-- Check memory_access_logs table JSO<PERSON> fields  
SELECT 
  'memory_access_logs' as table_name,
  COUNT(*) as total_records,
  COUNT(CASE WHEN metadata IS NOT NULL THEN 1 END) as records_with_metadata,
  COUNT(CASE WHEN metadata IS NOT NULL AND metadata::text != 'null' THEN 1 END) as valid_json_metadata
FROM memory_master.memory_access_logs;

-- Check apps table JSO<PERSON> fields
SELECT 
  'apps' as table_name,
  COUNT(*) as total_records,
  COUNT(CASE WHEN metadata IS NOT NULL THEN 1 END) as records_with_metadata,
  COUNT(CASE WHEN metadata IS NOT NULL AND metadata::text != 'null' THEN 1 END) as valid_json_metadata
FROM memory_master.apps;

-- Check users table JSON fields
SELECT 
  'users' as table_name,
  COUNT(*) as total_records,
  COUNT(CASE WHEN metadata IS NOT NULL THEN 1 END) as records_with_metadata,
  COUNT(CASE WHEN metadata IS NOT NULL AND metadata::text != 'null' THEN 1 END) as valid_json_metadata
FROM memory_master.users;

-- 2. APP_ID CONSISTENCY CHECK
SELECT 'VALIDATION: App ID Consistency Check' as check_type;

-- Check app_ids in memories that exist in apps table
SELECT 
  'memories_to_apps' as relationship,
  COUNT(DISTINCT m.app_id) as unique_app_ids_in_memories,
  COUNT(DISTINCT a.id) as matching_app_ids_in_apps,
  ROUND(
    (COUNT(DISTINCT a.id)::float / NULLIF(COUNT(DISTINCT m.app_id), 0)) * 100, 2
  ) as consistency_percentage
FROM memory_master.memories m
LEFT JOIN memory_master.apps a ON m.app_id = a.id;

-- Check app_ids in memory_access_logs that exist in apps table
SELECT 
  'memory_access_logs_to_apps' as relationship,
  COUNT(DISTINCT mal.app_id) as unique_app_ids_in_logs,
  COUNT(DISTINCT a.id) as matching_app_ids_in_apps,
  ROUND(
    (COUNT(DISTINCT a.id)::float / NULLIF(COUNT(DISTINCT mal.app_id), 0)) * 100, 2
  ) as consistency_percentage
FROM memory_master.memory_access_logs mal
LEFT JOIN memory_master.apps a ON mal.app_id = a.id;

-- Check memory_access_logs app_ids that match memories app_ids
SELECT 
  'memory_access_logs_to_memories' as relationship,
  COUNT(DISTINCT mal.app_id) as unique_app_ids_in_logs,
  COUNT(DISTINCT m.app_id) as unique_app_ids_in_memories,
  COUNT(DISTINCT CASE WHEN m.app_id IS NOT NULL THEN mal.app_id END) as matching_app_ids,
  ROUND(
    (COUNT(DISTINCT CASE WHEN m.app_id IS NOT NULL THEN mal.app_id END)::float / 
     NULLIF(COUNT(DISTINCT mal.app_id), 0)) * 100, 2
  ) as consistency_percentage
FROM memory_master.memory_access_logs mal
LEFT JOIN memory_master.memories m ON mal.memory_id = m.id;

-- 3. REFERENTIAL INTEGRITY CHECK
SELECT 'VALIDATION: Referential Integrity Check' as check_type;

-- Check for orphaned memories (user_id not in users table)
SELECT 
  'orphaned_memories' as check_name,
  COUNT(*) as count
FROM memory_master.memories m
LEFT JOIN memory_master.users u ON m.user_id = u.id
WHERE u.id IS NULL;

-- Check for orphaned memory_access_logs (memory_id not in memories table)
SELECT 
  'orphaned_memory_access_logs' as check_name,
  COUNT(*) as count
FROM memory_master.memory_access_logs mal
LEFT JOIN memory_master.memories m ON mal.memory_id = m.id
WHERE m.id IS NULL;

-- Check for orphaned apps (owner_id not in users table)
SELECT 
  'orphaned_apps' as check_name,
  COUNT(*) as count
FROM memory_master.apps a
LEFT JOIN memory_master.users u ON a.owner_id = u.id
WHERE u.id IS NULL;

-- 4. DATA QUALITY SUMMARY
SELECT 'SUMMARY: Data Quality Report' as check_type;

SELECT 
  'overall_summary' as metric_type,
  (SELECT COUNT(*) FROM memory_master.users) as total_users,
  (SELECT COUNT(*) FROM memory_master.apps) as total_apps,
  (SELECT COUNT(*) FROM memory_master.memories) as total_memories,
  (SELECT COUNT(*) FROM memory_master.memory_access_logs) as total_access_logs,
  (SELECT COUNT(*) FROM memory_master.categories) as total_categories,
  (SELECT COUNT(*) FROM memory_master.memory_categories) as total_memory_categories;
