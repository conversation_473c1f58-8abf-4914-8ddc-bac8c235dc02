/**
 * Enhanced error handling mechanisms for CRUD operations
 * 
 * This module provides comprehensive error handling specifically tailored for CRUD operations,
 * including retry mechanisms, error classification, user-friendly messages, and recovery suggestions.
 */

import { contextLogger, type LogContext } from '../utils/logger.js';
import {
  McpServerError,
  DatabaseError,
  ValidationError,
  ToolError,
  TimeoutError,
  RetryableError,
  ErrorCode,
  ErrorClassifier,
  ErrorRecovery,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  RetryManager,
} from '../utils/errors.js';
import { type WhereClause } from './crud-validation.js';

export interface CrudErrorContext extends LogContext {
  operation: 'CREATE' | 'READ' | 'UPDATE' | 'DELETE' | 'UPSERT' | 'BULK_CREATE' | 'BULK_UPDATE' | 'BULK_DELETE' | 'TRANSACTION';
  schema?: string;
  table?: string;
  columns?: string[];
  whereClause?: WhereClause;
  recordCount?: number;
  batchNumber?: number;
  transactionId?: string;
  query?: string;
  validation?: {
    field: string;
    error: string;
    value?: any;
  }[];
}

/**
 * CRUD-specific error handler with enhanced context and retry mechanisms
 */
export class CrudErrorHandler {
  private logger = contextLogger.child({ component: 'CrudErrorHandler' });
  private requestId?: string;

  constructor(requestId?: string) {
    this.requestId = requestId;
  }

  /**
   * Handle and classify CRUD operation errors with full context
   */
  handleError(error: unknown, context: CrudErrorContext): never {
    const enrichedContext = {
      ...context,
      requestId: this.requestId,
      timestamp: Date.now(),
    };

    // Log the error with full context
    this.logger.error(`CRUD ${context.operation} operation failed`, error as Error, enrichedContext);

    // Create enhanced error based on type
    if (error instanceof McpServerError) {
      // Already a structured error, add CRUD context
      const enhancedError = new McpServerError(
        error.code,
        error.message,
        error.statusCode,
        {
          requestId: error.requestId || this.requestId,
          context: { ...error.context, ...enrichedContext },
          cause: error.cause,
        }
      );
      throw enhancedError;
    }

    // Handle validation errors
    if (this.isValidationError(error, context)) {
      throw this.createValidationError(error, enrichedContext);
    }

    // Handle database/platform errors
    if (this.isDatabaseError(error)) {
      throw this.createDatabaseError(error, enrichedContext);
    }

    // Handle timeout errors
    if (this.isTimeoutError(error)) {
      throw this.createTimeoutError(error, enrichedContext);
    }

    // Handle read-only mode errors
    if (this.isReadOnlyError(error, context)) {
      throw this.createReadOnlyError(error, enrichedContext);
    }

    // Default to tool execution error
    throw new ToolError(
      this.extractErrorMessage(error),
      ErrorCode.TOOL_EXECUTION_ERROR,
      {
        requestId: this.requestId,
        context: enrichedContext,
        cause: error instanceof Error ? error : undefined,
      }
    );
  }

  /**
   * Execute CRUD operation with retry logic and enhanced error handling
   */
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    context: CrudErrorContext,
    options: {
      maxRetries?: number;
      baseDelay?: number;
      maxDelay?: number;
      shouldRetry?: (error: any, attempt: number) => boolean;
    } = {}
  ): Promise<T> {
    const operationLogger = this.logger.child({
      operation: context.operation,
      table: context.table ? `${context.schema}.${context.table}` : undefined,
      requestId: this.requestId,
    });

    try {
      return await RetryManager.executeWithRetry(operation, {
        ...options,
        requestId: this.requestId,
        onRetry: (error, attempt, delay) => {
          operationLogger.warn(`CRUD operation failed, retrying in ${delay}ms`, {
            operation: context.operation,
            attempt,
            error: this.extractErrorMessage(error),
            delay,
          });
        },
        shouldRetry: (error, attempt) => {
          // Use custom retry logic if provided, otherwise use smart defaults
          if (options.shouldRetry) {
            return options.shouldRetry(error, attempt);
          }
          return this.shouldRetryCrudOperation(error, context, attempt);
        },
      });
    } catch (error) {
      // Enhance error with CRUD-specific context before throwing
      this.handleError(error, context);
    }
  }

  /**
   * Create user-friendly error messages for different CRUD scenarios
   */
  createUserFriendlyMessage(error: unknown, context: CrudErrorContext): string {
    const baseMessage = ErrorReporter.formatUserError(error);
    
    // Add operation-specific context
    const operationContext = this.getOperationContext(context);
    
    // Add recovery suggestions
    const suggestions = this.getCrudRecoverySuggestions(error, context);
    
    let message = `${operationContext}: ${baseMessage}`;
    
    if (suggestions.length > 0) {
      message += `\n\nSuggestions:\n${suggestions.map(s => `• ${s}`).join('\n')}`;
    }
    
    return message;
  }

  /**
   * Validate CRUD operation parameters and throw enhanced validation errors
   */
  validateOperation(context: CrudErrorContext): void {
    const errors: Array<{field: string, error: string, value?: any}> = [];

    // Schema validation
    if (!context.schema || context.schema.trim() === '') {
      errors.push({ field: 'schema', error: 'Schema name is required' });
    }

    // Table validation
    if (!context.table || context.table.trim() === '') {
      errors.push({ field: 'table', error: 'Table name is required' });
    }

    // Operation-specific validations
    switch (context.operation) {
      case 'UPDATE':
        if (!context.whereClause) {
          errors.push({ 
            field: 'where', 
            error: 'WHERE clause is required for UPDATE operations for safety' 
          });
        }
        break;
      
      case 'DELETE':
        if (!context.whereClause) {
          errors.push({ 
            field: 'where', 
            error: 'WHERE clause is required for DELETE operations for safety' 
          });
        }
        break;
      
      case 'BULK_CREATE':
        if (!context.recordCount || context.recordCount === 0) {
          errors.push({ 
            field: 'data', 
            error: 'At least one record is required for bulk create operations' 
          });
        }
        break;
    }

    if (errors.length > 0) {
      const enrichedContext = {
        ...context,
        validation: errors,
        requestId: this.requestId,
      };

      throw new ValidationError(
        `${context.operation} operation validation failed: ${errors.map(e => `${e.field}: ${e.error}`).join(', ')}`,
        {
          requestId: this.requestId,
          context: enrichedContext,
        }
      );
    }
  }

  /**
   * Log successful CRUD operations with performance metrics
   */
  logSuccess(context: CrudErrorContext, result: any, duration?: number): void {
    this.logger.info(`CRUD ${context.operation} operation completed successfully`, {
      operation: context.operation,
      table: context.table ? `${context.schema}.${context.table}` : undefined,
      recordCount: context.recordCount,
      rowsAffected: result.rowsAffected || result.data?.length,
      duration,
      requestId: this.requestId,
      batchNumber: context.batchNumber,
      transactionId: context.transactionId,
    });
  }

  /**
   * Create detailed error report for debugging and monitoring
   */
  createErrorReport(error: unknown, context: CrudErrorContext): object {
    const report = ErrorReporter.createErrorReport(error, {
      ...context,
      requestId: this.requestId,
    });

    // Add CRUD-specific information
    return {
      ...report,
      crud: {
        operation: context.operation,
        table: context.table ? `${context.schema}.${context.table}` : undefined,
        recordCount: context.recordCount,
        hasWhere: !!context.whereClause,
        columns: context.columns,
        batchNumber: context.batchNumber,
        transactionId: context.transactionId,
      },
      userMessage: this.createUserFriendlyMessage(error, context),
    };
  }

  // Private helper methods

  private isValidationError(error: unknown, context: CrudErrorContext): boolean {
    if (error instanceof ValidationError) return true;
    
    const message = this.extractErrorMessage(error);
    return message.includes('validation failed') || 
           message.includes('invalid') || 
           message.includes('required') ||
           message.includes('Cannot use PostgreSQL reserved word');
  }

  private isDatabaseError(error: unknown): boolean {
    if (error instanceof DatabaseError) return true;
    
    const message = this.extractErrorMessage(error);
    return message.includes('SQL execution failed') ||
           message.includes('database') ||
           message.includes('constraint') ||
           message.includes('foreign key') ||
           message.includes('unique') ||
           message.includes('not null');
  }

  private isTimeoutError(error: unknown): boolean {
    if (error instanceof TimeoutError) return true;
    
    const message = this.extractErrorMessage(error);
    return message.includes('timeout') ||
           message.includes('TIMEOUT') ||
           message.includes('timed out');
  }

  private isReadOnlyError(error: unknown, context: CrudErrorContext): boolean {
    const message = this.extractErrorMessage(error);
    return message.includes('read-only mode') || 
           (message.includes('Cannot') && ['CREATE', 'UPDATE', 'DELETE', 'UPSERT'].includes(context.operation));
  }

  private createValidationError(error: unknown, context: CrudErrorContext): ValidationError {
    return new ValidationError(
      `${context.operation} validation failed: ${this.extractErrorMessage(error)}`,
      {
        requestId: this.requestId,
        context,
        cause: error instanceof Error ? error : undefined,
      }
    );
  }

  private createDatabaseError(error: unknown, context: CrudErrorContext): DatabaseError {
    return ErrorClassifier.createDatabaseError(error, context, this.requestId);
  }

  private createTimeoutError(error: unknown, context: CrudErrorContext): TimeoutError {
    return new TimeoutError(
      `${context.operation} operation timed out: ${this.extractErrorMessage(error)}`,
      'operation',
      undefined,
      {
        requestId: this.requestId,
        context,
        cause: error instanceof Error ? error : undefined,
      }
    );
  }

  private createReadOnlyError(error: unknown, context: CrudErrorContext): ToolError {
    return new ToolError(
      `${context.operation} operation not allowed: ${this.extractErrorMessage(error)}`,
      ErrorCode.PERMISSION_DENIED,
      {
        requestId: this.requestId,
        context,
        cause: error instanceof Error ? error : undefined,
      }
    );
  }

  private shouldRetryCrudOperation(error: unknown, context: CrudErrorContext, attempt: number): boolean {
    // Don't retry after too many attempts
    if (attempt >= 3) return false;

    // Don't retry validation errors
    if (this.isValidationError(error, context)) return false;

    // Don't retry read-only errors
    if (this.isReadOnlyError(error, context)) return false;

    // Don't retry constraint violations
    if (error instanceof DatabaseError) {
      const nonRetryableCodes = [
        ErrorCode.UNIQUE_VIOLATION,
        ErrorCode.FOREIGN_KEY_VIOLATION,
        ErrorCode.CHECK_VIOLATION,
        ErrorCode.NOT_NULL_VIOLATION,
        ErrorCode.PERMISSION_DENIED,
        ErrorCode.TABLE_NOT_FOUND,
        ErrorCode.COLUMN_NOT_FOUND,
      ];
      
      if (nonRetryableCodes.includes(error.code)) return false;
    }

    // Retry network errors, timeouts, and retryable database errors
    return this.isTimeoutError(error) || 
           error instanceof RetryableError ||
           this.extractErrorMessage(error).includes('connection') ||
           this.extractErrorMessage(error).includes('network');
  }

  private getOperationContext(context: CrudErrorContext): string {
    const table = context.table ? `${context.schema}.${context.table}` : 'unknown table';
    
    switch (context.operation) {
      case 'CREATE': return `Creating record in ${table}`;
      case 'READ': return `Reading records from ${table}`;
      case 'UPDATE': return `Updating records in ${table}`;
      case 'DELETE': return `Deleting records from ${table}`;
      case 'UPSERT': return `Upserting record in ${table}`;
      case 'BULK_CREATE': return `Bulk creating ${context.recordCount || 'multiple'} records in ${table}`;
      case 'BULK_UPDATE': return `Bulk updating records in ${table}`;
      case 'BULK_DELETE': return `Bulk deleting records from ${table}`;
      case 'TRANSACTION': return `Executing transaction`;
      default: return `Performing ${context.operation} operation`;
    }
  }

  private getCrudRecoverySuggestions(error: unknown, context: CrudErrorContext): string[] {
    const suggestions = ErrorRecovery.getRecoverySuggestions(error);
    
    // Add CRUD-specific suggestions
    if (this.isValidationError(error, context)) {
      suggestions.push('Check that all required fields are provided');
      suggestions.push('Verify field names match the table schema');
      suggestions.push('Ensure data types are correct for each field');
    }

    if (context.operation === 'UPDATE' || context.operation === 'DELETE') {
      if (!context.whereClause) {
        suggestions.push('Add a WHERE clause to specify which records to modify');
        suggestions.push('Be specific with WHERE conditions to avoid unintended changes');
      }
    }

    if (context.operation.startsWith('BULK_')) {
      suggestions.push('Consider reducing batch size if experiencing timeouts');
      suggestions.push('Ensure all records in the batch have consistent data structure');
    }

    if (context.operation === 'TRANSACTION') {
      suggestions.push('Check that all operations in the transaction are valid');
      suggestions.push('Consider breaking large transactions into smaller ones');
    }

    return suggestions;
  }

  private extractErrorMessage(error: unknown): string {
    if (error instanceof Error) {
      return error.message;
    }
    return String(error);
  }
}

/**
 * Factory function to create CRUD error handlers with request context
 */
export function createCrudErrorHandler(requestId?: string): CrudErrorHandler {
  return new CrudErrorHandler(requestId);
}

/**
 * Utility functions for common CRUD error scenarios
 */
export class CrudErrorUtils {
  /**
   * Create a WHERE clause validation error
   */
  static createWhereClauseError(message: string, requestId?: string): ValidationError {
    return new ValidationError(`WHERE clause validation failed: ${message}`, {
      requestId,
      context: { component: 'WHERE_CLAUSE_VALIDATOR' },
    });
  }

  /**
   * Create a constraint violation error with helpful context
   */
  static createConstraintError(
    constraintType: 'unique' | 'foreign_key' | 'check' | 'not_null',
    constraintName: string,
    table: string,
    column?: string,
    requestId?: string
  ): DatabaseError {
    const errorCode = {
      unique: ErrorCode.UNIQUE_VIOLATION,
      foreign_key: ErrorCode.FOREIGN_KEY_VIOLATION,
      check: ErrorCode.CHECK_VIOLATION,
      not_null: ErrorCode.NOT_NULL_VIOLATION,
    }[constraintType];

    const message = {
      unique: `Unique constraint violation: Record with this ${column || 'value'} already exists`,
      foreign_key: `Foreign key constraint violation: Referenced record does not exist`,
      check: `Check constraint violation: Data does not meet validation rules`,
      not_null: `Not null constraint violation: Required field ${column || 'unknown'} cannot be empty`,
    }[constraintType];

    return new DatabaseError(message, errorCode, {
      requestId,
      context: { table, column, constraint: constraintName },
      constraint: constraintName,
      table,
      column,
    });
  }

  /**
   * Create a bulk operation error with batch context
   */
  static createBulkOperationError(
    operation: string,
    batchNumber: number,
    totalBatches: number,
    recordCount: number,
    error: unknown,
    requestId?: string
  ): ToolError {
    return new ToolError(
      `Bulk ${operation} failed on batch ${batchNumber}/${totalBatches}: ${error instanceof Error ? error.message : String(error)}`,
      ErrorCode.TOOL_EXECUTION_ERROR,
      {
        requestId,
        context: {
          operation: `BULK_${operation.toUpperCase()}`,
          batchNumber,
          totalBatches,
          recordCount,
        },
        cause: error instanceof Error ? error : undefined,
      }
    );
  }
}