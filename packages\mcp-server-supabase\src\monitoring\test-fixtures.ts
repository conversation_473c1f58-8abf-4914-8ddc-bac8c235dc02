import { randomUUID } from 'crypto';
import { contextLogger } from '../utils/logger.js';
import type { TraceContext } from './distributed-tracing.js';
import type { Alert, AlertRule } from './alerting-system.js';

/**
 * Test data generators for monitoring components
 */
export class TestFixtures {
  private logger = contextLogger.child({ component: 'test-fixtures' });

  /**
   * Generate mock health check data
   */
  generateHealthCheckData(overrides: Partial<any> = {}): any {
    const baseHealth = {
      status: 'healthy' as const,
      timestamp: new Date().toISOString(),
      uptime: Math.floor(Math.random() * 86400000), // Random uptime up to 24 hours
      version: '1.0.0',
      checks: {
        database: {
          status: 'pass' as const,
          message: 'Database connection healthy',
          duration: Math.floor(Math.random() * 100) + 10, // 10-110ms
        },
        configuration: {
          status: 'pass' as const,
          message: 'Configuration valid',
          duration: Math.floor(Math.random() * 50) + 5, // 5-55ms
        },
        performance: {
          status: 'pass' as const,
          message: 'Performance within acceptable limits',
          duration: Math.floor(Math.random() * 200) + 20, // 20-220ms
        },
      },
      metrics: this.generatePerformanceMetrics(),
    };

    return { ...baseHealth, ...overrides };
  }

  /**
   * Generate mock performance metrics
   */
  generatePerformanceMetrics(overrides: Partial<any> = {}): any {
    const baseMetrics = {
      cache: {
        enabled: true,
        stats: {
          hits: Math.floor(Math.random() * 1000) + 100,
          misses: Math.floor(Math.random() * 200) + 10,
          hitRate: Math.random() * 0.3 + 0.7, // 70-100% hit rate
          size: Math.floor(Math.random() * 500) + 50,
          maxSize: 1000,
        },
      },
      connectionPool: {
        enabled: true,
        stats: {
          active: Math.floor(Math.random() * 10) + 1,
          idle: Math.floor(Math.random() * 5) + 2,
          total: Math.floor(Math.random() * 15) + 5,
          maxSize: 20,
          waitingCount: Math.floor(Math.random() * 3),
        },
      },
      circuitBreakers: {
        enabled: true,
        stats: {
          database: {
            state: 'closed',
            failureCount: Math.floor(Math.random() * 3),
            successCount: Math.floor(Math.random() * 100) + 50,
            lastFailureTime: null,
          },
        },
      },
      compression: {
        enabled: true,
        stats: {
          totalRequests: Math.floor(Math.random() * 1000) + 100,
          compressedRequests: Math.floor(Math.random() * 800) + 80,
          compressionRatio: Math.random() * 0.3 + 0.6, // 60-90% compression
          averageCompressionTime: Math.random() * 10 + 2, // 2-12ms
        },
      },
      overall: {
        uptime: Math.floor(Math.random() * 86400000),
        memoryUsage: {
          rss: Math.floor(Math.random() * 100000000) + 50000000,
          heapTotal: Math.floor(Math.random() * 80000000) + 40000000,
          heapUsed: Math.floor(Math.random() * 60000000) + 30000000,
          external: Math.floor(Math.random() * 10000000) + 5000000,
          arrayBuffers: Math.floor(Math.random() * 5000000) + 1000000,
        },
        cpuUsage: {
          user: Math.floor(Math.random() * 1000000) + 100000,
          system: Math.floor(Math.random() * 500000) + 50000,
        },
      },
    };

    return { ...baseMetrics, ...overrides };
  }

  /**
   * Generate mock trace context
   */
  generateTraceContext(overrides: Partial<TraceContext> = {}): TraceContext {
    const startTime = Date.now() - Math.floor(Math.random() * 10000); // Started up to 10 seconds ago
    const endTime = startTime + Math.floor(Math.random() * 5000) + 100; // Duration 100ms to 5.1s
    
    const baseTrace: TraceContext = {
      traceId: randomUUID(),
      spanId: randomUUID(),
      parentSpanId: Math.random() > 0.5 ? randomUUID() : undefined,
      operationName: this.getRandomOperationName(),
      startTime,
      endTime,
      duration: endTime - startTime,
      tags: {
        'service.name': 'mcp-server-supabase',
        'service.version': '1.0.0',
        'http.method': this.getRandomHttpMethod(),
        'http.status_code': this.getRandomHttpStatus(),
        'user.id': `user-${Math.floor(Math.random() * 1000)}`,
      },
      logs: this.generateTraceLogs(),
      status: Math.random() > 0.1 ? 'ok' : 'error', // 90% success rate
    };

    if (baseTrace.status === 'error') {
      baseTrace.error = new Error(this.getRandomErrorMessage());
      baseTrace.tags['error'] = true;
      baseTrace.tags['error.message'] = baseTrace.error.message;
    }

    return { ...baseTrace, ...overrides };
  }

  /**
   * Generate mock trace logs
   */
  generateTraceLogs(): TraceContext['logs'] {
    const logCount = Math.floor(Math.random() * 5) + 1;
    const logs: TraceContext['logs'] = [];
    
    for (let i = 0; i < logCount; i++) {
      logs.push({
        timestamp: Date.now() - Math.floor(Math.random() * 5000),
        level: this.getRandomLogLevel(),
        message: this.getRandomLogMessage(),
        fields: {
          component: this.getRandomComponent(),
          operation: this.getRandomOperationName(),
        },
      });
    }
    
    return logs.sort((a, b) => a.timestamp - b.timestamp);
  }

  /**
   * Generate mock alert
   */
  generateAlert(overrides: Partial<Alert> = {}): Alert {
    const severity = this.getRandomAlertSeverity();
    const timestamp = new Date(Date.now() - Math.floor(Math.random() * 86400000)); // Up to 24 hours ago
    
    const baseAlert: Alert = {
      id: randomUUID(),
      ruleId: `rule-${Math.floor(Math.random() * 10) + 1}`,
      name: this.getRandomAlertName(),
      description: this.getRandomAlertDescription(),
      severity,
      timestamp,
      value: Math.random() * 100,
      threshold: Math.random() * 80 + 10, // 10-90
      tags: {
        category: this.getRandomAlertCategory(),
        environment: 'test',
        service: 'mcp-server-supabase',
      },
      resolved: Math.random() > 0.3, // 70% resolved
      metadata: {
        source: 'test-fixture',
        generatedAt: new Date().toISOString(),
      },
    };

    if (baseAlert.resolved) {
      baseAlert.resolvedAt = new Date(timestamp.getTime() + Math.floor(Math.random() * 3600000)); // Resolved within 1 hour
    }

    return { ...baseAlert, ...overrides };
  }

  /**
   * Generate mock alert rule
   */
  generateAlertRule(overrides: Partial<AlertRule> = {}): AlertRule {
    const baseRule: AlertRule = {
      id: `rule-${randomUUID()}`,
      name: this.getRandomAlertName(),
      description: this.getRandomAlertDescription(),
      severity: this.getRandomAlertSeverity(),
      condition: () => Math.random() > 0.8, // 20% trigger rate
      threshold: Math.random() * 80 + 10,
      enabled: Math.random() > 0.2, // 80% enabled
      cooldownMs: Math.floor(Math.random() * 600000) + 60000, // 1-10 minutes
      tags: {
        category: this.getRandomAlertCategory(),
        type: 'generated',
        environment: 'test',
      },
    };

    return { ...baseRule, ...overrides };
  }

  /**
   * Generate multiple test traces
   */
  generateTraces(count: number): TraceContext[] {
    return Array.from({ length: count }, () => this.generateTraceContext());
  }

  /**
   * Generate multiple test alerts
   */
  generateAlerts(count: number): Alert[] {
    return Array.from({ length: count }, () => this.generateAlert());
  }

  /**
   * Generate test scenario data
   */
  generateScenario(name: string): {
    health: any;
    metrics: any;
    traces: TraceContext[];
    alerts: Alert[];
  } {
    const scenarios = {
      healthy: {
        health: this.generateHealthCheckData({ status: 'healthy' }),
        metrics: this.generatePerformanceMetrics(),
        traces: this.generateTraces(10).map(t => ({ ...t, status: 'ok' })),
        alerts: [],
      },
      degraded: {
        health: this.generateHealthCheckData({ 
          status: 'degraded',
          checks: {
            database: { status: 'pass', message: 'Database slow', duration: 500 },
            configuration: { status: 'fail', message: 'Config validation failed', duration: 100 },
          }
        }),
        metrics: this.generatePerformanceMetrics({
          cache: { stats: { hitRate: 0.4 } },
          overall: { memoryUsage: { heapUsed: 80000000, heapTotal: 90000000 } }
        }),
        traces: this.generateTraces(15).map((t, i) => ({ 
          ...t, 
          status: i % 4 === 0 ? 'error' : 'ok',
          duration: t.duration! * (i % 4 === 0 ? 3 : 1) // Slow down error traces
        })),
        alerts: this.generateAlerts(3).map(a => ({ ...a, resolved: false })),
      },
      critical: {
        health: this.generateHealthCheckData({ 
          status: 'unhealthy',
          checks: {
            database: { status: 'fail', message: 'Database connection failed', duration: 5000 },
            configuration: { status: 'fail', message: 'Critical config error', duration: 200 },
          }
        }),
        metrics: this.generatePerformanceMetrics({
          cache: { stats: { hitRate: 0.1 } },
          overall: { memoryUsage: { heapUsed: 95000000, heapTotal: 100000000 } }
        }),
        traces: this.generateTraces(20).map(t => ({ 
          ...t, 
          status: 'error',
          duration: t.duration! * 5 // Very slow
        })),
        alerts: this.generateAlerts(8).map(a => ({ 
          ...a, 
          resolved: false, 
          severity: Math.random() > 0.5 ? 'critical' : 'error' 
        })),
      },
    };

    return scenarios[name as keyof typeof scenarios] || scenarios.healthy;
  }

  // Helper methods for generating random data
  private getRandomOperationName(): string {
    const operations = [
      'database.query', 'database.insert', 'database.update', 'database.delete',
      'http.request', 'http.response', 'cache.get', 'cache.set',
      'auth.validate', 'auth.login', 'config.load', 'config.validate',
      'tool.execute', 'notification.send', 'health.check'
    ];
    return operations[Math.floor(Math.random() * operations.length)];
  }

  private getRandomHttpMethod(): string {
    const methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];
    return methods[Math.floor(Math.random() * methods.length)];
  }

  private getRandomHttpStatus(): number {
    const statuses = [200, 201, 204, 400, 401, 403, 404, 500, 502, 503];
    return statuses[Math.floor(Math.random() * statuses.length)];
  }

  private getRandomLogLevel(): 'debug' | 'info' | 'warn' | 'error' {
    const levels = ['debug', 'info', 'warn', 'error'] as const;
    return levels[Math.floor(Math.random() * levels.length)];
  }

  private getRandomLogMessage(): string {
    const messages = [
      'Operation started', 'Processing request', 'Database query executed',
      'Cache hit', 'Cache miss', 'Authentication successful',
      'Configuration loaded', 'Health check passed', 'Notification sent'
    ];
    return messages[Math.floor(Math.random() * messages.length)];
  }

  private getRandomComponent(): string {
    const components = [
      'database', 'cache', 'auth', 'config', 'health', 'notifications',
      'tools', 'performance', 'monitoring', 'tracing'
    ];
    return components[Math.floor(Math.random() * components.length)];
  }

  private getRandomAlertSeverity(): 'info' | 'warning' | 'error' | 'critical' {
    const severities = ['info', 'warning', 'error', 'critical'] as const;
    const weights = [0.1, 0.4, 0.4, 0.1]; // More warnings and errors
    const random = Math.random();
    let sum = 0;
    
    for (let i = 0; i < weights.length; i++) {
      sum += weights[i];
      if (random <= sum) {
        return severities[i];
      }
    }
    
    return 'warning';
  }

  private getRandomAlertName(): string {
    const names = [
      'High Memory Usage', 'Database Connection Failure', 'High Error Rate',
      'Slow Response Time', 'Cache Miss Rate High', 'Configuration Error',
      'Authentication Failure', 'Disk Space Low', 'CPU Usage High',
      'Network Connectivity Issue'
    ];
    return names[Math.floor(Math.random() * names.length)];
  }

  private getRandomAlertDescription(): string {
    const descriptions = [
      'System resource usage exceeds threshold',
      'Service availability is compromised',
      'Performance degradation detected',
      'Security issue identified',
      'Configuration validation failed',
      'External dependency unavailable'
    ];
    return descriptions[Math.floor(Math.random() * descriptions.length)];
  }

  private getRandomAlertCategory(): string {
    const categories = ['system', 'database', 'performance', 'security', 'configuration', 'network'];
    return categories[Math.floor(Math.random() * categories.length)];
  }

  private getRandomErrorMessage(): string {
    const errors = [
      'Connection timeout', 'Invalid credentials', 'Resource not found',
      'Permission denied', 'Service unavailable', 'Internal server error',
      'Rate limit exceeded', 'Invalid request format', 'Database constraint violation'
    ];
    return errors[Math.floor(Math.random() * errors.length)];
  }
}

/**
 * Global test fixtures instance
 */
export const testFixtures = new TestFixtures();
