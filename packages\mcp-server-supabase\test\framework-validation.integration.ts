import { describe, expect, test } from 'vitest';
import { createClient } from '@supabase/supabase-js';

/**
 * Integration tests to validate the testing framework is working correctly
 * These tests use the public schema which should be accessible with the anon key
 */

const SUPABASE_URL = process.env.SUPABASE_URL || 'https://devdb.syncrobit.net';
const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogImFub24iLAogICJpc3MiOiAic3VwYWJhc2UiLAogICJpYXQiOiAxNzQxMDk1MDAwLAogICJleHAiOiAxODk4ODYxNDAwCn0.J1N7sT41EI4ORiRFvPm3FPDArs43dzEq9g9-iq7d3pY';

describe('Testing Framework Validation', () => {
  test('can connect to Supabase instance', async () => {
    const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
    
    // Test basic connection - this should work even if no tables are accessible
    expect(supabase).toBeDefined();
    expect(supabase.auth).toBeDefined();
    expect(supabase.from).toBeDefined();
  });

  test('environment variables are loaded correctly', () => {
    expect(SUPABASE_URL).toBeDefined();
    expect(SUPABASE_URL).not.toBe('');
    expect(SUPABASE_ANON_KEY).toBeDefined();
    expect(SUPABASE_ANON_KEY).not.toBe('');
    
    // Validate URL format
    expect(SUPABASE_URL).toMatch(/^https?:\/\/.+/);
    
    // Validate JWT format (basic check)
    expect(SUPABASE_ANON_KEY).toMatch(/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/);
  });

  test('vitest testing framework is working', () => {
    // Test basic assertions
    expect(true).toBe(true);
    expect(1 + 1).toBe(2);
    expect('hello').toContain('ell');
    expect([1, 2, 3]).toHaveLength(3);
    
    // Test async support
    const promise = Promise.resolve('test');
    expect(promise).resolves.toBe('test');
  });

  test('can handle async operations', async () => {
    const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));
    
    const start = Date.now();
    await delay(10);
    const end = Date.now();
    
    expect(end - start).toBeGreaterThanOrEqual(10);
  });

  test('error handling works correctly', async () => {
    const supabase = createClient(SUPABASE_URL, 'invalid-key');
    
    // This should fail with invalid credentials
    const { data, error } = await supabase
      .from('non_existent_table')
      .select('*')
      .limit(1);
    
    expect(error).toBeDefined();
    expect(data).toBeNull();
  });

  test('can validate JSON handling', () => {
    const testObject = {
      id: 'test-123',
      metadata: {
        tags: ['test', 'integration'],
        priority: 'high',
        nested: {
          value: 42,
          active: true
        }
      },
      created_at: new Date().toISOString()
    };
    
    // Test JSON serialization/deserialization
    const jsonString = JSON.stringify(testObject);
    const parsed = JSON.parse(jsonString);
    
    expect(parsed).toEqual(testObject);
    expect(parsed.metadata.tags).toContain('test');
    expect(parsed.metadata.nested.value).toBe(42);
  });

  test('performance measurement works', async () => {
    const iterations = 1000;
    const start = performance.now();
    
    // Simple computation to measure
    let sum = 0;
    for (let i = 0; i < iterations; i++) {
      sum += i;
    }
    
    const end = performance.now();
    const duration = end - start;
    
    expect(sum).toBe((iterations - 1) * iterations / 2); // Sum formula
    expect(duration).toBeGreaterThan(0);
    expect(duration).toBeLessThan(100); // Should be very fast
  });
});
