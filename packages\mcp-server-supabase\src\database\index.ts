/**
 * Database module - Centralized database operations for Supabase MCP server
 * 
 * This module provides a comprehensive database layer for the Supabase MCP server,
 * including connection management, query wrappers, and error handling for the
 * remote Supabase instance at devdb.syncrobit.net.
 */

export {
  DatabaseQueryWrapper,
  createDatabaseQueryWrapper,
  type QueryOptions,
  type QueryResult,
  type TransactionContext,
  type DatabaseOperationType,
} from './query-wrapper.js';

export {
  SupabaseConnectionPool,
  getSupabaseConnectionPool,
  destroySupabaseConnectionPool,
  type SupabaseConnection,
  type ConnectionRequest,
  type PoolStats,
  CircuitBreakerState,
} from '../config/supabase-connection-pool.js';

export {
  SupabaseConnectionConfigManager,
  supabaseConnectionConfig,
  type SupabaseConnectionConfig,
  SUPABASE_ENV_MAPPING,
  DEFAULT_SUPABASE_CONFIG,
} from '../config/supabase-connection-config.js';

export {
  DatabaseLifecycleManager,
  getDatabaseLifecycleManager,
  initializeDatabaseSystem,
  shutdownDatabaseSystem,
  getQueryWrapper,
  getConnectionPool,
  isDatabaseHealthy,
  LifecycleState,
  type HealthStatus,
  type LifecycleEvents,
} from './lifecycle-manager.js';

/**
 * Initialize the database layer with configuration
 * @deprecated Use initializeDatabaseSystem instead
 */
export async function initializeDatabase(_config?: any): Promise<{
  pool: any;
  queryWrapper: any;
  configManager: any;
}> {
  const { initializeDatabaseSystem } = await import('./lifecycle-manager.js');

  const manager = await initializeDatabaseSystem();

  return {
    pool: manager.getConnectionPool(),
    queryWrapper: manager.getQueryWrapper(),
    configManager: manager.getConfigManager(),
  };
}

/**
 * Shutdown the database layer
 * @deprecated Use shutdownDatabaseSystem instead
 */
export async function shutdownDatabase(): Promise<void> {
  const { shutdownDatabaseSystem } = await import('./lifecycle-manager.js');
  await shutdownDatabaseSystem();
}