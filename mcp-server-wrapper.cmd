@echo off
setlocal

REM Set Supabase environment variables from command line arguments
set SUPABASE_URL=
set SUPABASE_ANON_KEY=
set SUPABASE_SERVICE_ROLE_KEY=

REM Parse command line arguments
:parse_args
if "%1"=="" goto run_server
if "%1"=="--supabase-url" (
    set SUPABASE_URL=%2
    shift
    shift
    goto parse_args
)
if "%1"=="--anon-key" (
    set SUPABASE_ANON_KEY=%2
    shift
    shift
    goto parse_args
)
if "%1"=="--service-key" (
    set SUPABASE_SERVICE_ROLE_KEY=%2
    shift
    shift
    goto parse_args
)
REM Handle --arg=value format
for /f "tokens=1,2 delims==" %%a in ("%1") do (
    if "%%a"=="--supabase-url" set SUPABASE_URL=%%b
    if "%%a"=="--anon-key" set SUPABASE_ANON_KEY=%%b
    if "%%a"=="--service-key" set SUPABASE_SERVICE_ROLE_KEY=%%b
)
shift
goto parse_args

:run_server
REM Debug output (remove in production)
echo Setting SUPABASE_URL=%SUPABASE_URL%
echo Setting SUPABASE_ANON_KEY=%SUPABASE_ANON_KEY%
echo Setting SUPABASE_SERVICE_ROLE_KEY=%SUPABASE_SERVICE_ROLE_KEY%

REM Call the actual MCP server
mcp-server-supabase

endlocal 