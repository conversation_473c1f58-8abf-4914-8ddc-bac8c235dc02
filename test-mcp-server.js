#!/usr/bin/env node

// Test script to verify MCP server functionality
const { spawn } = require('child_process');
const path = require('path');

// Set environment variables
process.env.SUPABASE_URL = 'https://devdb.syncrobit.net';
process.env.SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogImFub24iLAogICJpc3MiOiAic3VwYWJhc2UiLAogICJpYXQiOiAxNzQxMDk1MDAwLAogICJleHAiOiAxODk4ODYxNDAwCn0.J1N7sT41EI4ORiRFvPm3FPDArs43dzEq9g9-iq7d3pY';
process.env.SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogInNlcnZpY2Vfcm9sZSIsCiAgImlzcyI6ICJzdXBhYmFzZSIsCiAgImlhdCI6IDE3NDEwOTUwMDAsCiAgImV4cCI6IDE4OTg4NjE0MDAKfQ.4OLZbhrbccD5vfy14HcWgYDgJySzUXimxgcGA3OgaV8';
process.env.READ_ONLY = 'false';
process.env.DEBUG_SQL = 'false';

const serverPath = path.join(__dirname, 'packages', 'mcp-server-supabase', 'dist', 'transports', 'stdio.cjs');

console.log('Testing MCP server at:', serverPath);
console.log('Environment variables set');

// Test if we can run the server
const child = spawn('node', [serverPath, '--version'], {
  stdio: ['pipe', 'pipe', 'pipe'],
  env: process.env
});

let output = '';
let errorOutput = '';

child.stdout.on('data', (data) => {
  output += data.toString();
});

child.stderr.on('data', (data) => {
  errorOutput += data.toString();
});

child.on('close', (code) => {
  console.log(`\nExit code: ${code}`);
  console.log('STDOUT:', output);
  console.log('STDERR:', errorOutput);
  
  if (code === 0) {
    console.log('✅ MCP server version check successful');
  } else {
    console.log('❌ MCP server version check failed');
  }
});

child.on('error', (error) => {
  console.error('Failed to start MCP server:', error);
});

// Test MCP protocol communication
setTimeout(() => {
  console.log('\nTesting MCP protocol communication...');
  
  const mcpChild = spawn('node', [serverPath], {
    stdio: ['pipe', 'pipe', 'pipe'],
    env: process.env
  });
  
  let mcpOutput = '';
  let mcpError = '';
  
  mcpChild.stdout.on('data', (data) => {
    mcpOutput += data.toString();
  });
  
  mcpChild.stderr.on('data', (data) => {
    mcpError += data.toString();
  });
  
  // Send MCP initialize request
  const initRequest = {
    jsonrpc: "2.0",
    id: 1,
    method: "initialize",
    params: {
      protocolVersion: "2024-11-05",
      capabilities: {},
      clientInfo: {
        name: "test-client",
        version: "1.0.0"
      }
    }
  };
  
  mcpChild.stdin.write(JSON.stringify(initRequest) + '\n');
  
  setTimeout(() => {
    // Send tools/list request
    const toolsRequest = {
      jsonrpc: "2.0",
      id: 2,
      method: "tools/list"
    };
    
    mcpChild.stdin.write(JSON.stringify(toolsRequest) + '\n');
    
    setTimeout(() => {
      mcpChild.kill();
      console.log('\nMCP Protocol Test Results:');
      console.log('Output:', mcpOutput);
      console.log('Error:', mcpError);
    }, 2000);
  }, 1000);
  
}, 3000);
