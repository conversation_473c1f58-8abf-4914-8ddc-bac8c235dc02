import { describe, test, expect, beforeEach, afterEach, vi } from 'vitest';
import { contextLogger, RequestTracker } from '../src/utils/logger.js';
import { UniversalErrorHandler } from '../src/tools/universal-error-handler.js';
import { safeParseWithDetails } from '../src/tools/crud-validation.js';
import { extractValidationErrors } from '../src/utils/error-logging-validator.js';
import { z } from 'zod';

/**
 * Real-World Error Logging Integration Tests
 * 
 * Tests error logging by triggering actual validation and runtime errors
 * through real MCP tool operations. Validates that the enhanced error logging
 * infrastructure captures and logs errors with appropriate context.
 */

describe('Real-World Error Logging Integration Tests', () => {
  let errorHandler: UniversalErrorHandler;
  let mockLogger: any;
  let originalLogger: any;
  let requestId: string;

  beforeEach(() => {
    // Setup request context
    requestId = RequestTracker.generateRequestId();
    RequestTracker.setContext(requestId, { component: 'real-world-test' });

    // Create mock logger with spy functions
    mockLogger = {
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
      trace: vi.fn(),
      child: vi.fn().mockReturnThis(),
    };

    // Backup original logger and replace with mock
    originalLogger = contextLogger;
    vi.mocked(contextLogger).child = vi.fn(() => mockLogger);
    
    errorHandler = new UniversalErrorHandler(requestId);
  });

  afterEach(() => {
    RequestTracker.clearContext(requestId);
    vi.clearAllMocks();
  });

  describe('Zod Validation Error Logging', () => {
    test('should log real Zod validation errors with field details', () => {
      // Define a realistic schema for table creation
      const createTableSchema = z.object({
        tableName: z.string().min(1).max(63),
        schema: z.string().min(1).max(63),
        columns: z.array(z.object({
          name: z.string().min(1).max(63),
          type: z.enum(['varchar', 'integer', 'boolean', 'timestamp']),
          nullable: z.boolean().default(true),
          primaryKey: z.boolean().default(false)
        })).min(1),
        comment: z.string().optional()
      });

      // Create invalid data that will trigger multiple validation errors
      const invalidData = {
        tableName: '', // Empty string - violates min(1)
        schema: 'x'.repeat(70), // Too long - violates max(63)
        columns: [], // Empty array - violates min(1)
        comment: 123 // Wrong type - should be string
      };

      // Trigger actual Zod validation error
      const parseResult = createTableSchema.safeParse(invalidData);
      
      expect(parseResult.success).toBe(false);
      
      if (!parseResult.success) {
        const context = {
          toolName: 'create_table',
          operation: 'CREATE_TABLE',
          category: 'DDL' as const,
          schema: 'public',
          parameters: invalidData,
          validation: extractValidationErrors(parseResult.error)
        };

        // This should trigger the enhanced error logging
        try {
          errorHandler.handleError(parseResult.error, context);
        } catch (error) {
          // Expected to throw McpServerError
        }

        // Verify that error logging was called with enhanced context
        // There are multiple logger calls - check the main error handler call
        expect(mockLogger.error).toHaveBeenCalledWith(
          'create_table operation failed',
          expect.any(z.ZodError),
          expect.objectContaining({
            toolName: 'create_table',
            operation: 'CREATE_TABLE',
            category: 'DDL',
            requestId,
            parameters: invalidData,
            validation: expect.arrayContaining([
              expect.objectContaining({
                field: 'tableName',
                error: expect.stringContaining('at least 1 character')
              }),
              expect.objectContaining({
                field: 'schema',
                error: expect.stringContaining('at most 63 character')
              }),
              expect.objectContaining({
                field: 'columns',
                error: expect.stringContaining('at least 1 element')
              })
            ])
          })
        );

        // Also verify the detailed validation error was logged by ErrorLoggingValidator
        expect(mockLogger.error).toHaveBeenCalledWith(
          'Validation error detected',
          expect.objectContaining({
            validationErrors: expect.arrayContaining([
              expect.objectContaining({
                field: 'tableName',
                error: expect.stringContaining('at least 1 character')
              }),
              expect.objectContaining({
                field: 'schema', 
                error: expect.stringContaining('at most 63 character')
              }),
              expect.objectContaining({
                field: 'columns',
                error: expect.stringContaining('at least 1 element')
              })
            ]),
            errorType: 'ZOD_VALIDATION'
          })
        );
      }
    });

    test('should log nested object validation errors', () => {
      const updateRecordSchema = z.object({
        table: z.string().min(1),
        data: z.object({
          email: z.string().email(),
          age: z.number().min(0).max(150),
          preferences: z.object({
            theme: z.enum(['light', 'dark']),
            notifications: z.boolean()
          })
        }),
        where: z.object({
          id: z.number().positive()
        })
      });

      const invalidUpdateData = {
        table: 'users',
        data: {
          email: 'invalid-email', // Invalid email format
          age: -5, // Negative age
          preferences: {
            theme: 'blue', // Invalid enum value
            notifications: 'yes' // Wrong type
          }
        },
        where: {
          id: 0 // Not positive
        }
      };

      const parseResult = updateRecordSchema.safeParse(invalidUpdateData);
      
      if (!parseResult.success) {
        const context = {
          toolName: 'update_record',
          operation: 'UPDATE',
          category: 'CRUD' as const,
          table: 'users',
          parameters: invalidUpdateData,
          validation: extractValidationErrors(parseResult.error)
        };

        try {
          errorHandler.handleError(parseResult.error, context);
        } catch (error) {
          // Expected to throw
        }

        // Verify nested validation errors are captured
        expect(mockLogger.error).toHaveBeenCalledWith(
          'update_record operation failed',
          expect.any(z.ZodError),
          expect.objectContaining({
            validation: expect.arrayContaining([
              expect.objectContaining({
                field: 'data.email',
                error: expect.stringContaining('Invalid email')
              }),
              expect.objectContaining({
                field: 'data.age',
                error: expect.stringContaining('greater than or equal to 0')
              }),
              expect.objectContaining({
                field: 'data.preferences.theme',
                error: expect.stringContaining('Invalid enum value')
              }),
              expect.objectContaining({
                field: 'where.id',
                error: expect.stringContaining('greater than 0')
              })
            ])
          })
        );

        // Also verify the detailed validation error was logged by ErrorLoggingValidator
        expect(mockLogger.error).toHaveBeenCalledWith(
          'Validation error detected',
          expect.objectContaining({
            validationErrors: expect.arrayContaining([
              expect.objectContaining({
                field: 'data.email',
                error: expect.stringContaining('Invalid email')
              }),
              expect.objectContaining({
                field: 'data.age',
                error: expect.stringContaining('greater than or equal to 0')
              }),
              expect.objectContaining({
                field: 'data.preferences.theme', 
                error: expect.stringContaining('Invalid enum value')
              }),
              expect.objectContaining({
                field: 'where.id',
                error: expect.stringContaining('greater than 0')
              })
            ]),
            errorType: 'ZOD_VALIDATION'
          })
        );
      }
    });
  });

  describe('Runtime Error Logging', () => {
    test('should log database connection simulation errors', () => {
      // Simulate a database connection error
      const dbError = new Error('connect ECONNREFUSED 127.0.0.1:5432');
      dbError.name = 'ConnectionError';

      const context = {
        toolName: 'execute_sql',
        operation: 'QUERY_EXECUTION',
        category: 'DATABASE' as const,
        query: 'SELECT * FROM users LIMIT 10',
        parameters: { limit: 10 }
      };

      try {
        errorHandler.handleError(dbError, context);
      } catch (error) {
        // Expected to throw
      }

      // Verify database error logging with context
      expect(mockLogger.error).toHaveBeenCalledWith(
        'execute_sql operation failed',
        expect.any(Error),
        expect.objectContaining({
          toolName: 'execute_sql',
          operation: 'QUERY_EXECUTION',
          category: 'DATABASE',
          query: 'SELECT * FROM users LIMIT 10',
          parameters: { limit: 10 },
          requestId
        })
      );
    });

    test('should log security-related errors with enhanced context', () => {
      // Simulate a permission denied error
      const securityError = new Error('permission denied for table users');
      
      const context = {
        toolName: 'select_records',
        operation: 'SELECT',
        category: 'CRUD' as const,
        schema: 'public',
        table: 'users',
        parameters: {
          columns: ['id', 'email', 'password_hash'], // Attempting to access sensitive data
          where: 'role = \'admin\''
        }
      };

      try {
        errorHandler.handleError(securityError, context);
      } catch (error) {
        // Expected to throw
      }

      // Verify security error context is captured
      expect(mockLogger.error).toHaveBeenCalledWith(
        'select_records operation failed',
        expect.any(Error),
        expect.objectContaining({
          toolName: 'select_records',
          operation: 'SELECT',
          category: 'CRUD',
          schema: 'public',
          table: 'users',
          parameters: expect.objectContaining({
            columns: expect.arrayContaining(['password_hash'])
          }),
          requestId
        })
      );
    });
  });

  describe('Performance and Success Logging', () => {
    test('should log successful operations with metrics', () => {
      const context = {
        toolName: 'select_records',
        operation: 'SELECT',
        category: 'CRUD' as const,
        schema: 'public',
        table: 'users',
        recordCount: 150,
        parameters: {
          columns: ['id', 'username', 'email'],
          limit: 200
        }
      };

      const result = {
        data: Array(150).fill(null).map((_, i) => ({
          id: i + 1,
          username: `user${i + 1}`,
          email: `user${i + 1}@example.com`
        })),
        count: 150
      };

      const duration = 250; // 250ms execution time

      errorHandler.logSuccess(context, result, duration);

      // Verify success logging with metrics
      expect(mockLogger.info).toHaveBeenCalledWith(
        'select_records operation completed successfully',
        expect.objectContaining({
          operation: 'SELECT',
          category: 'CRUD',
          duration: 250,
          recordCount: 150,
          requestId
        })
      );
    });

    test('should log complex operation success with detailed context', () => {
      const context = {
        toolName: 'create_index',
        operation: 'CREATE_INDEX',
        category: 'DDL' as const,
        schema: 'public',
        table: 'users',
        columns: ['email', 'username'],
        parameters: {
          indexName: 'idx_users_email_username',
          table: 'users',
          columns: ['email', 'username'],
          unique: true,
          concurrent: true
        }
      };

      const result = {
        success: true,
        indexName: 'idx_users_email_username',
        createdAt: new Date().toISOString(),
        affectedTable: 'public.users',
        indexSize: '8192 bytes'
      };

      const duration = 1200; // 1.2 seconds for index creation

      errorHandler.logSuccess(context, result, duration);

      expect(mockLogger.info).toHaveBeenCalledWith(
        'create_index operation completed successfully',
        expect.objectContaining({
          operation: 'CREATE_INDEX',
          category: 'DDL',
          duration: 1200,
          requestId
        })
      );
    });
  });

  describe('Error Context Enrichment Validation', () => {
    test('should capture comprehensive error context for debugging', () => {
      // Create a complex error scenario with multiple context layers
      const complexValidationSchema = z.object({
        operation: z.literal('bulk_import'),
        source: z.object({
          format: z.enum(['csv', 'json', 'xlsx']),
          path: z.string().url(),
          headers: z.array(z.string()).min(1)
        }),
        destination: z.object({
          schema: z.string().min(1),
          table: z.string().min(1),
          createIfNotExists: z.boolean()
        }),
        mappings: z.array(z.object({
          sourceColumn: z.string().min(1),
          targetColumn: z.string().min(1),
          transformation: z.enum(['none', 'lowercase', 'uppercase', 'trim']).optional()
        })).min(1),
        options: z.object({
          batchSize: z.number().min(1).max(10000),
          skipErrors: z.boolean(),
          dryRun: z.boolean()
        })
      });

      const invalidComplexData = {
        operation: 'bulk_import',
        source: {
          format: 'xml', // Invalid enum
          path: 'not-a-url', // Invalid URL
          headers: [] // Empty array
        },
        destination: {
          schema: '', // Empty string
          table: 'users',
          createIfNotExists: 'maybe' // Wrong type
        },
        mappings: [], // Empty array
        options: {
          batchSize: 0, // Below minimum
          skipErrors: true,
          dryRun: 'yes' // Wrong type
        }
      };

      const parseResult = complexValidationSchema.safeParse(invalidComplexData);
      
      if (!parseResult.success) {
        const context = {
          toolName: 'bulk_import_data',
          operation: 'BULK_IMPORT',
          category: 'CRUD' as const,
          schema: 'public',
          table: 'users',
          parameters: invalidComplexData,
          validation: extractValidationErrors(parseResult.error)
        };

        try {
          errorHandler.handleError(parseResult.error, context);
        } catch (error) {
          // Expected to throw
        }

        // Verify comprehensive context capture
        expect(mockLogger.error).toHaveBeenCalledWith(
          'bulk_import_data operation failed',
          expect.any(z.ZodError),
          expect.objectContaining({
            toolName: 'bulk_import_data',
            operation: 'BULK_IMPORT',
            category: 'CRUD',
            schema: 'public',
            table: 'users',
            requestId,
            timestamp: expect.any(Number),
            validation: expect.arrayContaining([
              expect.objectContaining({
                field: 'source.format',
                error: expect.stringContaining('Invalid enum value')
              }),
              expect.objectContaining({
                field: 'source.path',
                error: expect.stringContaining('Invalid url')
              }),
              expect.objectContaining({
                field: 'mappings',
                error: expect.stringContaining('at least 1 element')
              })
            ])
          })
        );

        // Also verify the detailed validation error was logged by ErrorLoggingValidator
        expect(mockLogger.error).toHaveBeenCalledWith(
          'Validation error detected',
          expect.objectContaining({
            validationErrors: expect.arrayContaining([
              expect.objectContaining({
                field: 'source.format',
                error: expect.stringContaining('Invalid enum value')
              }),
              expect.objectContaining({
                field: 'source.path',
                error: expect.stringContaining('Invalid url')
              }),
              expect.objectContaining({
                field: 'mappings',
                error: expect.stringContaining('at least 1 element')
              })
            ]),
            errorType: 'ZOD_VALIDATION'
          })
        );
      }
    });
  });

  describe('Request Tracking and Correlation', () => {
    test('should maintain request correlation across error scenarios', () => {
      const scenarios = [
        {
          toolName: 'validate_constraints',
          operation: 'VALIDATE_FK',
          category: 'CONSTRAINT' as const
        },
        {
          toolName: 'process_json',
          operation: 'PARSE',
          category: 'JSON' as const
        },
        {
          toolName: 'deploy_function',
          operation: 'DEPLOY',
          category: 'EDGE_FUNCTION' as const
        }
      ];

      scenarios.forEach((scenario, index) => {
        const error = new Error(`Test error ${index + 1}`);
        
        try {
          errorHandler.handleError(error, scenario);
        } catch (e) {
          // Expected to throw
        }

        // Verify all errors share the same request ID for correlation
        expect(mockLogger.error).toHaveBeenCalledWith(
          `${scenario.toolName} operation failed`,
          expect.any(Error),
          expect.objectContaining({
            requestId: requestId, // Same request ID across all scenarios
            timestamp: expect.any(Number)
          })
        );
      });

      // Verify we captured all three scenarios  
      // Each error scenario generates 2 calls: one from ErrorLoggingValidator and one from UniversalErrorHandler
      expect(mockLogger.error).toHaveBeenCalledTimes(6);
    });
  });
});