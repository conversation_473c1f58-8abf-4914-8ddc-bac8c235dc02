import { describe, it, expect, beforeEach, afterEach, beforeAll, afterAll } from 'vitest';
import { z } from 'zod';
import { createMcpServer } from '@supabase/mcp-utils';
import * as transactionTools from '../src/tools/transaction-management-tools.js';
import * as constraintTools from '../src/tools/constraint-validation-tools.js';
import * as jsonTools from '../src/tools/json-handling-tools.js';
import * as databaseTools from '../src/tools/database-operation-tools.js';
import * as configTools from '../src/tools/development-tools.js';
import { ToolRegistry } from '../src/utils/tool-registry.js';
import { testConfig } from './test-config.js';
import { setupAllMocks } from './mocks.js';

describe('Cross-Tool Workflow Integration', () => {
  let toolRegistry: ToolRegistry;
  let testConfig: any;
  let mocks: any;

  beforeAll(async () => {
    // testConfig is imported as a constant object
    mocks = setupAllMocks();
    toolRegistry = new ToolRegistry();
  });

  beforeEach(async () => {
    // Clear registry and set up fresh test environment
    toolRegistry.clear();
  });

  afterEach(async () => {
    // Clean up any test data or connections
    toolRegistry.clear();
  });

  afterAll(async () => {
    // Clean up test environment
    if (mocks?.cleanup) {
      await mocks.cleanup();
    }
  });

  describe('Transaction Management + Constraint Validation Workflow', () => {
    it('should execute transaction with constraint validation in sequence', async () => {
      // Register transaction and constraint validation tools
      const txTools = transactionTools;
      const constraintToolsList = constraintTools;

      // Simulate a workflow: begin transaction → validate constraints → commit/rollback
      const workflow = {
        steps: [
          { tool: 'begin_transaction', phase: 'setup' },
          { tool: 'validate_foreign_key_constraints', phase: 'validation' },
          { tool: 'validate_check_constraints', phase: 'validation' },
          { tool: 'commit_transaction', phase: 'completion' },
        ],
        rollbackStep: { tool: 'rollback_transaction', phase: 'error_recovery' }
      };

      // Test successful workflow execution
      expect(workflow.steps).toHaveLength(4);
      expect(workflow.steps[0].tool).toBe('begin_transaction');
      expect(workflow.steps[workflow.steps.length - 1].tool).toBe('commit_transaction');
      
      // Verify constraint validation is properly positioned in workflow
      const validationSteps = workflow.steps.filter(step => step.phase === 'validation');
      expect(validationSteps).toHaveLength(2);
      expect(validationSteps.some(step => step.tool.includes('foreign_key'))).toBe(true);
      expect(validationSteps.some(step => step.tool.includes('check_constraints'))).toBe(true);
    });

    it('should handle constraint validation failures with proper rollback', async () => {
      // Simulate constraint validation failure scenario
      const failureWorkflow = {
        steps: [
          { tool: 'begin_transaction', status: 'success' },
          { tool: 'validate_foreign_key_constraints', status: 'failure', error: 'Orphaned records found' },
          { tool: 'rollback_transaction', status: 'success', reason: 'constraint_validation_failed' }
        ]
      };

      // Verify error handling workflow
      expect(failureWorkflow.steps[1].status).toBe('failure');
      expect(failureWorkflow.steps[2].tool).toBe('rollback_transaction');
      expect(failureWorkflow.steps[2].reason).toBe('constraint_validation_failed');
    });

    it('should support transaction savepoints for partial rollbacks', async () => {
      // Test savepoint workflow for complex multi-step operations
      const savepointWorkflow = {
        transaction: 'main_transaction',
        savepoints: [
          { name: 'before_validation', step: 1 },
          { name: 'after_foreign_key_check', step: 2 },
          { name: 'after_check_constraints', step: 3 }
        ],
        rollbackTarget: 'before_validation'
      };

      expect(savepointWorkflow.savepoints).toHaveLength(3);
      expect(savepointWorkflow.savepoints[0].name).toBe('before_validation');
      expect(savepointWorkflow.rollbackTarget).toBe('before_validation');
    });
  });

  describe('JSON Handling + Database Operations Integration', () => {
    it('should process JSON data through validation to database storage', async () => {
      // Simulate JSON processing workflow
      const jsonWorkflow = {
        input: '{"user_id": 123, "profile": {"name": "John", "settings": {}}}',
        steps: [
          { tool: 'validate_json_syntax', status: 'pending' },
          { tool: 'extract_json_values', status: 'pending' },
          { tool: 'transform_json_structure', status: 'pending' },
          { tool: 'execute_sql', operation: 'INSERT', status: 'pending' }
        ]
      };

      // Test JSON validation and extraction
      expect(jsonWorkflow.input).toMatch(/^{.*}$/);
      expect(jsonWorkflow.steps[0].tool).toBe('validate_json_syntax');
      expect(jsonWorkflow.steps[1].tool).toBe('extract_json_values');

      // Verify database operation integration
      const dbStep = jsonWorkflow.steps.find(step => step.tool === 'execute_sql');
      expect(dbStep).toBeDefined();
      expect(dbStep!.operation).toBe('INSERT');
    });

    it('should handle malformed JSON with repair and fallback strategies', async () => {
      // Test malformed JSON handling workflow
      const malformedWorkflow = {
        input: '{"user_id": 123, "name": "John", "data": {invalid}}',
        repairStrategy: 'attempt_repair_then_fallback',
        steps: [
          { tool: 'validate_json_syntax', status: 'failure', error: 'Syntax error' },
          { tool: 'repair_malformed_json', status: 'success', repaired: true },
          { tool: 'validate_json_syntax', status: 'success', attempt: 2 },
          { tool: 'execute_sql', operation: 'INSERT', status: 'success' }
        ]
      };

      expect(malformedWorkflow.steps[0].status).toBe('failure');
      expect(malformedWorkflow.steps[1].tool).toBe('repair_malformed_json');
      expect(malformedWorkflow.steps[2].status).toBe('success');
      expect(malformedWorkflow.steps[2].attempt).toBe(2);
    });

    it('should integrate JSON path querying with database filtering', async () => {
      // Test JSON path extraction with SQL WHERE clause generation
      const pathQueryWorkflow = {
        jsonPath: '$.user.profile.preferences.theme',
        expectedValue: 'dark',
        sqlGeneration: {
          table: 'user_preferences',
          whereClause: "json_extract(data, '$.user.profile.preferences.theme') = 'dark'"
        }
      };

      expect(pathQueryWorkflow.jsonPath).toBe('$.user.profile.preferences.theme');
      expect(pathQueryWorkflow.sqlGeneration.whereClause).toContain('json_extract');
      expect(pathQueryWorkflow.sqlGeneration.whereClause).toContain('dark');
    });
  });

  describe('Configuration Management + Tool Categories Integration', () => {
    it('should propagate configuration changes across all tool categories', async () => {
      // Test configuration change propagation
      const configChangeWorkflow = {
        change: { 
          key: 'database.connection_timeout', 
          oldValue: 30000, 
          newValue: 60000 
        },
        affectedCategories: [
          'database_operations',
          'transaction_management', 
          'constraint_validation',
          'json_handling'
        ],
        propagationSteps: [
          { category: 'database_operations', status: 'updated' },
          { category: 'transaction_management', status: 'updated' },
          { category: 'constraint_validation', status: 'updated' },
          { category: 'json_handling', status: 'not_affected' }
        ]
      };

      expect(configChangeWorkflow.affectedCategories).toHaveLength(4);
      expect(configChangeWorkflow.propagationSteps.filter(step => step.status === 'updated')).toHaveLength(3);
      const jsonStep = configChangeWorkflow.propagationSteps.find(step => step.category === 'json_handling');
      expect(jsonStep).toBeDefined();
      expect(jsonStep!.status).toBe('not_affected');
    });

    it('should validate configuration compatibility across tool categories', async () => {
      // Test configuration validation workflow
      const configValidationWorkflow = {
        configProfile: 'production',
        validationChecks: [
          { category: 'database_operations', check: 'connection_limits', status: 'pass' },
          { category: 'transaction_management', check: 'timeout_settings', status: 'pass' },
          { category: 'constraint_validation', check: 'batch_size_limits', status: 'warning', message: 'Large batch size may impact performance' },
          { category: 'monitoring', check: 'metrics_endpoints', status: 'pass' }
        ],
        overallStatus: 'pass_with_warnings'
      };

      const passChecks = configValidationWorkflow.validationChecks.filter(check => check.status === 'pass');
      const warningChecks = configValidationWorkflow.validationChecks.filter(check => check.status === 'warning');
      
      expect(passChecks).toHaveLength(3);
      expect(warningChecks).toHaveLength(1);
      expect(configValidationWorkflow.overallStatus).toBe('pass_with_warnings');
    });

    it('should support environment-specific tool configuration overrides', async () => {
      // Test environment-specific configuration
      const envConfigWorkflow = {
        environments: {
          development: {
            'database.pool_size': 5,
            'monitoring.detailed_logging': true,
            'constraint_validation.strict_mode': false
          },
          production: {
            'database.pool_size': 20,
            'monitoring.detailed_logging': false,
            'constraint_validation.strict_mode': true
          }
        },
        currentEnv: 'production',
        appliedConfig: {
          'database.pool_size': 20,
          'monitoring.detailed_logging': false,
          'constraint_validation.strict_mode': true
        }
      };

      expect(envConfigWorkflow.environments.development['database.pool_size']).toBe(5);
      expect(envConfigWorkflow.environments.production['database.pool_size']).toBe(20);
      expect(envConfigWorkflow.appliedConfig['constraint_validation.strict_mode']).toBe(true);
    });
  });

  describe('Error Propagation Across Tool Boundaries', () => {
    it('should propagate database errors through transaction and constraint tools', async () => {
      // Test error propagation workflow
      const errorPropagationWorkflow = {
        sourceError: {
          tool: 'execute_sql',
          error: 'Connection timeout',
          errorCode: 'DB_TIMEOUT',
          timestamp: Date.now()
        },
        propagationPath: [
          { tool: 'transaction_management', action: 'rollback_on_error', status: 'triggered' },
          { tool: 'constraint_validation', action: 'cancel_pending_checks', status: 'cancelled' },
          { tool: 'monitoring', action: 'log_error_metrics', status: 'logged' }
        ],
        finalState: 'error_recovered'
      };

      expect(errorPropagationWorkflow.sourceError.tool).toBe('execute_sql');
      expect(errorPropagationWorkflow.propagationPath).toHaveLength(3);
      expect(errorPropagationWorkflow.propagationPath[0].action).toBe('rollback_on_error');
      expect(errorPropagationWorkflow.finalState).toBe('error_recovered');
    });

    it('should handle circuit breaker patterns across tool categories', async () => {
      // Test circuit breaker integration
      const circuitBreakerWorkflow = {
        circuitBreakers: {
          database_operations: { state: 'closed', failureCount: 0, threshold: 5 },
          external_apis: { state: 'open', failureCount: 6, threshold: 5 },
          file_operations: { state: 'half_open', failureCount: 3, threshold: 5 }
        },
        requestRouting: {
          database_request: 'allowed',
          external_api_request: 'blocked',
          file_operation_request: 'test_allowed'
        }
      };

      expect(circuitBreakerWorkflow.circuitBreakers.database_operations.state).toBe('closed');
      expect(circuitBreakerWorkflow.circuitBreakers.external_apis.state).toBe('open');
      expect(circuitBreakerWorkflow.requestRouting.external_api_request).toBe('blocked');
    });

    it('should implement graceful degradation across dependent tools', async () => {
      // Test graceful degradation workflow
      const degradationWorkflow = {
        primaryService: 'constraint_validation',
        serviceStatus: 'degraded',
        fallbackStrategy: {
          level1: 'reduce_validation_scope',
          level2: 'skip_non_critical_validations',
          level3: 'minimal_validation_only'
        },
        currentLevel: 'level2',
        affectedTools: [
          { tool: 'validate_foreign_key_constraints', status: 'reduced_scope' },
          { tool: 'validate_check_constraints', status: 'skipped' },
          { tool: 'validate_unique_constraints', status: 'active' }
        ]
      };

      expect(degradationWorkflow.currentLevel).toBe('level2');
      expect(degradationWorkflow.affectedTools.find(tool => tool.tool === 'validate_check_constraints')?.status).toBe('skipped');
      expect(degradationWorkflow.affectedTools.find(tool => tool.tool === 'validate_unique_constraints')?.status).toBe('active');
    });
  });

  describe('Tool Registry Integration with Cross-Tool Workflows', () => {
    it('should validate tool compatibility before workflow execution', async () => {
      // Test tool compatibility validation
      const compatibilityCheck = {
        workflow: 'transaction_with_constraints',
        requiredTools: [
          'begin_transaction',
          'validate_foreign_key_constraints', 
          'commit_transaction'
        ],
        compatibilityMatrix: {
          'begin_transaction': { 
            compatibleWith: ['validate_foreign_key_constraints', 'commit_transaction'],
            incompatibleWith: []
          },
          'validate_foreign_key_constraints': {
            compatibleWith: ['begin_transaction', 'commit_transaction'],
            requiredPreconditions: ['active_transaction']
          }
        },
        validationResult: 'compatible'
      };

      expect(compatibilityCheck.requiredTools).toHaveLength(3);
      expect(compatibilityCheck.compatibilityMatrix['begin_transaction'].compatibleWith).toContain('validate_foreign_key_constraints');
      expect(compatibilityCheck.validationResult).toBe('compatible');
    });

    it('should track workflow execution statistics across tool categories', async () => {
      // Test workflow execution tracking
      const workflowStats = {
        workflows: {
          'transaction_constraint_workflow': {
            executions: 45,
            success_rate: 0.94,
            average_duration: 1200,
            common_failures: ['constraint_violation', 'timeout']
          },
          'json_database_workflow': {
            executions: 78,
            success_rate: 0.89,
            average_duration: 800,
            common_failures: ['malformed_json', 'database_error']
          }
        },
        crossToolMetrics: {
          total_cross_tool_executions: 123,
          category_interactions: {
            'database_operations+transaction_management': 45,
            'json_handling+database_operations': 78
          }
        }
      };

      expect(workflowStats.workflows['transaction_constraint_workflow'].success_rate).toBeGreaterThan(0.9);
      expect(workflowStats.crossToolMetrics.total_cross_tool_executions).toBe(123);
      expect(workflowStats.crossToolMetrics.category_interactions['json_handling+database_operations']).toBe(78);
    });
  });
}); 