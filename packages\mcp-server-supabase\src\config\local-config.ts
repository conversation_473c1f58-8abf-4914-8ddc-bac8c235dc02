import { z } from 'zod';

export const LocalConfigSchema = z.object({
  // Required configuration
  SUPABASE_URL: z.string().url().default('https://devdb.syncrobit.net'),
  SUPABASE_ANON_KEY: z.string(),
  SUPABASE_SERVICE_ROLE_KEY: z.string(),
  
  // Optional configuration with defaults
  DATABASE_URL: z.string().optional(),
  
  // Development options
  READ_ONLY: z.boolean().default(false),
  DEBUG_SQL: z.boolean().default(false),
});

export type LocalConfig = z.infer<typeof LocalConfigSchema>;

export function getLocalConfig(): LocalConfig {
  const config = LocalConfigSchema.parse({
    SUPABASE_URL: process.env.SUPABASE_URL,
    SUPABASE_ANON_KEY: process.env.SUPABASE_ANON_KEY,
    SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY,
    DATABASE_URL: process.env.DATABASE_URL,
    READ_ONLY: process.env.READ_ONLY === 'true',
    DEBUG_SQL: process.env.DEBUG_SQL === 'true',
  });
  
  if (config.DEBUG_SQL) {
    console.log('Local Supabase Configuration:', {
      url: config.SUPABASE_URL,
      hasAnonKey: !!config.SUPABASE_ANON_KEY,
      hasServiceKey: !!config.SUPABASE_SERVICE_ROLE_KEY,
      readOnly: config.READ_ONLY,
    });
  }
  
  return config;
}

export function validateLocalConfig(config: Partial<LocalConfig>): LocalConfig {
  return LocalConfigSchema.parse(config);
}