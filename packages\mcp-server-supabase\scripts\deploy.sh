#!/bin/bash

# MCP Server Supabase Deployment Script
# This script prepares the package for simplified deployment

set -e

echo "🚀 Preparing MCP Server Supabase for deployment..."

# Change to package directory
cd "$(dirname "$0")/.."

# Clean and build
echo "📦 Building package..."
npm run build

# Create deployment package
echo "📦 Creating deployment package..."
npm pack

# Get the package name
PACKAGE_FILE=$(ls *.tgz | head -n 1)

echo "✅ Deployment package created: $PACKAGE_FILE"
echo ""
echo "🎯 Quick Deployment Options:"
echo ""
echo "Option 1 - NPX (Recommended):"
echo "No installation needed. Just update your MCP config:"
echo '{'
echo '  "mcpServers": {'
echo '    "supabase": {'
echo '      "command": "npx",'
echo '      "args": ['
echo '        "@supabase/mcp-server-supabase",'
echo '        "--supabase-url=YOUR_SUPABASE_URL",'
echo '        "--anon-key=YOUR_ANON_KEY",'
echo '        "--service-key=YOUR_SERVICE_KEY"'
echo '      ]'
echo '    }'
echo '  }'
echo '}'
echo ""
echo "Option 2 - Global Installation:"
echo "npm install -g @supabase/mcp-server-supabase"
echo ""
echo "Option 3 - Local Package (copy $PACKAGE_FILE to target machine):"
echo "npm install ./$PACKAGE_FILE"
echo ""
echo "📚 For complete deployment instructions, see DEPLOYMENT.md"
echo ""
echo "🔧 Test your deployment:"
echo "npx @supabase/mcp-server-supabase --version" 