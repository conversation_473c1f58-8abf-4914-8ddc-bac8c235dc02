import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { z } from 'zod';
import { tool } from '@supabase/mcp-utils';
import { ToolRegistry, type EnhancedTool, type ToolMetadata } from '../src/utils/tool-registry.js';
import { ToolValidator } from '../src/utils/tool-validator.js';
import { ValidationError, ToolError } from '../src/utils/errors.js';

describe('Tool Registry & Compliance System Integration', () => {
  let toolRegistry: ToolRegistry;
  let toolValidator: ToolValidator;

  beforeEach(() => {
    toolRegistry = new ToolRegistry();
    toolValidator = new ToolValidator();
  });

  afterEach(() => {
    toolRegistry.clear();
  });

  describe('Tool Registration and Discovery', () => {
    it('should register a tool with valid metadata following snake_case convention', async () => {
      const metadata: ToolMetadata = {
        name: 'test_tool',
        description: 'A test tool for validation with sufficient description length',
        category: 'testing',
        version: '1.0.0',
        tags: ['test', 'validation'],
        permissions: {
          readOnly: true,
          requiresAuth: false,
        },
        performance: {
          complexity: 'low',
          cacheability: 'short',
        },
      };

      const testTool: EnhancedTool = {
        description: 'A test tool for validation with sufficient description length',
        parameters: z.object({
          param1: z.string(),
          param2: z.number().optional(),
        }),
        execute: async (params: any) => ({ success: true, data: params }),
        metadata,
      };

      expect(() => toolRegistry.register('test_tool', testTool)).not.toThrow();
      expect(toolRegistry.has('test_tool')).toBe(true);
      expect(toolRegistry.get('test_tool')).toEqual(testTool);
    });

    it('should throw error when registering tool with existing name', async () => {
      const metadata: ToolMetadata = {
        name: 'duplicate_tool',
        description: 'A duplicate tool with sufficient description length',
        category: 'testing',
        version: '1.0.0',
        tags: ['test'],
        permissions: { readOnly: false, requiresAuth: false },
        performance: { complexity: 'low', cacheability: 'none' },
      };

      const testTool: EnhancedTool = {
        description: 'A duplicate tool with sufficient description length',
        parameters: z.object({}),
        execute: async () => ({ success: true }),
        metadata,
      };

      toolRegistry.register('duplicate_tool', testTool);
      
      expect(() => toolRegistry.register('duplicate_tool', testTool))
        .toThrow(ValidationError);
    });

    it('should register multiple tools from a group successfully', async () => {
      const tools = {
        group_tool_one: tool({
          description: 'First group tool with sufficient description length',
          parameters: z.object({ param: z.string() }),
          execute: async (params) => ({ result: params.param }),
        }),
        group_tool_two: tool({
          description: 'Second group tool with sufficient description length',
          parameters: z.object({ value: z.number() }),
          execute: async (params) => ({ result: params.value * 2 }),
        }),
      };

      const defaultMetadata: Partial<ToolMetadata> = {
        category: 'test_group',
        version: '2.0.0',
        tags: ['group', 'test'],
        permissions: { readOnly: true, requiresAuth: true },
      };

      expect(() => toolRegistry.registerGroup('test_group', tools, defaultMetadata))
        .not.toThrow();

      expect(toolRegistry.has('group_tool_one')).toBe(true);
      expect(toolRegistry.has('group_tool_two')).toBe(true);
      expect(toolRegistry.getByCategory('test_group')).toHaveLength(2);
    });

    it('should discover tools with various filters', async () => {
      // Register multiple tools with different characteristics
      const tools = [
        {
          name: 'db_read_tool',
          metadata: {
            name: 'db_read_tool',
            description: 'Database read tool with comprehensive functionality',
            category: 'database',
            version: '1.0.0',
            tags: ['database', 'read'],
            permissions: { readOnly: true, requiresAuth: true },
                         performance: { complexity: 'medium' as const, cacheability: 'short' as const },
          },
        },
        {
          name: 'json_processing_tool',
          metadata: {
            name: 'json_processing_tool',
            description: 'JSON processing tool for data manipulation tasks',
            category: 'json_handling',
            version: '1.5.0',
            tags: ['json', 'processing'],
            permissions: { readOnly: false, requiresAuth: true },
                         performance: { complexity: 'high' as const, cacheability: 'none' as const },
          },
        },
        {
          name: 'deprecated_utility_tool',
          metadata: {
            name: 'deprecated_utility_tool',
            description: 'Deprecated utility tool maintained for compatibility',
            category: 'utilities',
            version: '0.9.0',
            tags: ['utility'],
            deprecated: true,
            deprecationMessage: 'Use new_utility_tool instead',
            permissions: { readOnly: true, requiresAuth: false },
                         performance: { complexity: 'low' as const, cacheability: 'long' as const },
          },
        },
      ];

      for (const toolData of tools) {
        const enhancedTool: EnhancedTool = {
          description: toolData.metadata.description,
          parameters: z.object({}),
          execute: async () => ({}),
          metadata: toolData.metadata,
        };
        toolRegistry.register(toolData.name, enhancedTool);
      }

      // Test category filtering
      const dbTools = toolRegistry.discover({ category: 'database' });
      expect(dbTools).toHaveLength(1);
      expect(dbTools[0]?.metadata.name).toBe('db_read_tool');

      // Test tag filtering
      const readTools = toolRegistry.discover({ tags: ['read'] });
      expect(readTools).toHaveLength(1);

      // Test deprecated filtering
      const activeTools = toolRegistry.discover({ deprecated: false });
      expect(activeTools).toHaveLength(2);

      const deprecatedTools = toolRegistry.discover({ deprecated: true });
      expect(deprecatedTools).toHaveLength(1);

      // Test search filtering
      const jsonTools = toolRegistry.discover({ search: 'json' });
      expect(jsonTools).toHaveLength(1);

      // Test read-only filtering
      const readOnlyTools = toolRegistry.discover({ readOnly: true });
      expect(readOnlyTools).toHaveLength(2);
    });
  });

  describe('Tool Validation and Compliance', () => {
    it('should validate tool parameters successfully', async () => {
      const schema = z.object({
        name: z.string().min(1),
        age: z.number().positive(),
        email: z.string().email().optional(),
      });

      const validParams = {
        name: 'John',
        age: 30,
        email: '<EMAIL>',
      };

      const result = await toolValidator.validateParameters(schema, validParams);
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect parameter validation errors', async () => {
      const schema = z.object({
        name: z.string().min(1),
        age: z.number().positive(),
        email: z.string().email(),
      });

      const invalidParams = {
        name: '', // Too short
        age: -5, // Negative
        email: 'not-an-email', // Invalid email
      };

      const result = await toolValidator.validateParameters(schema, invalidParams);
      expect(result.valid).toBe(false);
      expect(result.errors).toHaveLength(3);
      expect(result.errors.some(e => e.field === 'name')).toBe(true);
      expect(result.errors.some(e => e.field === 'age')).toBe(true);
      expect(result.errors.some(e => e.field === 'email')).toBe(true);
    });

    it('should validate snake_case naming convention', async () => {
      const invalidMetadata: ToolMetadata = {
        name: 'invalid-kebab-case',
        description: 'Tool with invalid naming convention but sufficient description',
        category: 'testing',
        version: '1.0.0',
        tags: ['test'],
        permissions: { readOnly: true, requiresAuth: false },
      };

      const testTool: EnhancedTool = {
        description: 'Tool with invalid naming convention but sufficient description',
        parameters: z.object({}),
        execute: async () => ({}),
        metadata: invalidMetadata,
      };

      expect(() => toolRegistry.register('invalid-kebab-case', testTool))
        .toThrow(ValidationError);
    });
  });

  describe('Tool Statistics and Reporting', () => {
    beforeEach(() => {
      // Register sample tools for statistics testing
      const sampleTools = [
        {
          name: 'read_only_tool',
          metadata: {
            name: 'read_only_tool',
            description: 'A read-only tool for safe data access operations',
            category: 'data_access',
            version: '1.0.0',
            tags: ['read', 'safe'],
            permissions: { readOnly: true, requiresAuth: true },
          },
        },
        {
          name: 'write_tool',
          metadata: {
            name: 'write_tool',
            description: 'A write tool for data modification operations',
            category: 'data_access',
            version: '2.0.0',
            tags: ['write', 'modify'],
            permissions: { readOnly: false, requiresAuth: true },
          },
        },
        {
          name: 'utility_tool',
          metadata: {
            name: 'utility_tool',
            description: 'A utility tool for general purpose operations',
            category: 'utilities',
            version: '1.1.0',
            tags: ['utility', 'general'],
            permissions: { readOnly: true, requiresAuth: false },
            deprecated: true,
            deprecationMessage: 'Use modern_utility_tool instead',
          },
        },
      ];

      for (const toolData of sampleTools) {
        const enhancedTool: EnhancedTool = {
          description: toolData.metadata.description,
          parameters: z.object({}),
          execute: async () => ({}),
          metadata: toolData.metadata,
        };
        toolRegistry.register(toolData.name, enhancedTool);
      }
    });

    it('should provide accurate tool statistics', async () => {
      const stats = toolRegistry.getStatistics();
      
      expect(stats.totalTools).toBe(3);
      expect(stats.categories['data_access']).toBe(2);
      expect(stats.categories['utilities']).toBe(1);
      expect(stats.deprecated).toBe(1);
      expect(stats.readOnly).toBe(2);
      expect(stats.versions['1.0.0']).toBe(1);
      expect(stats.versions['2.0.0']).toBe(1);
      expect(stats.versions['1.1.0']).toBe(1);
    });

    it('should list tool categories correctly', async () => {
      const categories = toolRegistry.getCategories();
      
      expect(categories).toContain('data_access');
      expect(categories).toContain('utilities');
      expect(categories).toHaveLength(2);
    });

    it('should convert tools to MCP format', async () => {
      const mcpTools = toolRegistry.toMcpFormat();
      
      expect(mcpTools).toHaveLength(3);
      
      // Check that deprecated tools are marked
      const deprecatedTool = mcpTools.find(tool => tool.name === 'utility_tool');
      expect(deprecatedTool?.description).toContain('[DEPRECATED]');
      expect(deprecatedTool?.description).toContain('Use modern_utility_tool instead');
      
      // Check that non-deprecated tools are not marked
      const readOnlyTool = mcpTools.find(tool => tool.name === 'read_only_tool');
      expect(readOnlyTool?.description).not.toContain('[DEPRECATED]');
    });
  });
}); 