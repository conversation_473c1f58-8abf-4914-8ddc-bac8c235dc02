# Supabase MCP Server Wrapper Script (No Profile)
# Sets required environment variables and launches the MCP server
# Use with: powershell -NoProfile -ExecutionPolicy Bypass -File script.ps1

$env:SUPABASE_URL = "https://devdb.syncrobit.net"
$env:SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogImFub24iLAogICJpc3MiOiAic3VwYWJhc2UiLAogICJpYXQiOiAxNzQxMDk1MDAwLAogICJleHAiOiAxODk4ODYxNDAwCn0.J1N7sT41EI4ORiRFvPm3FPDArs43dzEq9g9-iq7d3pY"
$env:SUPABASE_SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogInNlcnZpY2Vfcm9sZSIsCiAgImlzcyI6ICJzdXBhYmFzZSIsCiAgImlhdCI6IDE3NDEwOTUwMDAsCiAgImV4cCI6IDE4OTg4NjE0MDAKfQ.4OLZbhrbccD5vfy14HcWgYDgJySzUXimxgcGA3OgaV8"
$env:READ_ONLY = "false"
$env:DEBUG_SQL = "false"

# Launch the MCP server
& mcp-server-supabase @args