import { z } from 'zod';
import type { SupabasePlatform } from '../platform/types.js';
import { injectableTool } from './util.js';

export type MonitoringToolsOptions = {
  platform: SupabasePlatform;
  projectId?: string;
  contentApiClient?: any;
};

export function getEssentialMonitoringTools({
  platform,
  projectId,
  contentApiClient,
}: MonitoringToolsOptions) {
  const project_id = projectId;

  return {
    check_local_connection: injectableTool({
      description: 'Verifies connection to the local Supabase instance',
      parameters: z.object({
        project_id: z.string(),
      }),
      inject: { project_id },
      execute: async ({ project_id }) => {
        try {
          // Try a simple query to verify connection
          await platform.executeSql(project_id, {
            query: 'SELECT 1 as connection_test',
            read_only: true,
          });
          
          return {
            connected: true,
            message: 'Successfully connected to local Supabase instance',
            url: process.env.SUPABASE_URL || 'http://localhost:54321',
          };
        } catch (error) {
          return {
            connected: false,
            message: `Connection failed: ${error instanceof Error ? error.message : String(error)}`,
            url: process.env.SUPABASE_URL || 'http://localhost:54321',
          };
        }
      },
    }),

    get_local_config: injectableTool({
      description: 'Display current local Supabase configuration',
      parameters: z.object({
        project_id: z.string(),
      }),
      inject: { project_id },
      execute: async ({ project_id }) => {
        return {
          project_id,
          supabase_url: process.env.SUPABASE_URL || 'http://localhost:54321',
          has_anon_key: !!process.env.SUPABASE_ANON_KEY,
          has_service_role_key: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
          database_url: process.env.DATABASE_URL ? 'configured' : 'not configured',
          environment: 'local',
        };
      },
    }),

    search_docs: injectableTool({
      description: 'Searches the Supabase documentation for up-to-date information. LLMs can use this to find answers to questions or learn how to use specific features.',
      parameters: z.object({
        query: z
          .string()
          .min(1, 'Search query cannot be empty')
          .max(200, 'Search query cannot exceed 200 characters')
          .describe('Search query for Supabase documentation'),
        limit: z
          .number()
          .int()
          .min(1)
          .max(20)
          .default(5)
          .optional()
          .describe('Maximum number of results to return'),
      }),
      execute: async ({ query, limit = 5 }) => {
        if (!contentApiClient) {
          return {
            error: 'Documentation search not available in local environment',
            suggestion: 'Visit https://supabase.com/docs directly for documentation',
            query,
          };
        }

        try {
          const graphqlQuery = `
            query SearchDocs($query: String!, $limit: Int) {
              searchDocs(query: $query, limit: $limit) {
                nodes {
                  title
                  href
                  content
                  ... on Guide {
                    subsections {
                      nodes {
                        title
                        href
                        content
                      }
                    }
                  }
                }
              }
            }
          `;

          const response = await contentApiClient.request(graphqlQuery, { query, limit });
          
          if (response?.searchDocs?.nodes) {
            return {
              results: response.searchDocs.nodes.map((node: any) => ({
                title: node.title,
                href: node.href,
                content: node.content,
                subsections: node.subsections?.nodes || [],
              })),
              total_results: response.searchDocs.nodes.length,
              query,
            };
          }

          return {
            results: [],
            total_results: 0,
            query,
            message: 'No documentation results found',
          };
        } catch (error) {
          return {
            error: `Documentation search failed: ${error instanceof Error ? error.message : String(error)}`,
            suggestion: 'Visit https://supabase.com/docs directly for documentation',
            query,
          };
        }
      },
    }),
  };
} 