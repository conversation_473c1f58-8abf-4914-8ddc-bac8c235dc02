/**
 * Test configuration for database connections
 * Provides isolated and secure database connection setup for integration tests
 * Enhanced with comprehensive external dependency mocking
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { vi } from 'vitest';
import { setupAllMocks, validateMockSetup, mockEnvVars } from './mocks';

// Environment variables with fallbacks - enhanced with mock support
const TEST_SUPABASE_URL = process.env.SUPABASE_URL || mockEnvVars.SUPABASE_URL || 'http://192.168.1.218:54321';
const TEST_SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY || mockEnvVars.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogImFub24iLAogICJpc3MiOiAic3VwYWJhc2UiLAogICJpYXQiOiAxNzQxMDk1MDAwLAogICJleHAiOiAxODk4ODYxNDAwCn0.J1N7sT41EI4ORiRFvPm3FPDArs43dzEq9g9-iq7d3pY';
const TEST_SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || mockEnvVars.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogInNlcnZpY2Vfcm9sZSIsCiAgImlzcyI6ICJzdXBhYmFzZSIsCiAgImlhdCI6IDE3NDEwOTUwMDAsCiAgImV4cCI6IDE4OTg4NjE0MDAKfQ.4OLZbhrbccD5vfy14HcWgYDgJsSzUXimxgcGA3OgaV8';

/**
 * Test database configuration
 */
export const testConfig = {
  supabaseUrl: TEST_SUPABASE_URL,
  anonKey: TEST_SUPABASE_ANON_KEY,
  serviceKey: TEST_SUPABASE_SERVICE_KEY,
  
  // Test-specific settings
  testTimeout: 30000,
  maxRetries: 3,
  retryDelay: 1000,
  performanceThreshold: 2000, // 2 seconds for query performance
  
  // Schema configurations
  schemas: {
    public: 'public',
    memoryMaster: 'memory_master'
  }
};

/**
 * Create a Supabase client for testing with public schema access
 */
export function createTestClient(): SupabaseClient {
  return createClient(testConfig.supabaseUrl, testConfig.anonKey, {
    auth: {
      persistSession: false,
      autoRefreshToken: false
    },
    db: {
      schema: testConfig.schemas.public
    }
  });
}

/**
 * Create a Supabase client for testing with service role access
 */
export function createServiceTestClient(): SupabaseClient {
  return createClient(testConfig.supabaseUrl, testConfig.serviceKey, {
    auth: {
      persistSession: false,
      autoRefreshToken: false
    },
    db: {
      schema: testConfig.schemas.public
    }
  });
}

/**
 * Create a Supabase client for testing memory_master schema
 */
export function createMemoryMasterTestClient(): SupabaseClient {
  return createClient(testConfig.supabaseUrl, testConfig.serviceKey, {
    auth: {
      persistSession: false,
      autoRefreshToken: false
    },
    db: {
      schema: testConfig.schemas.memoryMaster
    }
  });
}

/**
 * Validate test environment configuration with enhanced mock validation
 */
export function validateTestEnvironment(): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!testConfig.supabaseUrl) {
    errors.push('SUPABASE_URL is not configured');
  }

  if (!testConfig.anonKey) {
    errors.push('SUPABASE_ANON_KEY is not configured');
  }

  if (!testConfig.serviceKey) {
    errors.push('SUPABASE_SERVICE_ROLE_KEY is not configured');
  }

  // Validate URL format
  if (testConfig.supabaseUrl && !testConfig.supabaseUrl.match(/^https?:\/\/.+/)) {
    errors.push('SUPABASE_URL must be a valid HTTP/HTTPS URL');
  }

  // Validate JWT format (basic check) - skip for mock tokens
  const jwtPattern = /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/;
  if (testConfig.anonKey && !testConfig.anonKey.startsWith('test-') && !jwtPattern.test(testConfig.anonKey)) {
    errors.push('SUPABASE_ANON_KEY must be a valid JWT token');
  }

  if (testConfig.serviceKey && !testConfig.serviceKey.startsWith('test-') && !jwtPattern.test(testConfig.serviceKey)) {
    errors.push('SUPABASE_SERVICE_ROLE_KEY must be a valid JWT token');
  }

  // Validate mock setup if in test environment
  if (process.env.NODE_ENV === 'test') {
    const mockValidation = validateMockSetup();
    if (!mockValidation.valid) {
      errors.push(...mockValidation.issues);
    }
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Setup test environment with comprehensive mocking
 */
export function setupTestEnvironment(options: {
  enableMocks?: boolean;
  mockOverrides?: Record<string, string>;
} = {}) {
  const { enableMocks = true, mockOverrides = {} } = options;

  if (enableMocks && process.env.NODE_ENV === 'test') {
    return setupAllMocks({
      env: mockOverrides,
    });
  }

  return null;
}

/**
 * Validate that tests are properly isolated from external dependencies
 */
export function validateTestIsolation(): { isolated: boolean; issues: string[] } {
  const issues: string[] = [];

  // Check for real network access
  if (process.env.NODE_ENV === 'test' && !process.env.CI) {
    // In local test environment, ensure we're not hitting real services
    if (testConfig.supabaseUrl.includes('supabase.co') && !testConfig.supabaseUrl.includes('test') && !testConfig.supabaseUrl.includes('devdb')) {
      issues.push('Tests may be hitting real Supabase services');
    }
  }

  // Check for deterministic time (only if fake timers are enabled)
  if (process.env.NODE_ENV === 'test' && vi && vi.isFakeTimers && vi.isFakeTimers()) {
    const now1 = Date.now();
    const now2 = Date.now();
    if (now2 > now1) {
      issues.push('Time is not mocked - tests may be non-deterministic');
    }
  }

  return {
    isolated: issues.length === 0,
    issues
  };
}

/**
 * Test database connection with retry logic
 */
export async function testDatabaseConnection(
  client: SupabaseClient,
  schema: string = 'public'
): Promise<{ connected: boolean; error?: string }> {
  let lastError: string | undefined;
  
  for (let attempt = 1; attempt <= testConfig.maxRetries; attempt++) {
    try {
      // Try a simple query to test connection
      const { error } = await client
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', schema)
        .limit(1);
      
      if (!error) {
        return { connected: true };
      }
      
      lastError = error.message;
    } catch (err) {
      lastError = err instanceof Error ? err.message : 'Unknown error';
    }
    
    if (attempt < testConfig.maxRetries) {
      await new Promise(resolve => setTimeout(resolve, testConfig.retryDelay));
    }
  }
  
  return {
    connected: false,
    error: lastError || 'Connection failed after retries'
  };
}

/**
 * Clean up test data (for future use when write operations are enabled)
 */
export async function cleanupTestData(client: SupabaseClient): Promise<void> {
  // This function is prepared for future use when write operations are enabled
  // Currently, tests should only perform read operations
      // Test cleanup - read-only mode, no cleanup needed
}

/**
 * Get test database statistics
 */
export async function getTestDatabaseStats(): Promise<{
  users: number;
  apps: number;
  memories: number;
  schemas: string[];
}> {
  const client = createMemoryMasterTestClient();
  
  try {
    // Get row counts for main tables
    const [usersResult, appsResult, memoriesResult] = await Promise.all([
      client.from('users').select('*', { count: 'exact', head: true }),
      client.from('apps').select('*', { count: 'exact', head: true }),
      client.from('memories').select('*', { count: 'exact', head: true })
    ]);
    
    return {
      users: usersResult.count || 0,
      apps: appsResult.count || 0,
      memories: memoriesResult.count || 0,
      schemas: ['public', 'memory_master', 'auth', 'storage'] // Known schemas
    };
  } catch (error) {
    console.warn('Failed to get database stats:', error);
    return {
      users: 0,
      apps: 0,
      memories: 0,
      schemas: ['public', 'memory_master']
    };
  }
}