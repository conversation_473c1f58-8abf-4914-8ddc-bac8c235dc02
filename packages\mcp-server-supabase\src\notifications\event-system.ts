/**
 * Event-Driven System for MCP Server
 * 
 * Provides a centralized event system for managing internal server events,
 * tool executions, database operations, and configuration changes.
 */

import { EventEmitter } from 'events';
import { contextLogger } from '../utils/logger.js';
import { NotificationManager, type NotificationPayload, NotificationTypes } from './notification-manager.js';
import { ProtocolCommunication } from './protocol-communication.js';
import type { ConfigManager } from '../config/index.js';

export interface ServerEvent {
  id: string;
  type: string;
  source: string;
  timestamp: Date;
  data: Record<string, unknown>;
  correlationId?: string; // For tracing related events
  userId?: string; // For user-specific events
  sessionId?: string; // For session-specific events
}

export interface EventFilter {
  type?: string | string[];
  source?: string | string[];
  userId?: string;
  sessionId?: string;
  correlationId?: string;
  timeRange?: {
    start: Date;
    end: Date;
  };
}

export interface EventSubscription {
  id: string;
  filter: EventFilter;
  handler: (event: ServerEvent) => Promise<void> | void;
  active: boolean;
  createdAt: Date;
  lastTriggered?: Date;
  triggerCount: number;
}

export interface EventStats {
  totalEvents: number;
  totalSubscriptions: number;
  eventsByType: Map<string, number>;
  eventsBySource: Map<string, number>;
  averageProcessingTime: number;
  uptime: number;
}

export class EventSystem extends EventEmitter {
  private readonly logger = contextLogger.child({ component: 'EventSystem' });
  private readonly notificationManager: NotificationManager;
  private readonly protocolCommunication?: ProtocolCommunication;
  private readonly config?: ConfigManager;

  // Event management
  private subscriptions = new Map<string, EventSubscription>();
  private eventHistory: ServerEvent[] = [];
  private readonly maxHistorySize: number;
  private processingTimes: number[] = [];
  private readonly maxProcessingTimes = 100;

  // Statistics
  private stats: EventStats;
  private readonly startTime = Date.now();

  // Configuration
  private readonly enableHistory: boolean;
  private readonly enableMetrics: boolean;
  private readonly debugEvents: boolean;

  constructor(
    notificationManager: NotificationManager,
    protocolCommunication?: ProtocolCommunication,
    config?: ConfigManager
  ) {
    super();
    
    this.notificationManager = notificationManager;
    this.protocolCommunication = protocolCommunication;
    this.config = config;

    // Load configuration
    this.maxHistorySize = config?.get('EVENT_HISTORY_SIZE', 1000) ?? 1000;
    this.enableHistory = config?.get('EVENT_ENABLE_HISTORY', true) ?? true;
    this.enableMetrics = config?.get('EVENT_ENABLE_METRICS', true) ?? true;
    this.debugEvents = config?.get('EVENT_DEBUG_LOGGING', false) ?? false;

    // Initialize stats
    this.stats = {
      totalEvents: 0,
      totalSubscriptions: 0,
      eventsByType: new Map(),
      eventsBySource: new Map(),
      averageProcessingTime: 0,
      uptime: 0,
    };

    this.setupCoreEventHandlers();
    this.logger.info('Event system initialized', {
      maxHistorySize: this.maxHistorySize,
      enableHistory: this.enableHistory,
      enableMetrics: this.enableMetrics,
    });
  }

  /**
   * Emit a server event
   */
  async emitEvent(
    type: string,
    source: string,
    data: Record<string, unknown>,
    options: {
      correlationId?: string;
      userId?: string;
      sessionId?: string;
      notifyClients?: boolean;
      priority?: NotificationPayload['priority'];
    } = {}
  ): Promise<void> {
    const startTime = Date.now();

    try {
      const event: ServerEvent = {
        id: this.generateEventId(),
        type,
        source,
        timestamp: new Date(),
        data,
        correlationId: options.correlationId,
        userId: options.userId,
        sessionId: options.sessionId,
      };

      if (this.debugEvents) {
        this.logger.debug('Emitting event', {
          id: event.id,
          type,
          source,
          correlationId: options.correlationId,
        });
      }

      // Update statistics
      this.updateStats(event);

      // Add to history if enabled
      if (this.enableHistory) {
        this.addToHistory(event);
      }

      // Process subscriptions
      await this.processSubscriptions(event);

      // Send notification if requested
      if (options.notifyClients !== false) {
        await this.notificationManager.notifySimple(
          `event.${type}`,
          {
            eventId: event.id,
            source,
            data,
            correlationId: options.correlationId,
          },
          {
            source: 'event-system',
            priority: options.priority ?? 'medium',
          }
        );
      }

      // Emit on EventEmitter for internal listeners
      super.emit('event', event);
      super.emit(type, event);

      this.logger.debug('Event emitted successfully', {
        id: event.id,
        type,
        subscriptionsTriggered: this.getMatchingSubscriptions(event).length,
      });

    } catch (error) {
      this.logger.error('Failed to emit event', error instanceof Error ? error : undefined, {
        type,
        source,
      });
      throw error;
    } finally {
      if (this.enableMetrics) {
        const processingTime = Date.now() - startTime;
        this.recordProcessingTime(processingTime);
      }
    }
  }

  /**
   * Subscribe to events with a filter
   */
  subscribe(
    filter: EventFilter,
    handler: (event: ServerEvent) => Promise<void> | void
  ): string {
    const subscription: EventSubscription = {
      id: this.generateSubscriptionId(),
      filter,
      handler,
      active: true,
      createdAt: new Date(),
      triggerCount: 0,
    };

    this.subscriptions.set(subscription.id, subscription);
    this.stats.totalSubscriptions = this.subscriptions.size;

    this.logger.debug('Event subscription created', {
      id: subscription.id,
      filter,
    });

    return subscription.id;
  }

  /**
   * Unsubscribe from events
   */
  unsubscribe(subscriptionId: string): boolean {
    const subscription = this.subscriptions.get(subscriptionId);
    if (!subscription) {
      return false;
    }

    subscription.active = false;
    this.subscriptions.delete(subscriptionId);
    this.stats.totalSubscriptions = this.subscriptions.size;

    this.logger.debug('Event subscription removed', { id: subscriptionId });
    return true;
  }

  /**
   * Get event history with optional filtering
   */
  getEventHistory(filter?: EventFilter, limit?: number): ServerEvent[] {
    if (!this.enableHistory) {
      this.logger.warn('Event history is disabled');
      return [];
    }

    let events = this.eventHistory;

    if (filter) {
      events = events.filter(event => this.matchesFilter(event, filter));
    }

    if (limit && limit > 0) {
      events = events.slice(-limit);
    }

    return events;
  }

  /**
   * Get event statistics
   */
  getStats(): EventStats {
    return {
      ...this.stats,
      uptime: Date.now() - this.startTime,
      averageProcessingTime: this.calculateAverageProcessingTime(),
    };
  }

  /**
   * Get active subscriptions
   */
  getSubscriptions(): EventSubscription[] {
    return Array.from(this.subscriptions.values())
      .filter(sub => sub.active);
  }

  /**
   * Clear event history
   */
  clearHistory(): void {
    if (this.enableHistory) {
      const count = this.eventHistory.length;
      this.eventHistory.length = 0;
      this.logger.info('Event history cleared', { count });
    }
  }

  /**
   * Shutdown event system
   */
  shutdown(): void {
    // Deactivate all subscriptions
    for (const subscription of this.subscriptions.values()) {
      subscription.active = false;
    }

    this.subscriptions.clear();
    this.eventHistory.length = 0;
    this.removeAllListeners();

    this.logger.info('Event system shutdown complete');
  }

  /**
   * Setup core event handlers for common server events
   */
  private setupCoreEventHandlers(): void {
    // Tool execution events
    this.subscribe(
      { type: 'tool.execution.started' },
      async (event) => {
        this.logger.info('Tool execution started', {
          toolName: event.data.toolName,
          correlationId: event.correlationId,
        });
      }
    );

    this.subscribe(
      { type: 'tool.execution.completed' },
      async (event) => {
        this.logger.info('Tool execution completed', {
          toolName: event.data.toolName,
          duration: event.data.duration,
          correlationId: event.correlationId,
        });
      }
    );

    this.subscribe(
      { type: 'tool.execution.failed' },
      async (event) => {
        this.logger.error('Tool execution failed', undefined, {
          toolName: event.data.toolName,
          error: event.data.error,
          correlationId: event.correlationId,
        });
      }
    );

    // Database events
    this.subscribe(
      { type: 'database.query.slow' },
      async (event) => {
        this.logger.warn('Slow database query detected', {
          query: event.data.query,
          duration: event.data.duration,
          threshold: event.data.threshold,
        });
      }
    );

    // Configuration change events
    this.subscribe(
      { type: 'config.changed' },
      async (event) => {
        this.logger.info('Configuration changed', {
          key: event.data.key,
          source: event.data.source,
        });
      }
    );

    // Health check events
    this.subscribe(
      { type: 'health.check.failed' },
      async (event) => {
        this.logger.error('Health check failed', undefined, {
          checkName: event.data.checkName,
          error: event.data.error,
        });
      }
    );
  }

  /**
   * Process event subscriptions
   */
  private async processSubscriptions(event: ServerEvent): Promise<void> {
    const matchingSubscriptions = this.getMatchingSubscriptions(event);

    if (matchingSubscriptions.length === 0) {
      return;
    }

    const promises = matchingSubscriptions.map(async (subscription) => {
      try {
        await subscription.handler(event);
        subscription.triggerCount++;
        subscription.lastTriggered = new Date();
      } catch (error) {
        this.logger.error('Event subscription handler failed', error instanceof Error ? error : undefined, {
          subscriptionId: subscription.id,
          eventId: event.id,
          eventType: event.type,
        });
      }
    });

    await Promise.allSettled(promises);
  }

  /**
   * Get subscriptions matching an event
   */
  private getMatchingSubscriptions(event: ServerEvent): EventSubscription[] {
    return Array.from(this.subscriptions.values())
      .filter(subscription => 
        subscription.active && this.matchesFilter(event, subscription.filter)
      );
  }

  /**
   * Check if an event matches a filter
   */
  private matchesFilter(event: ServerEvent, filter: EventFilter): boolean {
    // Type filter
    if (filter.type) {
      const types = Array.isArray(filter.type) ? filter.type : [filter.type];
      if (!types.includes(event.type)) {
        return false;
      }
    }

    // Source filter
    if (filter.source) {
      const sources = Array.isArray(filter.source) ? filter.source : [filter.source];
      if (!sources.includes(event.source)) {
        return false;
      }
    }

    // User ID filter
    if (filter.userId && event.userId !== filter.userId) {
      return false;
    }

    // Session ID filter
    if (filter.sessionId && event.sessionId !== filter.sessionId) {
      return false;
    }

    // Correlation ID filter
    if (filter.correlationId && event.correlationId !== filter.correlationId) {
      return false;
    }

    // Time range filter
    if (filter.timeRange) {
      const eventTime = event.timestamp.getTime();
      const startTime = filter.timeRange.start.getTime();
      const endTime = filter.timeRange.end.getTime();
      
      if (eventTime < startTime || eventTime > endTime) {
        return false;
      }
    }

    return true;
  }

  /**
   * Update statistics
   */
  private updateStats(event: ServerEvent): void {
    this.stats.totalEvents++;

    // Update type statistics
    const typeCount = this.stats.eventsByType.get(event.type) ?? 0;
    this.stats.eventsByType.set(event.type, typeCount + 1);

    // Update source statistics
    const sourceCount = this.stats.eventsBySource.get(event.source) ?? 0;
    this.stats.eventsBySource.set(event.source, sourceCount + 1);
  }

  /**
   * Add event to history
   */
  private addToHistory(event: ServerEvent): void {
    this.eventHistory.push(event);

    // Trim history if it exceeds max size
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory.shift();
    }
  }

  /**
   * Record processing time for statistics
   */
  private recordProcessingTime(time: number): void {
    this.processingTimes.push(time);
    if (this.processingTimes.length > this.maxProcessingTimes) {
      this.processingTimes.shift();
    }
  }

  /**
   * Calculate average processing time
   */
  private calculateAverageProcessingTime(): number {
    if (this.processingTimes.length === 0) return 0;
    const sum = this.processingTimes.reduce((a, b) => a + b, 0);
    return sum / this.processingTimes.length;
  }

  /**
   * Generate unique event ID
   */
  private generateEventId(): string {
    return `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate unique subscription ID
   */
  private generateSubscriptionId(): string {
    return `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * Predefined event types for common server operations
 */
export const EventTypes = {
  // Server lifecycle
  SERVER_STARTING: 'server.starting',
  SERVER_STARTED: 'server.started',
  SERVER_STOPPING: 'server.stopping',
  SERVER_STOPPED: 'server.stopped',
  SERVER_ERROR: 'server.error',
  
  // Tool operations
  TOOL_REGISTERED: 'tool.registered',
  TOOL_UNREGISTERED: 'tool.unregistered',
  TOOL_EXECUTION_STARTED: 'tool.execution.started',
  TOOL_EXECUTION_COMPLETED: 'tool.execution.completed',
  TOOL_EXECUTION_FAILED: 'tool.execution.failed',
  
  // Database operations
  DATABASE_CONNECTED: 'database.connected',
  DATABASE_DISCONNECTED: 'database.disconnected',
  DATABASE_ERROR: 'database.error',
  DATABASE_QUERY_STARTED: 'database.query.started',
  DATABASE_QUERY_COMPLETED: 'database.query.completed',
  DATABASE_QUERY_FAILED: 'database.query.failed',
  DATABASE_QUERY_SLOW: 'database.query.slow',
  
  // Configuration management
  CONFIG_LOADED: 'config.loaded',
  CONFIG_CHANGED: 'config.changed',
  CONFIG_VALIDATION_FAILED: 'config.validation.failed',
  CONFIG_RELOAD_REQUESTED: 'config.reload.requested',
  
  // Health and monitoring
  HEALTH_CHECK_STARTED: 'health.check.started',
  HEALTH_CHECK_PASSED: 'health.check.passed',
  HEALTH_CHECK_FAILED: 'health.check.failed',
  PERFORMANCE_THRESHOLD_EXCEEDED: 'performance.threshold.exceeded',
  MEMORY_USAGE_HIGH: 'memory.usage.high',
  
  // Client connections
  CLIENT_CONNECTED: 'client.connected',
  CLIENT_DISCONNECTED: 'client.disconnected',
  CLIENT_AUTHENTICATED: 'client.authenticated',
  CLIENT_AUTHORIZATION_FAILED: 'client.authorization.failed',
  
  // Resource management
  RESOURCE_CREATED: 'resource.created',
  RESOURCE_UPDATED: 'resource.updated',
  RESOURCE_DELETED: 'resource.deleted',
  RESOURCE_ACCESS_DENIED: 'resource.access.denied',
} as const;

export type EventType = typeof EventTypes[keyof typeof EventTypes];