import { describe, test, expect, beforeAll, afterAll } from 'vitest';
import { getConstraintValidationTools } from '../src/tools/constraint-validation-tools.js';
import { createLocalSupabasePlatform } from '../src/platform/local-platform.js';
import { getLocalConfig } from '../src/config/local-config.js';

describe('Constraint Validation Tools Integration', () => {
  let platform: any;
  let tools: any;
  const projectId = 'local';

  beforeAll(async () => {
    // Initialize platform with local config
    const config = getLocalConfig();
    platform = createLocalSupabasePlatform(config);
    
    // Get constraint validation tools
    tools = getConstraintValidationTools({
      platform,
      projectId,
      readOnly: true,
    });

    // Create test tables with constraints for testing
    try {
      await platform.executeSql(projectId, {
        query: `
          -- Create test schema if not exists
          CREATE SCHEMA IF NOT EXISTS test_constraints;
          
          -- Create parent table for foreign key tests
          CREATE TABLE IF NOT EXISTS test_constraints.parent_table (
            id SERIAL PRIMARY KEY,
            name VARCHAR(100) NOT NULL UNIQUE,
            email VARCHAR(255) UNIQUE,
            age INTEGER CHECK (age >= 0 AND age <= 150),
            created_at TIMESTAMPTZ DEFAULT NOW()
          );
          
          -- Create child table with foreign key constraint
          CREATE TABLE IF NOT EXISTS test_constraints.child_table (
            id SERIAL PRIMARY KEY,
            parent_id INTEGER REFERENCES test_constraints.parent_table(id),
            description TEXT NOT NULL,
            status VARCHAR(20) CHECK (status IN ('active', 'inactive', 'pending')),
            created_at TIMESTAMPTZ DEFAULT NOW()
          );
          
          -- Insert test data
          INSERT INTO test_constraints.parent_table (name, email, age) VALUES
            ('John Doe', '<EMAIL>', 30),
            ('Jane Smith', '<EMAIL>', 25),
            ('Bob Wilson', '<EMAIL>', 45)
          ON CONFLICT (name) DO NOTHING;
          
          INSERT INTO test_constraints.child_table (parent_id, description, status) VALUES
            (1, 'Test record 1', 'active'),
            (2, 'Test record 2', 'inactive'),
            (3, 'Test record 3', 'pending')
          ON CONFLICT DO NOTHING;
        `,
        read_only: false,
      });
    } catch (error) {
      console.warn('Failed to create test tables (may already exist):', error);
    }
  });

  afterAll(async () => {
    // Clean up test tables
    try {
      await platform.executeSql(projectId, {
        query: `
          DROP TABLE IF EXISTS test_constraints.child_table CASCADE;
          DROP TABLE IF EXISTS test_constraints.parent_table CASCADE;
          DROP SCHEMA IF EXISTS test_constraints CASCADE;
        `,
        read_only: false,
      });
    } catch (error) {
      console.warn('Failed to clean up test tables:', error);
    }
  });

  describe('validate_foreign_key_constraints', () => {
    test('should validate foreign key constraints successfully', async () => {
      const result = await tools.validate_foreign_key_constraints.execute({
        project_id: projectId,
        schema: 'test_constraints',
        include_violation_details: false,
      });

      expect(result).toBeDefined();
      expect(result.schema).toBe('test_constraints');
      expect(result.total_constraints_checked).toBeGreaterThanOrEqual(0);
      expect(result.results).toBeInstanceOf(Array);
      
      // Check that results have the expected structure
      if (result.results.length > 0) {
        const firstResult = result.results[0];
        expect(firstResult).toHaveProperty('constraint_name');
        expect(firstResult).toHaveProperty('constraint_type');
        expect(firstResult).toHaveProperty('table_schema');
        expect(firstResult).toHaveProperty('table_name');
        expect(firstResult).toHaveProperty('is_valid');
        expect(firstResult).toHaveProperty('violation_count');
        expect(firstResult.constraint_type).toBe('FOREIGN KEY');
      }
    });

    test('should validate specific table foreign key constraints', async () => {
      const result = await tools.validate_foreign_key_constraints.execute({
        project_id: projectId,
        schema: 'test_constraints',
        table: 'child_table',
        include_violation_details: false,
      });

      expect(result).toBeDefined();
      expect(result.schema).toBe('test_constraints');
      expect(result.table).toBe('child_table');
      expect(result.results).toBeInstanceOf(Array);
    });
  });

  describe('validate_unique_constraints', () => {
    test('should validate unique constraints successfully', async () => {
      const result = await tools.validate_unique_constraints.execute({
        project_id: projectId,
        schema: 'test_constraints',
        include_violation_details: false,
      });

      expect(result).toBeDefined();
      expect(result.schema).toBe('test_constraints');
      expect(result.total_constraints_checked).toBeGreaterThanOrEqual(0);
      expect(result.results).toBeInstanceOf(Array);
      
      // Check that results have the expected structure
      if (result.results.length > 0) {
        const firstResult = result.results[0];
        expect(firstResult).toHaveProperty('constraint_name');
        expect(firstResult).toHaveProperty('constraint_type');
        expect(firstResult).toHaveProperty('table_schema');
        expect(firstResult).toHaveProperty('table_name');
        expect(firstResult).toHaveProperty('is_valid');
        expect(firstResult).toHaveProperty('violation_count');
        expect(['UNIQUE', 'PRIMARY KEY']).toContain(firstResult.constraint_type);
      }
    });

    test('should validate specific table unique constraints', async () => {
      const result = await tools.validate_unique_constraints.execute({
        project_id: projectId,
        schema: 'test_constraints',
        table: 'parent_table',
        include_violation_details: false,
      });

      expect(result).toBeDefined();
      expect(result.schema).toBe('test_constraints');
      expect(result.table).toBe('parent_table');
      expect(result.results).toBeInstanceOf(Array);
    });
  });

  describe('validate_check_constraints', () => {
    test('should validate check constraints successfully', async () => {
      const result = await tools.validate_check_constraints.execute({
        project_id: projectId,
        schema: 'test_constraints',
        include_violation_details: false,
      });

      expect(result).toBeDefined();
      expect(result.schema).toBe('test_constraints');
      expect(result.total_constraints_checked).toBeGreaterThanOrEqual(0);
      expect(result.results).toBeInstanceOf(Array);
      
      // Check that results have the expected structure
      if (result.results.length > 0) {
        const firstResult = result.results[0];
        expect(firstResult).toHaveProperty('constraint_name');
        expect(firstResult).toHaveProperty('constraint_type');
        expect(firstResult).toHaveProperty('table_schema');
        expect(firstResult).toHaveProperty('table_name');
        expect(firstResult).toHaveProperty('is_valid');
        expect(firstResult).toHaveProperty('violation_count');
        expect(firstResult.constraint_type).toBe('CHECK');
      }
    });

    test('should validate specific table check constraints', async () => {
      const result = await tools.validate_check_constraints.execute({
        project_id: projectId,
        schema: 'test_constraints',
        table: 'parent_table',
        include_violation_details: false,
      });

      expect(result).toBeDefined();
      expect(result.schema).toBe('test_constraints');
      expect(result.table).toBe('parent_table');
      expect(result.results).toBeInstanceOf(Array);
    });
  });

  describe('validate_all_constraints', () => {
    test('should validate all constraint types successfully', async () => {
      const result = await tools.validate_all_constraints.execute({
        project_id: projectId,
        schema: 'test_constraints',
        constraint_types: ['foreign_key', 'unique', 'check'],
        include_violation_details: false,
      });

      expect(result).toBeDefined();
      expect(result.schema).toBe('test_constraints');
      expect(result.constraint_types_checked).toEqual(['foreign_key', 'unique', 'check']);
      expect(result.summary).toHaveProperty('total_constraints_checked');
      expect(result.summary).toHaveProperty('valid_constraints');
      expect(result.summary).toHaveProperty('invalid_constraints');
      expect(result.summary).toHaveProperty('total_violations');
      expect(result.foreign_key_results).toBeInstanceOf(Array);
      expect(result.unique_results).toBeInstanceOf(Array);
      expect(result.check_results).toBeInstanceOf(Array);
    });

    test('should validate specific constraint types only', async () => {
      const result = await tools.validate_all_constraints.execute({
        project_id: projectId,
        schema: 'test_constraints',
        constraint_types: ['unique'],
        include_violation_details: false,
      });

      expect(result).toBeDefined();
      expect(result.constraint_types_checked).toEqual(['unique']);
      expect(result.foreign_key_results).toHaveLength(0);
      expect(result.check_results).toHaveLength(0);
      expect(result.unique_results).toBeInstanceOf(Array);
    });
  });

  describe('parameter validation', () => {
    test('should reject invalid schema names', async () => {
      await expect(
        tools.validate_foreign_key_constraints.execute({
          project_id: projectId,
          schema: 'invalid-schema-name!',
        })
      ).rejects.toThrow();
    });

    test('should reject invalid table names', async () => {
      await expect(
        tools.validate_unique_constraints.execute({
          project_id: projectId,
          schema: 'test_constraints',
          table: 'invalid-table-name!',
        })
      ).rejects.toThrow();
    });

    test('should reject invalid constraint names', async () => {
      await expect(
        tools.validate_check_constraints.execute({
          project_id: projectId,
          schema: 'test_constraints',
          constraint_name: 'invalid-constraint-name!',
        })
      ).rejects.toThrow();
    });

    test('should reject invalid limit values', async () => {
      await expect(
        tools.validate_all_constraints.execute({
          project_id: projectId,
          schema: 'test_constraints',
          limit_violations: 0,
        })
      ).rejects.toThrow();

      await expect(
        tools.validate_all_constraints.execute({
          project_id: projectId,
          schema: 'test_constraints',
          limit_violations: 1001,
        })
      ).rejects.toThrow();
    });
  });

  describe('error handling', () => {
    test('should handle non-existent schema gracefully', async () => {
      const result = await tools.validate_foreign_key_constraints.execute({
        project_id: projectId,
        schema: 'non_existent_schema',
        include_violation_details: false,
      });

      expect(result).toBeDefined();
      expect(result.schema).toBe('non_existent_schema');
      expect(result.total_constraints_checked).toBe(0);
      expect(result.results).toHaveLength(0);
    });

    test('should handle non-existent table gracefully', async () => {
      const result = await tools.validate_unique_constraints.execute({
        project_id: projectId,
        schema: 'test_constraints',
        table: 'non_existent_table',
        include_violation_details: false,
      });

      expect(result).toBeDefined();
      expect(result.table).toBe('non_existent_table');
      expect(result.total_constraints_checked).toBe(0);
      expect(result.results).toHaveLength(0);
    });
  });
});
