# DDL Operations Support

This document describes the DDL (Data Definition Language) operation tools available in the Supabase MCP server.

## Overview

The DDL operations provide safe, validated, and logged database schema modifications with built-in security measures and operation tracking.

## Available Tools

### 1. `create_index`

Creates database indexes with safety checks and concurrency options.

**Parameters:**
- `table_name` (required): Name of the table to create index on
- `index_name` (required): Name for the new index
- `columns` (required): Array of column names to include in the index
- `unique` (optional): Whether to create a unique index (default: false)
- `concurrent` (optional): Whether to create index concurrently (default: true)
- `if_not_exists` (optional): Skip if index already exists (default: true)

**Example:**
```json
{
  "table_name": "users",
  "index_name": "idx_users_email",
  "columns": ["email"],
  "unique": true,
  "concurrent": true
}
```

### 2. `create_view`

Creates database views with SQL injection protection.

**Parameters:**
- `view_name` (required): Name for the new view
- `sql_query` (required): SQL SELECT query for the view
- `materialized` (optional): Whether to create a materialized view (default: false)
- `replace` (optional): Whether to use CREATE OR REPLACE (default: false)

**Example:**
```json
{
  "view_name": "active_users_view",
  "sql_query": "SELECT id, email, created_at FROM users WHERE status = 'active'",
  "materialized": false,
  "replace": true
}
```

### 3. `alter_table_add_column`

Safely adds columns to existing tables with type validation.

**Parameters:**
- `table_name` (required): Name of the table to modify
- `column_name` (required): Name of the new column
- `data_type` (required): PostgreSQL data type for the column
- `nullable` (optional): Whether column allows NULL values (default: true)
- `default_value` (optional): Default value for the column

**Example:**
```json
{
  "table_name": "users",
  "column_name": "phone_number",
  "data_type": "VARCHAR(20)",
  "nullable": true,
  "default_value": null
}
```

### 4. `list_ddl_history`

Retrieves the history of DDL operations performed through these tools.

**Parameters:**
- `limit` (optional): Maximum number of records to return (default: 100)
- `operation_type` (optional): Filter by operation type (e.g., 'CREATE_INDEX', 'CREATE_VIEW')

**Example:**
```json
{
  "limit": 50,
  "operation_type": "CREATE_INDEX"
}
```

### 5. `validate_ddl_permissions`

Checks if the current user has permissions to perform DDL operations.

**Parameters:**
- `operation_type` (required): Type of operation to check ('CREATE', 'ALTER', 'DROP')
- `object_name` (optional): Specific object name to check permissions for

**Example:**
```json
{
  "operation_type": "CREATE",
  "object_name": "users"
}
```

## Security Features

### Input Validation
- All parameters are validated using Zod schemas
- SQL injection protection for all user inputs
- Table and column name sanitization using identifier quoting

### Operation Logging
- All DDL operations are logged to a `ddl_operation_history` table
- Includes timestamp, operation type, user, and status
- Failed operations are also logged with error details

### Permission Checking
- Built-in permission validation before executing operations
- Checks for appropriate database privileges
- Prevents unauthorized schema modifications

## Error Handling

The DDL tools include comprehensive error handling:

- **Validation Errors**: Invalid parameters or unsafe SQL patterns
- **Permission Errors**: Insufficient database privileges
- **Database Errors**: Connection issues or constraint violations
- **Conflict Errors**: Attempting to create existing objects

## Limitations

### Current Limitations
- Limited to basic DDL operations (indexes, views, column additions)
- No support for complex table alterations or drops
- Materialized view refresh not automated
- Limited to PostgreSQL syntax

### Safety Restrictions
- No DROP operations to prevent accidental data loss
- Limited ALTER TABLE support to reduce risk
- SQL injection protection may be overly restrictive for complex queries

## Best Practices

### Planning
1. Always validate permissions before performing operations
2. Use concurrent index creation for large tables
3. Test DDL operations in development environment first

### Monitoring
1. Regularly check DDL history for unauthorized changes
2. Monitor index usage after creation
3. Review materialized view refresh schedules

### Security
1. Use least-privilege database users
2. Validate all inputs before execution
3. Monitor DDL operation logs for suspicious activity

## Integration

The DDL tools are automatically available when the Supabase MCP server is running. They integrate with:

- Existing database connection management
- Authentication and authorization systems
- Logging and monitoring infrastructure
- Error reporting mechanisms

## Troubleshooting

### Common Issues

**Permission Denied**
- Check database user privileges
- Verify connection string includes appropriate role
- Use `validate_ddl_permissions` to diagnose

**Index Creation Fails**
- Check for existing indexes with same name
- Verify table and column names are correct
- Consider using `if_not_exists` option

**View Creation Errors**
- Validate SQL syntax in development
- Check for referenced tables/columns existence
- Ensure proper SELECT permissions

### Getting Help

1. Check DDL operation history for previous similar operations
2. Validate permissions using the validation tool
3. Review PostgreSQL logs for detailed error messages
4. Test operations manually in development environment 