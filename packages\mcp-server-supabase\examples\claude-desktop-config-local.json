{"$schema": "https://modelcontextprotocol.io/schemas/claude-desktop-config.json", "mcpServers": {"supabase-local": {"command": "node", "args": ["{{ABSOLUTE_PATH_TO_PROJECT}}/packages/mcp-server-supabase/dist/transports/stdio.cjs"], "env": {"SUPABASE_URL": "{{YOUR_SUPABASE_URL}}", "SUPABASE_ANON_KEY": "{{YOUR_<PERSON><PERSON>_KEY}}", "SUPABASE_SERVICE_ROLE_KEY": "{{YOUR_SERVICE_KEY}}", "READ_ONLY": "false"}}}}