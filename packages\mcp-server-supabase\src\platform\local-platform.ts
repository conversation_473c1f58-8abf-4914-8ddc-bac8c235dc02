import { createClient, type SupabaseClient } from '@supabase/supabase-js';
import type { InitData } from '@supabase/mcp-utils';
import type { LocalConfig } from '../config/local-config.js';
import {
  type ApplyMigrationOptions,
  type CreateBranchOptions,
  type CreateProjectOptions,
  type DeployEdgeFunctionOptions,
  type EdgeFunction,
  type ExecuteSqlOptions,
  type GetLogsOptions,
  type Migration,
  type ResetBranchOptions,
  type SupabasePlatform,
  type GenerateTypescriptTypesResult,
} from './types.js';

export type LocalSupabasePlatformOptions = LocalConfig;

/**
 * Creates a Supabase platform implementation for local self-hosted instances.
 */
export function createLocalSupabasePlatform(
  options: LocalSupabasePlatformOptions
): SupabasePlatform {
  const { SUPABASE_URL, SUPABASE_ANON_KEY, SUPABASE_SERVICE_ROLE_KEY, DEBUG_SQL = true } = options;

  // Regular client with anon key for read operations
  const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
  
  // Admin client with service role key for privileged operations
  const adminSupabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  });

  const platform: SupabasePlatform = {
    async init(info: InitData) {
      // For local instances, we don't need complex initialization
      // Just verify we can connect to the instance
      try {
        // Try a simple connection test that doesn't require special permissions
        const { data, error } = await supabase
          .from('information_schema.tables')
          .select('count')
          .limit(1);
        
        if (DEBUG_SQL) {
          console.log('Supabase connection test:', { data, error });
        }
        
        // Don't fail initialization if we can't connect - just warn
        if (error) {
          console.warn(`Warning: Could not verify Supabase connection: ${error.message}`);
          console.warn('MCP server will still start, but some operations may fail');
        } else {
          if (DEBUG_SQL) {
            console.log('Successfully connected to local Supabase instance');
          }
        }
      } catch (error) {
        // Don't fail initialization - just log the warning
        console.warn(`Warning: Failed to test Supabase connection: ${error}`);
        console.warn('MCP server will still start, but some operations may fail');
      }
    },

    async executeSql<T>(projectId: string, options: ExecuteSqlOptions): Promise<T[]> {
      const { query, read_only } = options;
      
      if (DEBUG_SQL) {
        console.log('Executing SQL:', query);
      }

      // For now, always use admin client as authentication seems to be failing with anon
      // TODO: Investigate why anon role fails with exec_sql
      const client = adminSupabase;
      
      if (DEBUG_SQL) {
        console.log('Using client: service_role (forced)');
        console.log('Query:', query);
      }
      
      // Check if this is a DDL operation
      const isDDLOperation = /^\s*(CREATE|ALTER|DROP|GRANT|REVOKE)\s+/i.test(query.trim());
      
      try {
        // For direct SQL execution, we need to use the RPC function
        const { data, error } = await client.rpc('exec_sql', { query });
        
        if (DEBUG_SQL) {
          console.log('exec_sql response data:', JSON.stringify(data, null, 2));
          console.log('exec_sql response error:', JSON.stringify(error, null, 2));
        }
        
        if (error) {
          // If exec_sql function doesn't exist, provide a helpful error message
          if (error.code === 'PGRST202' || error.message.includes('function exec_sql does not exist')) {
            throw new Error(
              `The exec_sql function is not available in your Supabase instance. ` +
              `This function is required for DDL operations like CREATE SCHEMA. ` +
              `Please run the migration to create it: ` +
              `supabase migration run 20250613000000_create_exec_sql_function.sql`
            );
          }
          
          // If it's a PostgREST restriction error
          if (error.message.includes('Only SELECT, INSERT, UPDATE, DELETE, WITH, and TRUNCATE statements are allowed')) {
            throw new Error(
              `PostgREST API restriction detected. DDL operations like CREATE SCHEMA must use the exec_sql function. ` +
              `Error: ${error.message}. ` +
              `Ensure the exec_sql function is properly deployed and accessible.`
            );
          }
          
          throw new Error(`SQL execution failed: ${error.message}`);
        }
        
        // The exec_sql function returns data in different formats depending on query type
        // Handle the response based on the actual structure returned
        
        // If data is null or undefined, return empty array
        if (!data) {
          return [] as T[];
        }
        
        // The exec_sql function returns a JSON value directly
        // For SELECT queries, it returns JSON array
        // For DDL/DML queries, it returns objects with status/affected_rows
        
        // If it's already an array (SELECT result), return it
        if (Array.isArray(data)) {
          return data as T[];
        }
        
        // If it's an object, check the type
        if (typeof data === 'object') {
          // Handle DDL operation response (status/message object)
          if ('status' in data && data.status === 'success') {
            return [] as T[]; // DDL operations don't return rows
          }
          // Handle DML operation response (affected_rows)
          if ('affected_rows' in data) {
            return [] as T[]; // DML operations don't return rows for our purposes
          }
          
          // If it's a single object that should be in an array, wrap it
          return [data] as T[];
          }
        
        // Fallback: empty array for unexpected responses
        return [] as T[];
      } catch (error) {
        // For DDL operations that fail, don't try fallback - they need exec_sql
        if (isDDLOperation) {
          if (error instanceof Error) {
            throw error;
          }
          throw new Error(`DDL operation failed: ${String(error)}`);
        }
        
        // Fallback: try using the REST API for simple SELECT queries only
        if (query.toLowerCase().trim().startsWith('select')) {
          // Parse table name from simple SELECT queries
          const tableMatch = query.match(/from\s+([a-zA-Z_][a-zA-Z0-9_]*)/i);
          if (tableMatch && tableMatch[1]) {
            const tableName = tableMatch[1];
            try {
              const { data, error } = await client.from(tableName).select('*');
              if (error) {
                throw new Error(`Query failed: ${error.message}`);
              }
              return data as T[];
            } catch (fallbackError) {
              throw new Error(`Both exec_sql and REST API failed. exec_sql error: ${String(error)}. REST API error: ${String(fallbackError)}`);
            }
          }
        }
        
        throw new Error(`SQL execution failed: ${String(error)}`);
      }
    },

    async listMigrations(projectId: string): Promise<Migration[]> {
      // For local instances, we can check the migrations table if it exists
      try {
        const { data, error } = await adminSupabase
          .from('supabase_migrations.schema_migrations')
          .select('version, name')
          .order('version', { ascending: true });
        
        if (error) {
          // If migrations table doesn't exist, return empty array
          if (error.code === 'PGRST116') {
            return [];
          }
          throw error;
        }
        
        return data || [];
      } catch (error) {
        // Return empty array if we can't access migrations
        console.warn('Could not list migrations:', error);
        return [];
      }
    },

    async applyMigration<T>(projectId: string, options: ApplyMigrationOptions): Promise<T[]> {
      const { name, query } = options;
      
      if (DEBUG_SQL) {
        console.log('Applying migration:', name);
      }

      // Execute the migration query
      const result = await platform.executeSql<T>(projectId, { query, read_only: false });
      
      // Record the migration in the migrations table
      try {
        await adminSupabase
          .from('supabase_migrations.schema_migrations')
          .insert({ version: Date.now().toString(), name });
      } catch (error) {
        console.warn('Could not record migration:', error);
      }
      
      return result;
    },

    // Local instances don't have organizations - these throw errors
    async listOrganizations() {
      throw new Error('Organization management is not available for local Supabase instances');
    },

    async getOrganization(organizationId: string) {
      throw new Error('Organization management is not available for local Supabase instances');
    },

    // Local instances don't have project management - these throw errors  
    async listProjects() {
      throw new Error('Project management is not available for local Supabase instances. You are connected to a single local instance.');
    },

    async getProject(projectId: string) {
      // Return mock project data for the local instance
      return {
        id: 'local',
        organization_id: 'local',
        name: 'Local Supabase Instance',
        status: 'ACTIVE_HEALTHY',
        created_at: new Date().toISOString(),
        region: 'devdb.syncrobit.net',
      };
    },

    async createProject(options: CreateProjectOptions) {
      throw new Error('Project creation is not available for local Supabase instances');
    },

    async pauseProject(projectId: string) {
      throw new Error('Project management is not available for local Supabase instances');
    },

    async restoreProject(projectId: string) {
      throw new Error('Project management is not available for local Supabase instances');
    },

    // Edge functions - simplified for local development
    async listEdgeFunctions(projectId: string): Promise<EdgeFunction[]> {
      try {
        // Try to list functions via the functions endpoint
        const response = await fetch(`${SUPABASE_URL}/functions/v1/`, {
          headers: {
            'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
            'apikey': SUPABASE_ANON_KEY,
          },
        });
        
        if (!response.ok) {
          return [];
        }
        
        const functions = await response.json();
        return Array.isArray(functions) ? functions : [];
      } catch (error) {
        console.warn('Could not list edge functions:', error);
        return [];
      }
    },

    async getEdgeFunction(projectId: string, functionSlug: string): Promise<EdgeFunction> {
      const functions = await platform.listEdgeFunctions(projectId);
      const func = functions.find(f => f.slug === functionSlug);
      
      if (!func) {
        throw new Error(`Edge function '${functionSlug}' not found`);
      }
      
      return func;
    },

    async deployEdgeFunction(projectId: string, options: DeployEdgeFunctionOptions) {
      throw new Error(
        'Edge function deployment for local instances should be done using the Supabase CLI:\n' +
        `supabase functions deploy ${options.name}`
      );
    },

    // Debugging - not available for local instances
    async getLogs(projectId: string, options: GetLogsOptions) {
      throw new Error(
        'Cloud-style logs are not available for local instances. Use Docker logs instead:\n' +
        'docker compose logs supabase-db\n' +
        'docker compose logs supabase-auth\n' +
        'docker compose logs supabase-rest'
      );
    },

    async getSecurityAdvisors(projectId: string) {
      throw new Error('Security advisors are not available for local Supabase instances');
    },

    async getPerformanceAdvisors(projectId: string) {
      throw new Error('Performance advisors are not available for local Supabase instances');
    },

    // Development helpers
    async getProjectUrl(projectId: string): Promise<string> {
      return SUPABASE_URL;
    },

    async getAnonKey(projectId: string): Promise<string> {
      return SUPABASE_ANON_KEY;
    },

    async generateTypescriptTypes(projectId: string): Promise<GenerateTypescriptTypesResult> {
      return {
        types: `
// To generate TypeScript types for your local Supabase instance, run:
// npx supabase gen types typescript --local > types/database.ts

// Or if you have the CLI installed globally:
// supabase gen types typescript --local > types/database.ts

// This will generate types based on your current database schema.
`.trim(),
      };
    },

    // Branching is not available for local instances
    async listBranches(projectId: string) {
      throw new Error('Branching is not available for local Supabase instances');
    },

    async createBranch(projectId: string, options: CreateBranchOptions) {
      throw new Error('Branching is not available for local Supabase instances');
    },

    async deleteBranch(branchId: string) {
      throw new Error('Branching is not available for local Supabase instances');
    },

    async mergeBranch(branchId: string) {
      throw new Error('Branching is not available for local Supabase instances');
    },

    async resetBranch(branchId: string, options: ResetBranchOptions) {
      throw new Error('Branching is not available for local Supabase instances');
    },

    async rebaseBranch(branchId: string) {
      throw new Error('Branching is not available for local Supabase instances');
    },
  };

  return platform;
}