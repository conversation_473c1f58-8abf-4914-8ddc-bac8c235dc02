/**
 * Advanced Connection Pool Manager for Database and External Services
 * 
 * Provides intelligent connection pooling with health monitoring,
 * automatic retry, and load balancing capabilities.
 */

import { EventEmitter } from 'events';
import { contextLogger } from '../utils/logger.js';
import type { ConfigManager } from '../config/config-manager.js';

export interface ConnectionConfig {
  host: string;
  port: number;
  database?: string;
  username?: string;
  password?: string;
  ssl?: boolean;
  timeout?: number;
  maxRetries?: number;
  retryDelay?: number;
}

export interface PoolOptions {
  minConnections?: number;
  maxConnections?: number;
  acquireTimeout?: number;
  idleTimeout?: number;
  connectionTimeout?: number;
  healthCheckInterval?: number;
  maxRetries?: number;
  retryDelay?: number;
  enableHealthCheck?: boolean;
  enableMetrics?: boolean;
}

export interface Connection {
  id: string;
  config: ConnectionConfig;
  client: any; // Database client or HTTP client
  isHealthy: boolean;
  isIdle: boolean;
  createdAt: Date;
  lastUsed: Date;
  useCount: number;
  errors: number;
}

export interface PoolStats {
  totalConnections: number;
  idleConnections: number;
  activeConnections: number;
  waitingRequests: number;
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageWaitTime: number;
  averageConnectionTime: number;
  healthyConnections: number;
  unhealthyConnections: number;
}

export interface ConnectionRequest {
  id: string;
  timestamp: Date;
  timeout: NodeJS.Timeout;
  resolve: (connection: Connection) => void;
  reject: (error: Error) => void;
}

/**
 * Advanced connection pool with health monitoring and load balancing
 */
export class ConnectionPool extends EventEmitter {
  private readonly logger = contextLogger.child({ component: 'ConnectionPool' });
  private readonly config?: ConfigManager;
  private readonly options: Required<PoolOptions>;
  private readonly connectionConfig: ConnectionConfig;
  
  private connections = new Map<string, Connection>();
  private idleConnections = new Set<string>();
  private waitingRequests: ConnectionRequest[] = [];
  private healthCheckTimer?: NodeJS.Timeout;
  
  private stats: PoolStats = {
    totalConnections: 0,
    idleConnections: 0,
    activeConnections: 0,
    waitingRequests: 0,
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageWaitTime: 0,
    averageConnectionTime: 0,
    healthyConnections: 0,
    unhealthyConnections: 0,
  };
  
  private totalWaitTime = 0;
  private totalConnectionTime = 0;
  private isShuttingDown = false;

  constructor(
    connectionConfig: ConnectionConfig,
    options: PoolOptions = {},
    config?: ConfigManager
  ) {
    super();
    
    this.connectionConfig = connectionConfig;
    this.config = config;
    this.options = {
      minConnections: options.minConnections ?? config?.get('POOL_MIN_CONNECTIONS', 2) ?? 2,
      maxConnections: options.maxConnections ?? config?.get('POOL_MAX_CONNECTIONS', 10) ?? 10,
      acquireTimeout: options.acquireTimeout ?? config?.get('POOL_ACQUIRE_TIMEOUT', 30000) ?? 30000,
      idleTimeout: options.idleTimeout ?? config?.get('POOL_IDLE_TIMEOUT', 300000) ?? 300000, // 5 minutes
      connectionTimeout: options.connectionTimeout ?? config?.get('POOL_CONNECTION_TIMEOUT', 10000) ?? 10000,
      healthCheckInterval: options.healthCheckInterval ?? config?.get('POOL_HEALTH_CHECK_INTERVAL', 60000) ?? 60000,
      maxRetries: options.maxRetries ?? config?.get('POOL_MAX_RETRIES', 3) ?? 3,
      retryDelay: options.retryDelay ?? config?.get('POOL_RETRY_DELAY', 1000) ?? 1000,
      enableHealthCheck: options.enableHealthCheck ?? config?.get('POOL_ENABLE_HEALTH_CHECK', true) ?? true,
      enableMetrics: options.enableMetrics ?? config?.get('POOL_ENABLE_METRICS', true) ?? true,
    };

    this.logger.info('Connection pool initialized', {
      host: connectionConfig.host,
      port: connectionConfig.port,
      minConnections: this.options.minConnections,
      maxConnections: this.options.maxConnections,
    });
  }

  /**
   * Initialize the connection pool
   */
  async initialize(): Promise<void> {
    try {
      // Create minimum connections
      for (let i = 0; i < this.options.minConnections; i++) {
        await this.createConnection();
      }

      // Start health check timer
      if (this.options.enableHealthCheck) {
        this.startHealthCheck();
      }

      this.logger.info('Connection pool initialized successfully', {
        connections: this.connections.size,
      });

      this.emit('initialized');
    } catch (error) {
      this.logger.error('Failed to initialize connection pool', error instanceof Error ? error : undefined);
      throw error;
    }
  }

  /**
   * Acquire a connection from the pool
   */
  async acquire(): Promise<Connection> {
    if (this.isShuttingDown) {
      throw new Error('Connection pool is shutting down');
    }

    const startTime = Date.now();
    this.stats.totalRequests++;

    try {
      // Try to get an idle connection first
      const idleConnectionId = this.idleConnections.values().next().value;
      if (idleConnectionId) {
        const connection = this.connections.get(idleConnectionId);
        if (connection && connection.isHealthy) {
          this.idleConnections.delete(idleConnectionId);
          connection.isIdle = false;
          connection.lastUsed = new Date();
          connection.useCount++;
          
          this.updateStats();
          this.stats.successfulRequests++;
          
          this.logger.debug('Connection acquired from idle pool', { connectionId: connection.id });
          return connection;
        }
      }

      // Create new connection if under limit
      if (this.connections.size < this.options.maxConnections) {
        const connection = await this.createConnection();
        connection.isIdle = false;
        connection.lastUsed = new Date();
        connection.useCount++;
        
        this.updateStats();
        this.stats.successfulRequests++;
        
        this.logger.debug('New connection created and acquired', { connectionId: connection.id });
        return connection;
      }

      // Wait for available connection
      return await this.waitForConnection();
    } catch (error) {
      this.stats.failedRequests++;
      this.logger.error('Failed to acquire connection', error instanceof Error ? error : undefined);
      throw error;
    } finally {
      if (this.options.enableMetrics) {
        const waitTime = Date.now() - startTime;
        this.totalWaitTime += waitTime;
        this.stats.averageWaitTime = this.totalWaitTime / this.stats.totalRequests;
      }
    }
  }

  /**
   * Release a connection back to the pool
   */
  async release(connection: Connection): Promise<void> {
    try {
      if (!this.connections.has(connection.id)) {
        this.logger.warn('Attempting to release unknown connection', { connectionId: connection.id });
        return;
      }

      // Check if there are waiting requests
      if (this.waitingRequests.length > 0) {
        const request = this.waitingRequests.shift()!;
        clearTimeout(request.timeout);
        
        connection.lastUsed = new Date();
        connection.useCount++;
        
        this.logger.debug('Connection passed to waiting request', { 
          connectionId: connection.id,
          requestId: request.id,
        });
        
        request.resolve(connection);
        return;
      }

      // Return to idle pool
      connection.isIdle = true;
      this.idleConnections.add(connection.id);
      
      this.updateStats();
      
      this.logger.debug('Connection released to idle pool', { connectionId: connection.id });
      this.emit('connectionReleased', connection);
    } catch (error) {
      this.logger.error('Failed to release connection', error instanceof Error ? error : undefined, {
        connectionId: connection.id,
      });
    }
  }

  /**
   * Remove and destroy a connection
   */
  async destroyConnection(connectionId: string): Promise<void> {
    const connection = this.connections.get(connectionId);
    if (!connection) {
      return;
    }

    try {
      // Close the actual connection
      if (connection.client && typeof connection.client.end === 'function') {
        await connection.client.end();
      } else if (connection.client && typeof connection.client.close === 'function') {
        await connection.client.close();
      }

      this.connections.delete(connectionId);
      this.idleConnections.delete(connectionId);
      
      this.updateStats();
      
      this.logger.debug('Connection destroyed', { connectionId });
      this.emit('connectionDestroyed', connection);
    } catch (error) {
      this.logger.error('Failed to destroy connection', error instanceof Error ? error : undefined, {
        connectionId,
      });
    }
  }

  /**
   * Get pool statistics
   */
  getStats(): PoolStats {
    this.updateStats();
    return { ...this.stats };
  }

  /**
   * Get all connections (for debugging)
   */
  getConnections(): Connection[] {
    return Array.from(this.connections.values());
  }

  /**
   * Shutdown the connection pool
   */
  async shutdown(): Promise<void> {
    this.isShuttingDown = true;
    
    // Stop health check
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = undefined;
    }

    // Reject all waiting requests
    for (const request of this.waitingRequests) {
      clearTimeout(request.timeout);
      request.reject(new Error('Connection pool shutting down'));
    }
    this.waitingRequests.length = 0;

    // Close all connections
    const connectionIds = Array.from(this.connections.keys());
    await Promise.all(connectionIds.map(id => this.destroyConnection(id)));

    this.removeAllListeners();
    
    this.logger.info('Connection pool shutdown complete');
    this.emit('shutdown');
  }

  /**
   * Create a new connection
   */
  private async createConnection(): Promise<Connection> {
    const startTime = Date.now();
    const connectionId = this.generateConnectionId();

    try {
      // This is a placeholder - in a real implementation, you'd create actual database connections
      // For example, with pg: const client = new pg.Client(this.connectionConfig);
      const client = await this.createClient();

      const connection: Connection = {
        id: connectionId,
        config: this.connectionConfig,
        client,
        isHealthy: true,
        isIdle: true,
        createdAt: new Date(),
        lastUsed: new Date(),
        useCount: 0,
        errors: 0,
      };

      this.connections.set(connectionId, connection);
      this.idleConnections.add(connectionId);

      if (this.options.enableMetrics) {
        const connectionTime = Date.now() - startTime;
        this.totalConnectionTime += connectionTime;
        this.stats.averageConnectionTime = this.totalConnectionTime / this.connections.size;
      }

      this.logger.debug('Connection created', { connectionId, host: this.connectionConfig.host });
      this.emit('connectionCreated', connection);

      return connection;
    } catch (error) {
      this.logger.error('Failed to create connection', error instanceof Error ? error : undefined, {
        connectionId,
        host: this.connectionConfig.host,
      });
      throw error;
    }
  }

  /**
   * Create actual client connection (placeholder)
   */
  private async createClient(): Promise<any> {
    // This is a placeholder implementation
    // In a real scenario, you'd create actual database clients here
    return {
      id: this.generateConnectionId(),
      connected: true,
      end: async () => { /* close connection */ },
      query: async (sql: string) => { /* execute query */ },
    };
  }

  /**
   * Wait for an available connection
   */
  private async waitForConnection(): Promise<Connection> {
    return new Promise((resolve, reject) => {
      const requestId = this.generateRequestId();
      const timeout = setTimeout(() => {
        const index = this.waitingRequests.findIndex(req => req.id === requestId);
        if (index !== -1) {
          this.waitingRequests.splice(index, 1);
        }
        reject(new Error(`Connection acquire timeout after ${this.options.acquireTimeout}ms`));
      }, this.options.acquireTimeout);

      const request: ConnectionRequest = {
        id: requestId,
        timestamp: new Date(),
        timeout,
        resolve,
        reject,
      };

      this.waitingRequests.push(request);
      this.updateStats();
    });
  }

  /**
   * Start health check timer
   */
  private startHealthCheck(): void {
    this.healthCheckTimer = setInterval(() => {
      this.performHealthCheck();
    }, this.options.healthCheckInterval);
  }

  /**
   * Perform health check on all connections
   */
  private async performHealthCheck(): Promise<void> {
    const healthCheckPromises = Array.from(this.connections.values()).map(async (connection) => {
      try {
        // Placeholder health check - in real implementation, you'd ping the database
        const isHealthy = await this.checkConnectionHealth(connection);
        
        if (!isHealthy && connection.isHealthy) {
          connection.isHealthy = false;
          connection.errors++;
          this.logger.warn('Connection marked as unhealthy', { connectionId: connection.id });
          this.emit('connectionUnhealthy', connection);
        } else if (isHealthy && !connection.isHealthy) {
          connection.isHealthy = true;
          this.logger.info('Connection recovered', { connectionId: connection.id });
          this.emit('connectionRecovered', connection);
        }
      } catch (error) {
        connection.isHealthy = false;
        connection.errors++;
        this.logger.error('Health check failed', error instanceof Error ? error : undefined, {
          connectionId: connection.id,
        });
      }
    });

    await Promise.allSettled(healthCheckPromises);
    this.updateStats();
  }

  /**
   * Check individual connection health (placeholder)
   */
  private async checkConnectionHealth(connection: Connection): Promise<boolean> {
    // Placeholder implementation
    // In real scenario: return await connection.client.query('SELECT 1');
    return connection.client && connection.client.connected;
  }

  /**
   * Update pool statistics
   */
  private updateStats(): void {
    this.stats.totalConnections = this.connections.size;
    this.stats.idleConnections = this.idleConnections.size;
    this.stats.activeConnections = this.connections.size - this.idleConnections.size;
    this.stats.waitingRequests = this.waitingRequests.length;
    
    let healthyCount = 0;
    for (const connection of this.connections.values()) {
      if (connection.isHealthy) {
        healthyCount++;
      }
    }
    
    this.stats.healthyConnections = healthyCount;
    this.stats.unhealthyConnections = this.connections.size - healthyCount;
  }

  /**
   * Generate unique connection ID
   */
  private generateConnectionId(): string {
    return `conn_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Generate unique request ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }
}
