services:
  mcp-server-supabase:
    build:
      context: ../..
      dockerfile: packages/mcp-server-supabase/Dockerfile
    image: supabase/mcp-server-supabase
    container_name: mcp-server-supabase
    restart: unless-stopped
    stdin_open: true
    tty: true
    environment:
      - SUPABASE_URL=http://192.168.1.218:54321
      - SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogImFub24iLAogICJpc3MiOiAic3VwYWJhc2UiLAogICJpYXQiOiAxNzQxMDk1MDAwLAogICJleHAiOiAxODk4ODYxNDAwCn0.J1N7sT41EI4ORiRFvPm3FPDArs43dzEq9g9-iq7d3pY
      - SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogInNlcnZpY2Vfcm9sZSIsCiAgImlzcyI6ICJzdXBhYmFzZSIsCiAgImlhdCI6IDE3NDEwOTUwMDAsCiAgImV4cCI6IDE4OTg4NjE0MDAKfQ.4OLZbhrbccD5vfy14HcWgYDgJsSzUXimxgcGA3OgaV8
      # - DEBUG=true # Uncomment for debug logging
    # If you need to expose a port for debugging or other transports:
    # ports:
    #   - "8080:8080"