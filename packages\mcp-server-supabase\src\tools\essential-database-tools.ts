import { z } from 'zod';
import { listExtensionsSql, listTablesSql } from '../pg-meta/index.js';
import {
  postgresExtensionSchema,
  postgresTableSchema,
} from '../pg-meta/types.js';
import type { SupabasePlatform } from '../platform/types.js';
import { injectableTool } from './util.js';
import {
  schemaNameSchema,
  postgresIdentifierSchema,
} from './crud-validation.js';

export type DatabaseToolsOptions = {
  platform: SupabasePlatform;
  projectId?: string;
  readOnly?: boolean;
};

export function getEssentialDatabaseTools({
  platform,
  projectId,
  readOnly,
}: DatabaseToolsOptions) {
  const project_id = projectId;

  return {
    list_tables: injectableTool({
      description: 'Lists all tables in one or more schemas.',
      parameters: z.object({
        project_id: z.string(),
        schemas: z
          .array(schemaNameSchema)
          .min(1, 'Must specify at least one schema')
          .max(50, 'Cannot query more than 50 schemas at once')
          .default(['public'])
          .describe('List of schema names to include. Defaults to public schema.'),
      }),
      inject: { project_id },
      execute: async ({ project_id, schemas }) => {
        try {
          const schemaFilter = schemas.map(s => `'${s}'`).join(',');
          const query = `
            SELECT 
              c.oid::bigint as id,
              nc.nspname as schema,
              c.relname as name,
              COALESCE(c.relrowsecurity, false) as rls_enabled,
              COALESCE(c.relforcerowsecurity, false) as rls_forced,
              CASE COALESCE(c.relreplident, 'd')
                WHEN 'd' THEN 'DEFAULT'
                WHEN 'n' THEN 'NOTHING' 
                WHEN 'f' THEN 'FULL'
                WHEN 'i' THEN 'INDEX'
                ELSE 'DEFAULT'
              END as replica_identity,
              COALESCE(pg_total_relation_size(c.oid), 0) as bytes,
              COALESCE(pg_size_pretty(pg_total_relation_size(c.oid)), '0 bytes') as size,
              COALESCE(pg_stat_get_live_tuples(c.oid), 0) as live_rows_estimate,
              COALESCE(pg_stat_get_dead_tuples(c.oid), 0) as dead_rows_estimate,
              COALESCE(obj_description(c.oid), '') as comment,
              '[]'::json as primary_keys,
              '[]'::json as relationships
            FROM pg_class c
            JOIN pg_namespace nc ON c.relnamespace = nc.oid
            WHERE c.relkind IN ('r', 'p')
              AND nc.nspname IN (${schemaFilter})
            ORDER BY nc.nspname, c.relname
          `;

          const data = await platform.executeSql(project_id, {
            query,
            read_only: readOnly,
          });

          if (!Array.isArray(data)) {
            throw new Error(`Expected array from executeSql, got ${typeof data}: ${JSON.stringify(data)}`);
          }

          return data.map((table: any) => ({
            id: Number(table.id) || 0,
            schema: String(table.schema) || 'public',
            name: String(table.name) || 'unknown_table',
            rls_enabled: Boolean(table.rls_enabled),
            rls_forced: Boolean(table.rls_forced),
            replica_identity: table.replica_identity || 'DEFAULT',
            bytes: Number(table.bytes) || 0,
            size: String(table.size) || '0 bytes',
            live_rows_estimate: Number(table.live_rows_estimate) || 0,
            dead_rows_estimate: Number(table.dead_rows_estimate) || 0,
            comment: String(table.comment || ''),
            primary_keys: [],
            relationships: []
          }));
        } catch (error) {
          throw new Error(`Failed to list tables: ${error instanceof Error ? error.message : String(error)}`);
        }
      },
    }),

    list_extensions: injectableTool({
      description: 'Lists all extensions in the database.',
      parameters: z.object({
        project_id: z.string(),
      }),
      inject: { project_id },
      execute: async ({ project_id }) => {
        const query = listExtensionsSql();
        const data = await platform.executeSql(project_id, {
          query,
          read_only: readOnly,
        });
        
        if (!Array.isArray(data)) {
          throw new Error(`Expected array from executeSql, got ${typeof data}: ${JSON.stringify(data)}`);
        }
        
        return data.map((extension) => postgresExtensionSchema.parse(extension));
      },
    }),

    list_migrations: injectableTool({
      description: 'Lists all migrations in the database.',
      parameters: z.object({
        project_id: z.string(),
      }),
      inject: { project_id },
      execute: async ({ project_id }) => {
        return await platform.listMigrations(project_id);
      },
    }),

    apply_migration: injectableTool({
      description: 'Applies a migration to the database. Use this when executing DDL operations. Do not hardcode references to generated IDs in data migrations.',
      parameters: z.object({
        project_id: z.string(),
        name: z
          .string()
          .min(1, 'Migration name cannot be empty')
          .max(100, 'Migration name cannot exceed 100 characters')
          .regex(/^[a-z0-9_]+$/, 'Migration name must be snake_case')
          .describe('The name of the migration in snake_case'),
        query: z
          .string()
          .min(1, 'SQL query cannot be empty')
          .max(100000, 'SQL query cannot exceed 100,000 characters')
          .describe('The SQL DDL query to apply'),
      }),
      inject: { project_id },
      execute: async ({ project_id, name, query }) => {
        if (readOnly) {
          throw new Error('Cannot apply migrations in read-only mode');
        }
        return await platform.applyMigration(project_id, { name, query });
      },
    }),

    execute_sql: injectableTool({
      description: 'Executes raw SQL in the Postgres database. Use `apply_migration` instead for DDL operations.',
      parameters: z.object({
        project_id: z.string(),
        query: z
          .string()
          .min(1, 'SQL query cannot be empty')
          .max(50000, 'SQL query cannot exceed 50,000 characters')
          .describe('The SQL query to execute'),
      }),
      inject: { project_id },
      execute: async ({ project_id, query }) => {
        return await platform.executeSql(project_id, {
          query,
          read_only: readOnly,
        });
      },
    }),
  };
} 