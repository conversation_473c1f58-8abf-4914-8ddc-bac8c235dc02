import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { describe, expect, test, beforeAll, afterAll } from 'vitest';
import { 
  Tool,
  <PERSON>rrorCode,
  McpError,
  LoggingMessageNotificationSchema
} from '@modelcontextprotocol/sdk/types.js';
import { MCP_CLIENT_NAME, MCP_CLIENT_VERSION } from './mocks.js';
import { testConfig } from './test-config.js';

/**
 * MCP Tool Validation and Schema Compliance Tests
 * 
 * This test suite validates:
 * - Tool schema compliance with JSON Schema standards
 * - Input parameter validation
 * - Output format consistency
 * - Tool metadata accuracy
 * - Schema evolution compatibility
 */

type MCPTestClient = {
  client: Client;
  transport: StdioClientTransport;
};

// Simplified validation without external dependencies

async function setupMCPClient(): Promise<MCPTestClient> {
  const client = new Client(
    {
      name: MCP_CLIENT_NAME,
      version: MCP_CLIENT_VERSION,
    },
    {
      capabilities: {
        tools: {},
        prompts: {},
        resources: {},
        logging: {}
      },
    }
  );

  client.setNotificationHandler(LoggingMessageNotificationSchema, (message) => {
    const { level, data } = message.params;
    if (level === 'error') {
      console.error('MCP Server Error:', data);
    }
  });

  const transport = new StdioClientTransport({
    command: 'npx',
    args: [
      '@supabase/mcp-server-supabase',
      '--supabase-url', process.env.SUPABASE_URL || '',
      '--anon-key', process.env.SUPABASE_ANON_KEY || '',
      '--service-key', process.env.SUPABASE_SERVICE_ROLE_KEY || ''
    ],
  });

  await client.connect(transport);
  return { client, transport };
}

async function cleanupMCPClient(mcpClient: MCPTestClient): Promise<void> {
  try {
    await mcpClient.client.close();
    await mcpClient.transport.close();
  } catch (error) {
    console.warn('Error during MCP client cleanup:', error);
  }
}

/**
 * Validate that a JSON Schema is well-formed
 */
function validateJsonSchema(schema: any): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  try {
    // Basic schema structure validation
    if (!schema || typeof schema !== 'object') {
      errors.push('Schema must be an object');
      return { valid: false, errors };
    }
    
    if (!schema.type) {
      errors.push('Schema must have a type property');
    }
    
    if (schema.type === 'object') {
      if (!schema.properties) {
        errors.push('Object schema must have properties');
      } else if (typeof schema.properties !== 'object') {
        errors.push('Properties must be an object');
      }
      
      if (schema.required && !Array.isArray(schema.required)) {
        errors.push('Required must be an array');
      }
    }
    
    // Try to compile the schema with AJV
    try {
      ajv.compile(schema);
    } catch (ajvError) {
      errors.push(`AJV compilation error: ${ajvError.message}`);
    }
    
    return { valid: errors.length === 0, errors };
  } catch (error) {
    errors.push(`Schema validation error: ${error.message}`);
    return { valid: false, errors };
  }
}

/**
 * Generate test data for a given JSON schema
 */
function generateTestData(schema: any): any[] {
  const testCases: any[] = [];
  
  if (schema.type === 'object') {
    // Valid case with all required properties
    const validCase: any = {};
    
    if (schema.properties) {
      Object.entries(schema.properties).forEach(([key, propSchema]: [string, any]) => {
        if (propSchema.type === 'string') {
          validCase[key] = propSchema.default || 'test_string';
        } else if (propSchema.type === 'number') {
          validCase[key] = propSchema.default || 42;
        } else if (propSchema.type === 'boolean') {
          validCase[key] = propSchema.default !== undefined ? propSchema.default : true;
        } else if (propSchema.type === 'array') {
          validCase[key] = propSchema.default || ['test_item'];
        } else if (propSchema.type === 'object') {
          validCase[key] = propSchema.default || {};
        }
      });
    }
    
    testCases.push(validCase);
    
    // Invalid cases
    if (schema.required && schema.required.length > 0) {
      // Missing required property
      const missingRequired = { ...validCase };
      delete missingRequired[schema.required[0]];
      testCases.push(missingRequired);
    }
    
    // Wrong type for property
    if (schema.properties) {
      const firstProp = Object.keys(schema.properties)[0];
      if (firstProp) {
        const wrongType = { ...validCase };
        wrongType[firstProp] = 12345; // Wrong type
        testCases.push(wrongType);
      }
    }
  }
  
  return testCases;
}

describe('MCP Tool Validation and Schema Compliance', () => {
  let mcpClient: MCPTestClient;
  let availableTools: Tool[] = [];

  beforeAll(async () => {
    mcpClient = await setupMCPClient();
    
    // Get all available tools for testing
    const response = await mcpClient.client.listTools();
    availableTools = response.tools;
  }, testConfig.testTimeout);

  afterAll(async () => {
    if (mcpClient) {
      await cleanupMCPClient(mcpClient);
    }
  });

  describe('Tool Schema Validation', () => {
    test('all tools should have valid JSON schemas', () => {
      expect(availableTools.length).toBeGreaterThan(0);
      
      availableTools.forEach(tool => {
        const validation = validateJsonSchema(tool.inputSchema);
        
        if (!validation.valid) {
          console.error(`Tool ${tool.name} has invalid schema:`, validation.errors);
        }
        
        expect(validation.valid).toBe(true);
        expect(validation.errors).toHaveLength(0);
      });
    });

    test('all tools should have required metadata', () => {
      availableTools.forEach(tool => {
        // Required properties
        expect(tool.name).toBeDefined();
        expect(typeof tool.name).toBe('string');
        expect(tool.name.length).toBeGreaterThan(0);
        
        expect(tool.description).toBeDefined();
        expect(typeof tool.description).toBe('string');
        expect(tool.description.length).toBeGreaterThan(0);
        
        expect(tool.inputSchema).toBeDefined();
        expect(typeof tool.inputSchema).toBe('object');
        
        // Name should follow naming conventions
        expect(tool.name).toMatch(/^[a-z][a-z0-9_]*$/);
        
        // Description should be meaningful
        expect(tool.description.length).toBeGreaterThan(10);
      });
    });

    test('tool names should be unique', () => {
      const toolNames = availableTools.map(tool => tool.name);
      const uniqueNames = new Set(toolNames);
      
      expect(uniqueNames.size).toBe(toolNames.length);
    });

    test('tool schemas should be compilable by AJV', () => {
      availableTools.forEach(tool => {
        expect(() => {
          ajv.compile(tool.inputSchema);
        }).not.toThrow();
      });
    });
  });

  describe('Input Parameter Validation', () => {
    test('should validate required parameters for each tool', async () => {
      for (const tool of availableTools) {
        const schema = tool.inputSchema;
        
        if (schema.required && schema.required.length > 0) {
          // Test with missing required parameter
          const incompleteArgs: any = {};
          
          // Add some properties but not the required ones
          if (schema.properties) {
            const nonRequiredProps = Object.keys(schema.properties)
              .filter(prop => !schema.required.includes(prop));
            
            if (nonRequiredProps.length > 0) {
              incompleteArgs[nonRequiredProps[0]] = 'test_value';
            }
          }
          
          try {
            await mcpClient.client.callTool({
              name: tool.name,
              arguments: incompleteArgs
            });
            
            // Should not reach here if validation is working
            console.warn(`Tool ${tool.name} accepted incomplete arguments`);
          } catch (error) {
            expect(error).toBeInstanceOf(McpError);
            expect((error as McpError).code).toBe(ErrorCode.InvalidParams);
          }
        }
      }
    });

    test('should reject invalid parameter types', async () => {
      for (const tool of availableTools) {
        const schema = tool.inputSchema;
        
        if (schema.properties) {
          for (const [propName, propSchema] of Object.entries(schema.properties) as [string, any][]) {
            if (propSchema.type === 'string') {
              // Test with number instead of string
              try {
                await mcpClient.client.callTool({
                  name: tool.name,
                  arguments: {
                    [propName]: 12345
                  }
                });
                
                // Some tools might accept type coercion
                console.info(`Tool ${tool.name} accepts type coercion for ${propName}`);
              } catch (error) {
                expect(error).toBeInstanceOf(McpError);
              }
            }
          }
        }
      }
    });

    test('should handle edge case parameter values', async () => {
      const edgeCases = [
        '', // Empty string
        ' ', // Whitespace
        '\n\r\t', // Control characters
        'a'.repeat(10000), // Very long string
        '🚀🔥💯', // Unicode
        null,
        undefined
      ];
      
      for (const tool of availableTools) {
        const schema = tool.inputSchema;
        
        if (schema.properties) {
          for (const [propName, propSchema] of Object.entries(schema.properties) as [string, any][]) {
            if (propSchema.type === 'string') {
              for (const edgeCase of edgeCases) {
                try {
                  await mcpClient.client.callTool({
                    name: tool.name,
                    arguments: {
                      [propName]: edgeCase
                    }
                  });
                  
                  // Should handle gracefully
                  expect(true).toBe(true);
                } catch (error) {
                  // Should be a proper error, not a crash
                  expect(error).toBeDefined();
                }
              }
            }
          }
        }
      }
    });
  });

  describe('Output Format Validation', () => {
    test('all tool outputs should follow MCP content format', async () => {
      // Test a few representative tools
      const toolsToTest = availableTools.slice(0, Math.min(3, availableTools.length));
      
      for (const tool of toolsToTest) {
        try {
          // Generate valid test data for the tool
          const testData = generateTestData(tool.inputSchema);
          const validArgs = testData[0] || {};
          
          const response = await mcpClient.client.callTool({
            name: tool.name,
            arguments: validArgs
          });
          
          // Validate response structure
          expect(response.content).toBeDefined();
          expect(Array.isArray(response.content)).toBe(true);
          
          response.content.forEach(item => {
            expect(item.type).toBeDefined();
            expect(['text', 'image', 'resource'].includes(item.type)).toBe(true);
            
            if (item.type === 'text') {
              expect(item.text).toBeDefined();
              expect(typeof item.text).toBe('string');
            }
          });
        } catch (error) {
          // Tool might require specific setup - that's okay
          console.info(`Tool ${tool.name} requires specific setup:`, error.message);
        }
      }
    });

    test('error responses should follow MCP error format', async () => {
      // Intentionally cause errors to test error format
      try {
        await mcpClient.client.callTool({
          name: 'execute_sql',
          arguments: {
            query: 'INVALID SQL SYNTAX'
          }
        });
      } catch (error) {
        expect(error).toBeInstanceOf(McpError);
        
        const mcpError = error as McpError;
        expect(mcpError.code).toBeDefined();
        expect(typeof mcpError.code).toBe('number');
        expect(mcpError.message).toBeDefined();
        expect(typeof mcpError.message).toBe('string');
      }
    });
  });

  describe('Tool-Specific Validation', () => {
    test('list_tables tool should validate schema parameter', async () => {
      const listTablesTool = availableTools.find(tool => tool.name === 'list_tables');
      
      if (listTablesTool) {
        // Valid schemas
        const validSchemas = [['public'], ['information_schema']];
        
        for (const schemas of validSchemas) {
          try {
            const response = await mcpClient.client.callTool({
              name: 'list_tables',
              arguments: { schemas }
            });
            
            expect(response.content).toBeDefined();
          } catch (error) {
            // Some schemas might not be accessible - that's okay
            console.info(`Schema ${schemas} not accessible:`, error.message);
          }
        }
        
        // Invalid schemas
        const invalidSchemas = [
          'not_an_array',
          123,
          null,
          [123], // Array with non-string elements
        ];
        
        for (const schemas of invalidSchemas) {
          try {
            await mcpClient.client.callTool({
              name: 'list_tables',
              arguments: { schemas }
            });
            
            // Should either work with type coercion or fail
            console.info(`list_tables accepted invalid schema: ${schemas}`);
          } catch (error) {
            expect(error).toBeInstanceOf(McpError);
          }
        }
      }
    });

    test('execute_sql tool should validate query parameter', async () => {
      const executeSqlTool = availableTools.find(tool => tool.name === 'execute_sql');
      
      if (executeSqlTool) {
        // Valid queries
        const validQueries = [
          'SELECT 1',
          'SELECT COUNT(*) FROM information_schema.tables',
          'SELECT version()'
        ];
        
        for (const query of validQueries) {
          try {
            const response = await mcpClient.client.callTool({
              name: 'execute_sql',
              arguments: { query }
            });
            
            expect(response.content).toBeDefined();
          } catch (error) {
            // Some queries might fail due to permissions - that's okay
            console.info(`Query failed:`, error.message);
          }
        }
        
        // Invalid query types
        const invalidQueries = [
          123, // Number instead of string
          null,
          undefined,
          [], // Array instead of string
          {} // Object instead of string
        ];
        
        for (const query of invalidQueries) {
          try {
            await mcpClient.client.callTool({
              name: 'execute_sql',
              arguments: { query }
            });
            
            // Should either work with type coercion or fail
            console.info(`execute_sql accepted invalid query type: ${typeof query}`);
          } catch (error) {
            expect(error).toBeInstanceOf(McpError);
          }
        }
      }
    });

    test('apply_migration tool should validate required parameters', async () => {
      const applyMigrationTool = availableTools.find(tool => tool.name === 'apply_migration');
      
      if (applyMigrationTool) {
        // Test missing required parameters
        const incompleteArgs = [
          {}, // Missing both name and query
          { name: 'test_migration' }, // Missing query
          { query: 'SELECT 1' }, // Missing name
        ];
        
        for (const args of incompleteArgs) {
          try {
            await mcpClient.client.callTool({
              name: 'apply_migration',
              arguments: args
            });
            
            // Should not reach here if validation is working
            console.warn(`apply_migration accepted incomplete args:`, args);
          } catch (error) {
            expect(error).toBeInstanceOf(McpError);
            expect((error as McpError).code).toBe(ErrorCode.InvalidParams);
          }
        }
      }
    });
  });

  describe('Schema Evolution Compatibility', () => {
    test('should handle unknown properties gracefully', async () => {
      // Test with extra properties that might be added in future versions
      for (const tool of availableTools.slice(0, 2)) {
        try {
          const validArgs = generateTestData(tool.inputSchema)[0] || {};
          const argsWithExtra = {
            ...validArgs,
            future_property: 'future_value',
            another_future_prop: { nested: 'object' }
          };
          
          await mcpClient.client.callTool({
            name: tool.name,
            arguments: argsWithExtra
          });
          
          // Should either ignore extra properties or handle them gracefully
          expect(true).toBe(true);
        } catch (error) {
          // Should be a proper error if extra properties are not allowed
          expect(error).toBeInstanceOf(McpError);
        }
      }
    });

    test('should maintain backward compatibility', async () => {
      // Test that tools work with minimal required parameters
      for (const tool of availableTools) {
        const schema = tool.inputSchema;
        
        if (schema.required && schema.required.length > 0) {
          const minimalArgs: any = {};
          
          // Only include required properties
          schema.required.forEach((requiredProp: string) => {
            if (schema.properties && schema.properties[requiredProp]) {
              const propSchema = schema.properties[requiredProp];
              
              if (propSchema.type === 'string') {
                minimalArgs[requiredProp] = propSchema.default || 'test';
              } else if (propSchema.type === 'array') {
                minimalArgs[requiredProp] = propSchema.default || [];
              }
            }
          });
          
          try {
            const response = await mcpClient.client.callTool({
              name: tool.name,
              arguments: minimalArgs
            });
            
            expect(response.content).toBeDefined();
          } catch (error) {
            // Tool might require specific setup - that's okay
            console.info(`Tool ${tool.name} requires specific setup for minimal args`);
          }
        }
      }
    });
  });
});