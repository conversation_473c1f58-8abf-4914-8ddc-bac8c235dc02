import { describe, expect, test, beforeEach, afterEach, vi } from 'vitest';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StreamTransport } from '@supabase/mcp-utils';
import { createLocalSupabaseMcpServer } from '../src/server.js';
import { 
  ErrorCode, 
  McpServerError, 
  RequestTracker, 
  contextLogger,
  PerformanceMonitor,
  healthChecker 
} from '../src/utils/index.js';
import { testConfig } from './test-config.js';

/**
 * Integration tests for enhanced error handling and logging system
 */
describe('Error Handling and Logging Integration', () => {
  let client: Client;
  let server: any;
  let clientTransport: StreamTransport;
  let serverTransport: StreamTransport;

  beforeEach(async () => {
    // Reset performance metrics
    PerformanceMonitor.reset();
    
    // Set up transports
    clientTransport = new StreamTransport();
    serverTransport = new StreamTransport();
    
    clientTransport.readable.pipeTo(serverTransport.writable);
    serverTransport.readable.pipeTo(clientTransport.writable);

    // Create client
    client = new Client(
      {
        name: 'test-client',
        version: '1.0.0',
      },
      {
        capabilities: {},
      }
    );

    // Create server with test configuration
    server = createLocalSupabaseMcpServer({
      config: {
        SUPABASE_URL: testConfig.supabaseUrl,
        SUPABASE_ANON_KEY: testConfig.anonKey,
        SUPABASE_SERVICE_ROLE_KEY: testConfig.serviceKey,
        READ_ONLY: true,
        DEBUG_SQL: false,
      },
      readOnly: true,
    });

    // Connect
    await server.connect(serverTransport);
    await client.connect(clientTransport);
  });

  afterEach(async () => {
    if (client) {
      await client.close();
    }
    if (server && typeof server.close === 'function') {
      await server.close();
    }
  });

  test('should handle successful tool execution with logging', async () => {
    const result = await client.callTool({
      name: 'list_tables',
      arguments: {},
    });

    expect(result.isError).toBeFalsy();
    expect(result.content).toBeDefined();
    
    // Check that performance metrics were recorded
    const metrics = PerformanceMonitor.getMetrics();
    expect(Object.keys(metrics).length).toBeGreaterThan(0);
  });

  test('should handle tool errors gracefully', async () => {
    const result = await client.callTool({
      name: 'execute_sql',
      arguments: {
        query: 'INVALID SQL SYNTAX',
      },
    });

    expect(result.isError).toBeTruthy();
    expect(result.content).toBeDefined();
    expect(result.content[0].type).toBe('text');
    
    const errorData = JSON.parse(result.content[0].text);
    expect(errorData.error).toBeDefined();
    expect(errorData.error.code).toBeDefined();
    expect(errorData.error.timestamp).toBeDefined();
  });

  test('should handle non-existent tool calls', async () => {
    const result = await client.callTool({
      name: 'non_existent_tool',
      arguments: {},
    });

    expect(result.isError).toBeTruthy();
    expect(result.content).toBeDefined();
    
    const errorData = JSON.parse(result.content[0].text);
    expect(errorData.error.message).toContain('tool not found');
  });

  test('should handle invalid tool arguments', async () => {
    const result = await client.callTool({
      name: 'execute_sql',
      arguments: {
        // Missing required 'query' parameter
        invalidParam: 'test',
      },
    });

    expect(result.isError).toBeTruthy();
    expect(result.content).toBeDefined();
  });

  test('should track request context properly', async () => {
    const requestId = RequestTracker.generateRequestId();
    const context = {
      requestId,
      userId: 'test-user',
      operation: 'test-operation',
    };

    RequestTracker.setContext(requestId, context);
    const retrieved = RequestTracker.getContext(requestId);
    
    expect(retrieved).toEqual(context);
    
    RequestTracker.clearContext(requestId);
    expect(RequestTracker.getContext(requestId)).toBeUndefined();
  });

  test('should provide health check information', async () => {
    const health = await healthChecker.getHealth();
    
    expect(health).toBeDefined();
    expect(health.status).toMatch(/healthy|degraded|unhealthy/);
    expect(health.timestamp).toBeDefined();
    expect(health.uptime).toBeGreaterThan(0);
    expect(health.checks).toBeDefined();
    expect(health.metrics).toBeDefined();
  });

  test('should handle database connection errors', async () => {
    // Test with invalid configuration should throw during server creation
    expect(() => {
      createLocalSupabaseMcpServer({
        config: {
          SUPABASE_URL: 'http://invalid-url:12345',
          SUPABASE_ANON_KEY: 'invalid-key',
          SUPABASE_SERVICE_ROLE_KEY: 'invalid-service-key',
        },
      });
    }).toThrow();
  });

  test('should log performance metrics', async () => {
    // Execute multiple operations
    await client.callTool({ name: 'list_tables', arguments: {} });
    await client.callTool({ name: 'get_project_url', arguments: {} });
    await client.callTool({ name: 'get_anon_key', arguments: {} });

    const metrics = PerformanceMonitor.getMetrics();
    
    // Should have recorded metrics for the operations
    expect(Object.keys(metrics).length).toBeGreaterThan(0);
    
    // Each metric should have the expected structure
    for (const [operation, metric] of Object.entries(metrics)) {
      expect(metric).toHaveProperty('count');
      expect(metric).toHaveProperty('totalDuration');
      expect(metric).toHaveProperty('averageDuration');
      expect(metric).toHaveProperty('minDuration');
      expect(metric).toHaveProperty('maxDuration');
      expect(metric).toHaveProperty('errors');
      expect(metric).toHaveProperty('errorRate');
      expect(metric).toHaveProperty('successRate');
      
      expect(metric.count).toBeGreaterThan(0);
      expect(metric.averageDuration).toBeGreaterThan(0);
    }
  });

  test('should handle concurrent requests properly', async () => {
    const promises = Array.from({ length: 5 }, (_, i) =>
      client.callTool({
        name: 'list_tables',
        arguments: {},
      })
    );

    const results = await Promise.all(promises);
    
    // All requests should succeed
    results.forEach(result => {
      expect(result.isError).toBeFalsy();
    });

    // Should have recorded metrics for all requests
    const metrics = PerformanceMonitor.getMetrics();
    expect(Object.keys(metrics).length).toBeGreaterThan(0);
  });

  test('should sanitize sensitive data in logs', async () => {
    const logSpy = vi.spyOn(contextLogger, 'info');
    
    await client.callTool({
      name: 'execute_sql',
      arguments: {
        query: 'SELECT 1',
        // This should be sanitized in logs
        apiKey: 'secret-api-key',
        password: 'secret-password',
      },
    });

    // Check that sensitive data was redacted
    const logCalls = logSpy.mock.calls;
    const sensitiveDataFound = logCalls.some(call => 
      JSON.stringify(call).includes('secret-api-key') || 
      JSON.stringify(call).includes('secret-password')
    );
    
    expect(sensitiveDataFound).toBeFalsy();
    
    logSpy.mockRestore();
  });

  test('should handle server initialization errors', async () => {
    // Test with missing required configuration
    expect(() => {
      createLocalSupabaseMcpServer({
        config: {
          SUPABASE_URL: '',
          SUPABASE_ANON_KEY: '',
          SUPABASE_SERVICE_ROLE_KEY: '',
        },
      });
    }).toThrow();
  });
});

describe('Error Classes Integration', () => {
  test('should create and serialize custom errors correctly', () => {
    const error = new McpServerError(
      ErrorCode.DATABASE_ERROR,
      'Test database error',
      500,
      {
        requestId: 'test-request-123',
        context: { table: 'test_table', operation: 'SELECT' },
      }
    );

    expect(error.name).toBe('McpServerError');
    expect(error.code).toBe(ErrorCode.DATABASE_ERROR);
    expect(error.message).toBe('Test database error');
    expect(error.statusCode).toBe(500);
    expect(error.requestId).toBe('test-request-123');
    expect(error.context).toEqual({ table: 'test_table', operation: 'SELECT' });

    const serialized = error.toJSON();
    expect(serialized).toHaveProperty('name', 'McpServerError');
    expect(serialized).toHaveProperty('code', ErrorCode.DATABASE_ERROR);
    expect(serialized).toHaveProperty('message', 'Test database error');
    expect(serialized).toHaveProperty('statusCode', 500);
    expect(serialized).toHaveProperty('requestId', 'test-request-123');
    expect(serialized).toHaveProperty('context');
    expect(serialized).toHaveProperty('timestamp');
  });
});
