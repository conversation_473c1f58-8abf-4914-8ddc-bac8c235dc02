import { z } from 'zod';
import { readFileSync, existsSync, watchFile, unwatchFile } from 'fs';
import { join, resolve } from 'path';
import { contextLogger } from '../utils/logger.js';
import { ConfigurationError } from '../utils/errors.js';

/**
 * Configuration profiles for different environments
 */
export type ConfigProfile = 'development' | 'testing' | 'production' | 'local';

/**
 * Configuration source types
 */
export type ConfigSource = 'env' | 'file' | 'cli' | 'default';

/**
 * Configuration value with metadata
 */
export interface ConfigValue<T = unknown> {
  value: T;
  source: ConfigSource;
  profile: ConfigProfile;
  sensitive: boolean;
  description?: string;
  lastUpdated: Date;
}

/**
 * Configuration change event
 */
export interface ConfigChangeEvent {
  key: string;
  oldValue: unknown;
  newValue: unknown;
  source: ConfigSource;
  timestamp: Date;
}

/**
 * Configuration validation result
 */
export interface ConfigValidationResult {
  valid: boolean;
  errors: Array<{
    key: string;
    message: string;
    value: unknown;
  }>;
  warnings: Array<{
    key: string;
    message: string;
    value: unknown;
  }>;
}

/**
 * Feature flag configuration
 */
export interface FeatureFlag {
  enabled: boolean;
  description: string;
  conditions?: Record<string, unknown>;
  rolloutPercentage?: number;
}

/**
 * Base configuration schema
 */
export const BaseConfigSchema = z.object({
  // Core Supabase configuration
  SUPABASE_URL: z.string().url().describe('Supabase instance URL'),
  SUPABASE_ANON_KEY: z.string().min(1).describe('Supabase anonymous key'),
  SUPABASE_SERVICE_ROLE_KEY: z.string().min(1).describe('Supabase service role key'),
  
  // Optional database configuration
  DATABASE_URL: z.string().url().optional().describe('Direct database connection URL'),
  
  // Server configuration
  READ_ONLY: z.boolean().default(false).describe('Enable read-only mode'),
  DEBUG_SQL: z.boolean().default(false).describe('Enable SQL query debugging'),
  LOG_LEVEL: z.enum(['trace', 'debug', 'info', 'warn', 'error', 'fatal']).default('info').describe('Logging level'),
  
  // Performance configuration
  MAX_CONNECTIONS: z.number().min(1).max(100).default(10).describe('Maximum database connections'),
  QUERY_TIMEOUT: z.number().min(1000).max(60000).default(30000).describe('Query timeout in milliseconds'),
  CACHE_TTL: z.number().min(0).max(3600000).default(300000).describe('Cache TTL in milliseconds'),
  
  // Security configuration
  RATE_LIMIT_REQUESTS: z.number().min(1).max(10000).default(100).describe('Rate limit requests per minute'),
  RATE_LIMIT_WINDOW: z.number().min(1000).max(3600000).default(60000).describe('Rate limit window in milliseconds'),
  ENABLE_CORS: z.boolean().default(true).describe('Enable CORS headers'),
  
  // Feature flags
  ENABLE_ANALYTICS: z.boolean().default(false).describe('Enable analytics collection'),
  ENABLE_METRICS: z.boolean().default(true).describe('Enable performance metrics'),
  ENABLE_HEALTH_CHECKS: z.boolean().default(true).describe('Enable health check endpoints'),
  ENABLE_HOT_RELOAD: z.boolean().default(false).describe('Enable configuration hot reloading'),
});

export type BaseConfig = z.infer<typeof BaseConfigSchema>;

/**
 * Profile-specific configuration schemas
 */
export const ProfileConfigSchemas = {
  development: BaseConfigSchema.extend({
    DEBUG_SQL: z.boolean().default(true),
    LOG_LEVEL: z.enum(['trace', 'debug', 'info', 'warn', 'error', 'fatal']).default('debug'),
    ENABLE_HOT_RELOAD: z.boolean().default(true),
    ENABLE_ANALYTICS: z.boolean().default(false),
  }),
  
  testing: BaseConfigSchema.extend({
    READ_ONLY: z.boolean().default(true),
    LOG_LEVEL: z.enum(['trace', 'debug', 'info', 'warn', 'error', 'fatal']).default('warn'),
    QUERY_TIMEOUT: z.number().default(5000),
    ENABLE_ANALYTICS: z.boolean().default(false),
    ENABLE_METRICS: z.boolean().default(false),
  }),
  
  production: BaseConfigSchema.extend({
    DEBUG_SQL: z.boolean().default(false),
    LOG_LEVEL: z.enum(['trace', 'debug', 'info', 'warn', 'error', 'fatal']).default('info'),
    ENABLE_HOT_RELOAD: z.boolean().default(false),
    ENABLE_ANALYTICS: z.boolean().default(true),
    MAX_CONNECTIONS: z.number().default(50),
  }),
  
  local: BaseConfigSchema.extend({
    SUPABASE_URL: z.string().url().default('https://devdb.syncrobit.net'),
    DEBUG_SQL: z.boolean().default(true),
    LOG_LEVEL: z.enum(['trace', 'debug', 'info', 'warn', 'error', 'fatal']).default('debug'),
    ENABLE_HOT_RELOAD: z.boolean().default(true),
  }),
};

/**
 * Sensitive configuration keys that should be redacted in logs
 */
export const SENSITIVE_KEYS = [
  'SUPABASE_ANON_KEY',
  'SUPABASE_SERVICE_ROLE_KEY',
  'DATABASE_URL',
  'API_KEY',
  'SECRET',
  'PASSWORD',
  'TOKEN',
] as const;

/**
 * Enhanced configuration manager with multiple sources and hot reloading
 */
export class ConfigManager {
  private config = new Map<string, ConfigValue>();
  private watchers = new Map<string, () => void>();
  private changeListeners = new Set<(event: ConfigChangeEvent) => void>();
  private featureFlags = new Map<string, FeatureFlag>();
  private logger = contextLogger.child({ component: 'ConfigManager' });
  private profile: ConfigProfile;
  private configFiles: string[] = [];

  constructor(profile: ConfigProfile = 'development') {
    this.profile = profile;
    this.logger.info('Initializing configuration manager', { profile });
  }

  /**
   * Load configuration from multiple sources
   */
  async load(options: {
    configFiles?: string[];
    envPrefix?: string;
    cliArgs?: Record<string, unknown>;
  } = {}): Promise<void> {
    const { configFiles = [], envPrefix = '', cliArgs = {} } = options;
    this.configFiles = configFiles;

    try {
      // 1. Load default configuration
      await this.loadDefaults();

      // 2. Load from configuration files
      for (const configFile of configFiles) {
        await this.loadFromFile(configFile);
      }

      // 3. Load from environment variables
      await this.loadFromEnv(envPrefix);

      // 4. Load from CLI arguments
      await this.loadFromCli(cliArgs);

      // 5. Validate final configuration
      const validation = await this.validate();
      if (!validation.valid) {
        throw new ConfigurationError(
          `Configuration validation failed: ${validation.errors.map(e => e.message).join(', ')}`
        );
      }

      // 6. Setup hot reloading if enabled
      if (this.get('ENABLE_HOT_RELOAD')) {
        this.setupHotReload();
      }

      this.logger.info('Configuration loaded successfully', {
        profile: this.profile,
        sources: this.getConfigSources(),
        totalKeys: this.config.size,
      });
    } catch (error) {
      this.logger.error('Failed to load configuration', error instanceof Error ? error : undefined);
      throw error;
    }
  }

  /**
   * Get configuration value
   */
  get<T = unknown>(key: string, defaultValue?: T): T {
    const configValue = this.config.get(key);
    if (configValue) {
      return configValue.value as T;
    }
    return defaultValue as T;
  }

  /**
   * Get configuration value with metadata
   */
  getWithMetadata(key: string): ConfigValue | undefined {
    return this.config.get(key);
  }

  /**
   * Set configuration value
   */
  set(key: string, value: unknown, source: ConfigSource = 'cli'): void {
    const oldValue = this.config.get(key)?.value;
    const configValue: ConfigValue = {
      value,
      source,
      profile: this.profile,
      sensitive: SENSITIVE_KEYS.some(sensitiveKey => key.includes(sensitiveKey)),
      lastUpdated: new Date(),
    };

    this.config.set(key, configValue);

    // Emit change event
    const changeEvent: ConfigChangeEvent = {
      key,
      oldValue,
      newValue: value,
      source,
      timestamp: new Date(),
    };

    this.emitChange(changeEvent);
    
    this.logger.debug('Configuration value updated', {
      key,
      source,
      hasOldValue: oldValue !== undefined,
      sensitive: configValue.sensitive,
    });
  }

  /**
   * Check if configuration key exists
   */
  has(key: string): boolean {
    return this.config.has(key);
  }

  /**
   * Get all configuration keys
   */
  keys(): string[] {
    return Array.from(this.config.keys());
  }

  /**
   * Get configuration as plain object (with sensitive values redacted)
   */
  toObject(includeSensitive: boolean = false): Record<string, unknown> {
    const result: Record<string, unknown> = {};
    
    for (const [key, configValue] of this.config.entries()) {
      if (configValue.sensitive && !includeSensitive) {
        result[key] = '[REDACTED]';
      } else {
        result[key] = configValue.value;
      }
    }
    
    return result;
  }

  /**
   * Validate current configuration
   */
  async validate(): Promise<ConfigValidationResult> {
    const result: ConfigValidationResult = {
      valid: true,
      errors: [],
      warnings: [],
    };

    try {
      // Get the appropriate schema for the current profile
      const schema = ProfileConfigSchemas[this.profile] || BaseConfigSchema;
      
      // Convert config to plain object for validation
      const configObject = this.toObject(true);
      
      // Validate with Zod
      schema.parse(configObject);
    } catch (error) {
      if (error instanceof z.ZodError) {
        result.valid = false;
        result.errors = error.errors.map(err => ({
          key: err.path.join('.'),
          message: err.message,
          value: this.get(err.path.join('.')),
        }));
      } else {
        result.valid = false;
        result.errors.push({
          key: 'unknown',
          message: error instanceof Error ? error.message : String(error),
          value: undefined,
        });
      }
    }

    // Custom validation rules
    await this.runCustomValidation(result);

    return result;
  }

  /**
   * Reload configuration from all sources
   */
  async reload(): Promise<void> {
    this.logger.info('Reloading configuration');
    
    // Clear current configuration
    this.config.clear();
    
    // Reload from all sources
    await this.load({
      configFiles: this.configFiles,
    });
  }

  /**
   * Add configuration change listener
   */
  onChange(listener: (event: ConfigChangeEvent) => void): () => void {
    this.changeListeners.add(listener);
    
    // Return unsubscribe function
    return () => {
      this.changeListeners.delete(listener);
    };
  }

  /**
   * Get feature flag value
   */
  getFeatureFlag(name: string): boolean {
    const flag = this.featureFlags.get(name);
    if (!flag) {
      return false;
    }

    // Check rollout percentage
    if (flag.rolloutPercentage !== undefined) {
      const hash = this.hashString(name);
      const percentage = (hash % 100) + 1;
      if (percentage > flag.rolloutPercentage) {
        return false;
      }
    }

    return flag.enabled;
  }

  /**
   * Set feature flag
   */
  setFeatureFlag(name: string, flag: FeatureFlag): void {
    this.featureFlags.set(name, flag);
    this.logger.debug('Feature flag updated', { name, enabled: flag.enabled });
  }

  /**
   * Get configuration sources summary
   */
  getConfigSources(): Record<ConfigSource, number> {
    const sources: Record<ConfigSource, number> = {
      env: 0,
      file: 0,
      cli: 0,
      default: 0,
    };

    for (const configValue of this.config.values()) {
      sources[configValue.source]++;
    }

    return sources;
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    // Stop watching files
    for (const [filePath, unwatcher] of this.watchers.entries()) {
      unwatchFile(filePath, unwatcher);
    }
    this.watchers.clear();

    // Clear listeners
    this.changeListeners.clear();

    this.logger.info('Configuration manager destroyed');
  }

  /**
   * Load default configuration values
   */
  private async loadDefaults(): Promise<void> {
    const schema = ProfileConfigSchemas[this.profile] || BaseConfigSchema;
    const defaults = this.extractDefaults(schema);

    for (const [key, value] of Object.entries(defaults)) {
      this.config.set(key, {
        value,
        source: 'default',
        profile: this.profile,
        sensitive: SENSITIVE_KEYS.some(sensitiveKey => key.includes(sensitiveKey)),
        lastUpdated: new Date(),
      });
    }

    this.logger.debug('Default configuration loaded', { count: Object.keys(defaults).length });
  }

  /**
   * Load configuration from file
   */
  private async loadFromFile(filePath: string): Promise<void> {
    const resolvedPath = resolve(filePath);

    if (!existsSync(resolvedPath)) {
      this.logger.warn('Configuration file not found', { filePath: resolvedPath });
      return;
    }

    try {
      const content = readFileSync(resolvedPath, 'utf-8');
      let config: Record<string, unknown>;

      if (filePath.endsWith('.json')) {
        config = JSON.parse(content);
      } else if (filePath.endsWith('.env') || filePath.includes('.env')) {
        config = this.parseEnvFile(content);
      } else {
        throw new Error(`Unsupported configuration file format: ${filePath}`);
      }

      for (const [key, value] of Object.entries(config)) {
        this.config.set(key, {
          value,
          source: 'file',
          profile: this.profile,
          sensitive: SENSITIVE_KEYS.some(sensitiveKey => key.includes(sensitiveKey)),
          description: `Loaded from ${filePath}`,
          lastUpdated: new Date(),
        });
      }

      this.logger.debug('Configuration loaded from file', {
        filePath: resolvedPath,
        count: Object.keys(config).length
      });
    } catch (error) {
      this.logger.error('Failed to load configuration file', error instanceof Error ? error : undefined, {
        filePath: resolvedPath,
      });
      throw new ConfigurationError(`Failed to load configuration from ${filePath}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Load configuration from environment variables
   */
  private async loadFromEnv(prefix: string = ''): Promise<void> {
    const envVars = process.env;
    let count = 0;

    for (const [key, value] of Object.entries(envVars)) {
      if (value === undefined) continue;

      // Apply prefix filter if specified
      const configKey = prefix && key.startsWith(prefix) ? key.slice(prefix.length) : key;

      // Only load known configuration keys
      if (this.isValidConfigKey(configKey)) {
        this.config.set(configKey, {
          value: this.parseEnvValue(value),
          source: 'env',
          profile: this.profile,
          sensitive: SENSITIVE_KEYS.some(sensitiveKey => configKey.includes(sensitiveKey)),
          description: `Environment variable: ${key}`,
          lastUpdated: new Date(),
        });
        count++;
      }
    }

    this.logger.debug('Configuration loaded from environment', { count, prefix });
  }

  /**
   * Load configuration from CLI arguments
   */
  private async loadFromCli(cliArgs: Record<string, unknown>): Promise<void> {
    for (const [key, value] of Object.entries(cliArgs)) {
      if (this.isValidConfigKey(key)) {
        this.config.set(key, {
          value,
          source: 'cli',
          profile: this.profile,
          sensitive: SENSITIVE_KEYS.some(sensitiveKey => key.includes(sensitiveKey)),
          description: 'Command line argument',
          lastUpdated: new Date(),
        });
      }
    }

    this.logger.debug('Configuration loaded from CLI', { count: Object.keys(cliArgs).length });
  }

  /**
   * Setup hot reloading for configuration files
   */
  private setupHotReload(): void {
    for (const filePath of this.configFiles) {
      const resolvedPath = resolve(filePath);

      if (existsSync(resolvedPath)) {
        const watcher = () => {
          this.logger.info('Configuration file changed, reloading', { filePath: resolvedPath });
          this.reload().catch(error => {
            this.logger.error('Failed to reload configuration', error instanceof Error ? error : undefined);
          });
        };

        watchFile(resolvedPath, { interval: 1000 }, watcher);
        this.watchers.set(resolvedPath, watcher);

        this.logger.debug('Watching configuration file for changes', { filePath: resolvedPath });
      }
    }
  }

  /**
   * Emit configuration change event
   */
  private emitChange(event: ConfigChangeEvent): void {
    for (const listener of this.changeListeners) {
      try {
        listener(event);
      } catch (error) {
        this.logger.error('Configuration change listener error', error instanceof Error ? error : undefined);
      }
    }
  }

  /**
   * Run custom validation rules
   */
  private async runCustomValidation(result: ConfigValidationResult): Promise<void> {
    // Validate URL accessibility
    const supabaseUrl = this.get('SUPABASE_URL');
    if (supabaseUrl && typeof supabaseUrl === 'string') {
      try {
        new URL(supabaseUrl);
      } catch {
        result.errors.push({
          key: 'SUPABASE_URL',
          message: 'Invalid URL format',
          value: supabaseUrl,
        });
        result.valid = false;
      }
    }

    // Validate key lengths
    const anonKey = this.get('SUPABASE_ANON_KEY');
    if (anonKey && typeof anonKey === 'string' && anonKey.length < 100) {
      result.warnings.push({
        key: 'SUPABASE_ANON_KEY',
        message: 'Anonymous key seems too short, might be invalid',
        value: anonKey,
      });
    }

    // Validate production settings
    if (this.profile === 'production') {
      if (this.get('DEBUG_SQL')) {
        result.warnings.push({
          key: 'DEBUG_SQL',
          message: 'Debug SQL should be disabled in production',
          value: this.get('DEBUG_SQL'),
        });
      }

      if (this.get('LOG_LEVEL') === 'debug' || this.get('LOG_LEVEL') === 'trace') {
        result.warnings.push({
          key: 'LOG_LEVEL',
          message: 'Verbose logging should be avoided in production',
          value: this.get('LOG_LEVEL'),
        });
      }
    }
  }

  /**
   * Extract default values from Zod schema
   */
  private extractDefaults(schema: z.ZodObject<any>): Record<string, unknown> {
    const defaults: Record<string, unknown> = {};

    try {
      const parsed = schema.parse({});
      return parsed;
    } catch (error) {
      if (error instanceof z.ZodError) {
        // Extract defaults from schema definition
        const shape = schema.shape;
        for (const [key, fieldSchema] of Object.entries(shape)) {
          if (fieldSchema instanceof z.ZodDefault) {
            defaults[key] = fieldSchema._def.defaultValue();
          } else if (fieldSchema instanceof z.ZodOptional) {
            // Optional fields don't have defaults
            continue;
          }
        }
      }
    }

    return defaults;
  }

  /**
   * Parse environment file content
   */
  private parseEnvFile(content: string): Record<string, string> {
    const result: Record<string, string> = {};
    const lines = content.split('\n');

    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#')) {
        const [key, ...valueParts] = trimmed.split('=');
        if (key && valueParts.length > 0) {
          const value = valueParts.join('=').replace(/^["']|["']$/g, '');
          result[key.trim()] = value;
        }
      }
    }

    return result;
  }

  /**
   * Parse environment variable value to appropriate type
   */
  private parseEnvValue(value: string): unknown {
    // Boolean values
    if (value.toLowerCase() === 'true') return true;
    if (value.toLowerCase() === 'false') return false;

    // Number values
    if (/^\d+$/.test(value)) return parseInt(value, 10);
    if (/^\d+\.\d+$/.test(value)) return parseFloat(value);

    // JSON values
    if ((value.startsWith('{') && value.endsWith('}')) ||
        (value.startsWith('[') && value.endsWith(']'))) {
      try {
        return JSON.parse(value);
      } catch {
        // Fall through to string
      }
    }

    return value;
  }

  /**
   * Check if a key is a valid configuration key
   */
  private isValidConfigKey(key: string): boolean {
    const schema = ProfileConfigSchemas[this.profile] || BaseConfigSchema;
    return key in schema.shape || SENSITIVE_KEYS.some(sensitiveKey => key.includes(sensitiveKey));
  }

  /**
   * Simple hash function for feature flag rollouts
   */
  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }
}

/**
 * Global configuration manager instance
 */
export const configManager = new ConfigManager();
