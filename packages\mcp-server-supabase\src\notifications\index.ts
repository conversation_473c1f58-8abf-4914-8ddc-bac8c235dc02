/**
 * Notifications Module
 * 
 * Provides comprehensive notification, event-driven communication, and
 * real-time protocol support for the MCP server.
 */

export {
  NotificationManager,
  type NotificationPayload,
  type NotificationHandler,
  type NotificationQueueItem,
  type NotificationStats,
  NotificationTypes,
  type NotificationType,
} from './notification-manager.js';

export {
  ProtocolCommunication,
  type ProtocolMessage,
  type ClientConnection,
  type ProtocolCommunicationOptions,
} from './protocol-communication.js';

export {
  EventSystem,
  type ServerEvent,
  type EventFilter,
  type EventSubscription,
  type EventStats,
  EventTypes,
  type EventType,
} from './event-system.js';

export {
  WebSocketTransport,
  type WebSocketTransportOptions,
  type WebSocketClient,
} from './websocket-transport.js';

export {
  SSETransport,
  type SSETransportOptions,
  type SSEClient,
} from './sse-transport.js';

export {
  RealTimeTransport,
  type RealTimeTransportOptions,
  type TransportStats,
} from './realtime-transport.js';

/**
 * Create a complete notification system for the MCP server
 */
import { NotificationManager } from './notification-manager.js';
import { ProtocolCommunication } from './protocol-communication.js';
import { EventSystem } from './event-system.js';
import type { ConfigManager } from '../config/index.js';

export interface NotificationSystemOptions {
  enableNotifications?: boolean;
  enableRealTimeUpdates?: boolean;
  enableEventHistory?: boolean;
  enableMetrics?: boolean;
  debugEvents?: boolean;
}

export class NotificationSystem {
  public readonly notificationManager: NotificationManager;
  public readonly protocolCommunication: ProtocolCommunication;
  public readonly eventSystem: EventSystem;
  
  constructor(
    config?: ConfigManager,
    options: NotificationSystemOptions = {}
  ) {
    // Create notification manager
    this.notificationManager = new NotificationManager(config);
    
    // Create protocol communication
    this.protocolCommunication = new ProtocolCommunication(
      this.notificationManager,
      {
        enableNotifications: options.enableNotifications,
        enableRealTimeUpdates: options.enableRealTimeUpdates,
      },
      config
    );
    
    // Create event system
    this.eventSystem = new EventSystem(
      this.notificationManager,
      this.protocolCommunication,
      config
    );
  }

  /**
   * Initialize the complete notification system
   */
  async initialize(): Promise<void> {
    // The notification system is already initialized in constructors
    // This method is for any additional setup if needed
  }

  /**
   * Shutdown the complete notification system
   */
  shutdown(): void {
    this.eventSystem.shutdown();
    this.protocolCommunication.shutdown();
    this.notificationManager.shutdown();
  }

  /**
   * Get combined statistics from all components
   */
  getSystemStats() {
    return {
      notifications: this.notificationManager.getStats(),
      protocol: this.protocolCommunication.getConnectionStats(),
      events: this.eventSystem.getStats(),
    };
  }
}

/**
 * Create a notification system with default configuration
 */
export function createNotificationSystem(
  config?: ConfigManager,
  options?: NotificationSystemOptions
): NotificationSystem {
  return new NotificationSystem(config, options);
}