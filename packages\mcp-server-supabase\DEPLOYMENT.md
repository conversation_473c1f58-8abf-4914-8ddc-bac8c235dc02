# MCP Server Supabase - Simplified Deployment Guide

## 🚀 Quick Deployment for Remote Supabase Instances

This guide shows how to deploy the MCP Server Supabase to client machines that only need to connect to remote Supabase instances, without running the full development environment locally.

## 📋 Prerequisites

- Node.js 18+ installed on the target machine
- Access to a Supabase instance (local or remote)
- Supabase Anonymous Key and Service Role Key

## 🎯 Deployment Options

### Option 1: NPX (Recommended - No Installation Required)

The simplest approach using npx to run the server directly:

**MCP Configuration (mcp.json):**
```json
{
  "mcpServers": {
    "supabase": {
      "command": "npx",
      "args": [
        "@supabase/mcp-server-supabase",
        "--supabase-url=YOUR_SUPABASE_URL",
        "--anon-key=YOUR_ANON_KEY",
        "--service-key=YOUR_SERVICE_KEY"
      ],
      "alwaysAllow": [
        "list_tables",
        "execute_sql",
        "check_local_connection"
      ]
    }
  }
}
```

### Option 2: Global Installation

Install the package globally and reference it:

```bash
npm install -g @supabase/mcp-server-supabase
```

**MCP Configuration:**
```json
{
  "mcpServers": {
    "supabase": {
      "command": "mcp-server-supabase",
      "env": {
        "SUPABASE_URL": "YOUR_SUPABASE_URL",
        "SUPABASE_ANON_KEY": "YOUR_ANON_KEY",
        "SUPABASE_SERVICE_ROLE_KEY": "YOUR_SERVICE_KEY",
        "READ_ONLY": "false"
      },
      "alwaysAllow": [
        "list_tables",
        "execute_sql",
        "check_local_connection"
      ]
    }
  }
}
```

### Option 3: Local Package Installation

For projects that need to manage the MCP server as a dependency:

```bash
npm install @supabase/mcp-server-supabase
```

**MCP Configuration:**
```json
{
  "mcpServers": {
    "supabase": {
      "command": "npx",
      "args": ["mcp-server-supabase"],
      "env": {
        "SUPABASE_URL": "YOUR_SUPABASE_URL",
        "SUPABASE_ANON_KEY": "YOUR_ANON_KEY", 
        "SUPABASE_SERVICE_ROLE_KEY": "YOUR_SERVICE_KEY"
      }
    }
  }
}
```

## 🔧 Configuration Options

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `SUPABASE_URL` | Your Supabase instance URL | Yes |
| `SUPABASE_ANON_KEY` | Supabase anonymous key | Yes |
| `SUPABASE_SERVICE_ROLE_KEY` | Supabase service role key | Yes |
| `READ_ONLY` | Restrict to read-only operations | No (default: false) |
| `DEBUG_SQL` | Enable SQL query debugging | No (default: false) |

### CLI Arguments

You can also pass configuration via CLI arguments:

```bash
npx @supabase/mcp-server-supabase \
  --supabase-url="https://your-project.supabase.co" \
  --anon-key="your-anon-key" \
  --service-key="your-service-key" \
  --read-only
```

## 🛡️ Security Best Practices

1. **Environment Variables**: Use environment variables instead of hardcoding keys in configuration files
2. **Read-Only Mode**: Consider using `READ_ONLY: true` for production clients
3. **Key Rotation**: Regularly rotate your Supabase keys
4. **Network Security**: Ensure proper network security between client and Supabase instance

## ✅ Verification

Test your deployment with:

```bash
# Test the installation
npx @supabase/mcp-server-supabase --version

# Test connectivity (replace with your actual keys)
npx @supabase/mcp-server-supabase \
  --supabase-url="YOUR_URL" \
  --anon-key="YOUR_KEY" \
  --service-key="YOUR_SERVICE_KEY"
```

## 📚 Available Tools

Once deployed, the MCP server provides these tools:

- **Database Operations**: CRUD operations, SQL execution, schema inspection
- **Constraint Validation**: Foreign key, unique, and check constraint validation
- **JSON Handling**: JSON validation, parsing, transformation, and repair
- **Transaction Management**: ACID transactions, savepoints, rollback scenarios
- **Migration Tools**: Database schema migration management
- **Development Tools**: Project configuration and utility functions

## 🐛 Troubleshooting

### Common Issues

1. **Connection Failed**: Verify your Supabase URL and keys
2. **Permission Denied**: Check that your service role key has necessary permissions
3. **Module Not Found**: Ensure Node.js 18+ is installed

### Debug Mode

Enable debug logging:

```json
{
  "env": {
    "DEBUG_SQL": "true"
  }
}
```

## 📞 Support

For issues and questions:
- Check the [README.md](./README.md) for detailed documentation
- Review error logs for specific error messages
- Verify network connectivity to your Supabase instance

---

## Migration from Local Setup

If you're migrating from a local development setup:

1. **Remove local dependencies**: You no longer need Docker, local Supabase CLI, or the full project
2. **Update configuration**: Switch from local URLs to your remote Supabase instance
3. **Test thoroughly**: Verify all required tools work with your remote instance
4. **Clean up**: Remove local development files and configurations

The simplified deployment provides the same comprehensive MCP functionality without the overhead of local development infrastructure. 