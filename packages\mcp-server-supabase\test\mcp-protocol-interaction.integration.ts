import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { describe, expect, test, beforeAll, afterAll } from 'vitest';
import { 
  CallToolRequestSchema,
  ListToolsRequestSchema,
  GetPromptRequestSchema,
  ListPromptsRequestSchema,
  ListResourcesRequestSchema,
  ReadResourceRequestSchema,
  LoggingMessageNotificationSchema,
  ErrorCode,
  McpError
} from '@modelcontextprotocol/sdk/types.js';
import { ACCESS_TOKEN, MCP_CLIENT_NAME, MCP_CLIENT_VERSION } from './mocks.js';
import { testConfig } from './test-config.js';

/**
 * MCP Protocol Interaction Tests
 * 
 * These tests validate the MCP protocol implementation by testing:
 * - Tool discovery and validation
 * - Tool execution with various parameters
 * - Error handling and protocol compliance
 * - Resource management
 * - Prompt handling
 * - Protocol message validation
 */

type MCPTestClient = {
  client: Client;
  transport: StdioClientTransport;
};

type SetupOptions = {
  anonKey?: string;
  serviceKey?: string;
  supabaseUrl?: string;
  readOnly?: boolean;
};

async function setupMCPClient(options: SetupOptions = {}): Promise<MCPTestClient> {
  const {
    anonKey = process.env.SUPABASE_ANON_KEY || testConfig.anonKey,
    serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || testConfig.serviceKey,
    supabaseUrl = process.env.SUPABASE_URL || testConfig.supabaseUrl,
    readOnly = false
  } = options;

  const client = new Client(
    {
      name: MCP_CLIENT_NAME,
      version: MCP_CLIENT_VERSION,
    },
    {
      capabilities: {
        tools: {},
        prompts: {},
        resources: {},
        logging: {}
      },
    }
  );

  // Set up logging notification handler for test debugging
  client.setNotificationHandler(LoggingMessageNotificationSchema, (message) => {
    const { level, data } = message.params;
    if (level === 'error') {
      console.error('MCP Server Error:', data);
    } else if (level === 'debug') {
      console.debug('MCP Server Debug:', data);
    }
  });

  // Use the built stdio transport directly
  const command = 'node';
  const args = ['dist/transports/stdio.js'];

  // Set environment variables for the MCP server process
  const env = {
    ...process.env,
    SUPABASE_URL: supabaseUrl,
    SUPABASE_ANON_KEY: anonKey,
    SUPABASE_SERVICE_ROLE_KEY: serviceKey,
    READ_ONLY: readOnly ? 'true' : 'false'
  };

  const transport = new StdioClientTransport({
    command,
    args,
    env
  });

  await client.connect(transport);
  return { client, transport };
}

async function cleanupMCPClient(mcpClient: MCPTestClient): Promise<void> {
  try {
    await mcpClient.client.close();
    await mcpClient.transport.close();
  } catch (error) {
    console.warn('Error during MCP client cleanup:', error);
  }
}

describe('MCP Protocol Interaction Tests', () => {
  let mcpClient: MCPTestClient;

  beforeAll(async () => {
    // Skip tests if required environment variables are not set
    if (!process.env.SUPABASE_URL && !testConfig.supabaseUrl) {
      console.warn('Skipping MCP Protocol tests: SUPABASE_URL not configured');
      return;
    }
    
    try {
      mcpClient = await setupMCPClient();
    } catch (error) {
      console.warn('Failed to setup MCP client:', error);
      throw error;
    }
  }, testConfig.testTimeout);

  afterAll(async () => {
    if (mcpClient) {
      await cleanupMCPClient(mcpClient);
    }
  });

  describe('Tool Discovery and Validation', () => {
    test('should list available tools with proper schema', async () => {
      if (!mcpClient) {
        console.warn('MCP client not available, skipping test');
        return;
      }
      
      const response = await mcpClient.client.listTools();
      
      expect(response.tools).toBeDefined();
      expect(Array.isArray(response.tools)).toBe(true);
      expect(response.tools.length).toBeGreaterThan(0);
      
      // Validate each tool has required properties
      response.tools.forEach(tool => {
        expect(tool.name).toBeDefined();
        expect(typeof tool.name).toBe('string');
        expect(tool.description).toBeDefined();
        expect(typeof tool.description).toBe('string');
        expect(tool.inputSchema).toBeDefined();
        expect(typeof tool.inputSchema).toBe('object');
      });
    });

    test('should include expected Supabase MCP tools', async () => {
      if (!mcpClient) {
        console.warn('MCP client not available, skipping test');
        return;
      }
      
      const response = await mcpClient.client.listTools();
      const toolNames = response.tools.map(tool => tool.name);
      
      // Expected core tools for Supabase MCP server
      const expectedTools = [
        'list_tables',
        'execute_sql'
      ];
      
      expectedTools.forEach(expectedTool => {
        expect(toolNames).toContain(expectedTool);
      });
    });

    test('should validate tool input schemas are valid JSON Schema', async () => {
      if (!mcpClient) {
        console.warn('MCP client not available, skipping test');
        return;
      }
      
      const response = await mcpClient.client.listTools();
      
      response.tools.forEach(tool => {
        const schema = tool.inputSchema;
        
        // Basic JSON Schema validation
        expect(schema.type).toBeDefined();
        expect(['object', 'string', 'number', 'boolean', 'array', 'null']).toContain(schema.type);
        
        if (schema.type === 'object') {
          expect(schema.properties).toBeDefined();
          expect(typeof schema.properties).toBe('object');
        }
      });
    });
  });

  describe('Tool Execution Tests', () => {
    test('should execute list_tables tool successfully', async () => {
      if (!mcpClient) {
        console.warn('MCP client not available, skipping test');
        return;
      }
      
      const response = await mcpClient.client.callTool({
        name: 'list_tables',
        arguments: {
          schemas: ['public']
        }
      });
      
      expect(response.content).toBeDefined();
      expect(Array.isArray(response.content)).toBe(true);
      expect(response.content.length).toBeGreaterThan(0);
      
      // Validate response content structure
      response.content.forEach(item => {
        expect(item.type).toBe('text');
        expect(item.text).toBeDefined();
      });
    });

    test('should handle invalid tool parameters gracefully', async () => {
      if (!mcpClient) {
        console.warn('MCP client not available, skipping test');
        return;
      }
      
      try {
        await mcpClient.client.callTool({
          name: 'list_tables',
          arguments: {
            invalid_parameter: 'invalid_value'
          }
        });
        
        // If no error is thrown, the tool should still return valid content
        expect(true).toBe(true);
      } catch (error) {
        // If an error is thrown, it should be a proper MCP error
        expect(error).toBeInstanceOf(McpError);
        expect((error as McpError).code).toBeDefined();
      }
    });

    test('should execute read-only SQL queries safely', async () => {
      if (!mcpClient) {
        console.warn('MCP client not available, skipping test');
        return;
      }
      
      const response = await mcpClient.client.callTool({
        name: 'execute_sql',
        arguments: {
          query: 'SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = \'public\''
        }
      });
      
      expect(response.content).toBeDefined();
      expect(Array.isArray(response.content)).toBe(true);
      expect(response.content.length).toBeGreaterThan(0);
      
      const textContent = response.content.find(item => item.type === 'text');
      expect(textContent).toBeDefined();
      expect(textContent?.text).toContain('table_count');
    });
  });

  describe('Error Handling and Protocol Compliance', () => {
    test('should handle non-existent tool calls properly', async () => {
      if (!mcpClient) {
        console.warn('MCP client not available, skipping test');
        return;
      }
      
      try {
        await mcpClient.client.callTool({
          name: 'non_existent_tool',
          arguments: {}
        });
        
        // Should not reach here
        expect(false).toBe(true);
      } catch (error) {
        // Accept any error type - the important thing is that it throws an error
        expect(error).toBeDefined();
        expect(error instanceof Error).toBe(true);
      }
    });

    test('should validate required parameters', async () => {
      if (!mcpClient) {
        console.warn('MCP client not available, skipping test');
        return;
      }
      
      try {
        await mcpClient.client.callTool({
          name: 'execute_sql',
          arguments: {} // Missing required 'query' parameter
        });
        
        // Should not reach here
        expect(false).toBe(true);
      } catch (error) {
        // Accept any error type - the important thing is that it throws an error
        expect(error).toBeDefined();
        expect(error instanceof Error).toBe(true);
      }
    });

    test('should maintain connection stability during errors', async () => {
      if (!mcpClient) {
        console.warn('MCP client not available, skipping test');
        return;
      }
      
      // Execute a failing operation
      try {
        await mcpClient.client.callTool({
          name: 'execute_sql',
          arguments: {
            query: 'SELECT * FROM non_existent_table'
          }
        });
      } catch (error) {
        // Expected to fail
      }
      
      // Verify connection is still stable by executing a valid operation
      const response = await mcpClient.client.listTools();
      expect(response.tools).toBeDefined();
      expect(response.tools.length).toBeGreaterThan(0);
    });
  });

  describe('Performance and Reliability Tests', () => {
    test('should handle concurrent tool calls', async () => {
      if (!mcpClient) {
        console.warn('MCP client not available, skipping test');
        return;
      }
      
      const concurrentCalls = Array.from({ length: 5 }, () => 
        mcpClient.client.callTool({
          name: 'list_tables',
          arguments: { schemas: ['public'] }
        })
      );
      
      const results = await Promise.allSettled(concurrentCalls);
      
      // All calls should succeed
      results.forEach(result => {
        expect(result.status).toBe('fulfilled');
        if (result.status === 'fulfilled') {
          expect(result.value.content).toBeDefined();
        }
      });
    });

    test('should respond within reasonable time limits', async () => {
      if (!mcpClient) {
        console.warn('MCP client not available, skipping test');
        return;
      }
      
      const startTime = Date.now();
      
      await mcpClient.client.listTools();
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Should respond within 5 seconds
      expect(duration).toBeLessThan(5000);
    });
  });

  describe('Protocol Message Validation', () => {
    test('should send properly formatted MCP messages', async () => {
      if (!mcpClient) {
        console.warn('MCP client not available, skipping test');
        return;
      }
      
      // This test validates that our client sends properly formatted messages
      // by ensuring successful communication with the server
      
      const response = await mcpClient.client.listTools();
      
      // If we get a response, the message format was correct
      expect(response).toBeDefined();
      expect(response.tools).toBeDefined();
    });

    test('should handle server capabilities correctly', async () => {
      if (!mcpClient) {
        console.warn('MCP client not available, skipping test');
        return;
      }
      
      // Test that the client properly negotiates capabilities with the server
      const response = await mcpClient.client.listTools();
      
      expect(response.tools).toBeDefined();
      expect(Array.isArray(response.tools)).toBe(true);
      
      // The fact that we can list tools means capability negotiation worked
      expect(response.tools.length).toBeGreaterThan(0);
    });
  });
});

/**
 * Additional test suite for read-only mode validation
 */
describe('MCP Protocol Read-Only Mode Tests', () => {
  let readOnlyClient: MCPTestClient;

  beforeAll(async () => {
    // Skip tests if required environment variables are not set
    if (!process.env.SUPABASE_URL && !testConfig.supabaseUrl) {
      console.warn('Skipping MCP Protocol read-only tests: SUPABASE_URL not configured');
      return;
    }
    
    try {
      readOnlyClient = await setupMCPClient({ readOnly: true });
    } catch (error) {
      console.warn('Failed to setup read-only MCP client:', error);
      throw error;
    }
  }, testConfig.testTimeout);

  afterAll(async () => {
    if (readOnlyClient) {
      await cleanupMCPClient(readOnlyClient);
    }
  });

  test('should allow read operations in read-only mode', async () => {
    if (!readOnlyClient) {
      console.warn('Read-only MCP client not available, skipping test');
      return;
    }
    
    const response = await readOnlyClient.client.callTool({
      name: 'list_tables',
      arguments: { schemas: ['public'] }
    });
    
    expect(response.content).toBeDefined();
    expect(Array.isArray(response.content)).toBe(true);
  });
});