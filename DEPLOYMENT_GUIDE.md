# 📖 Supabase MCP Server - Deployment Guide

> **Complete installation and deployment instructions for Windows and Linux environments**

This guide provides step-by-step instructions for deploying the Supabase MCP Server on both Windows and Linux machines, with configurations for popular AI assistants.

## 🎯 Deployment Options

### Option 1: Package Distribution (Recommended)
- ✅ **Fastest deployment** - Single package file
- ✅ **No build required** - Pre-built and tested
- ✅ **Lightweight** - Only production dependencies
- ✅ **Easy updates** - Simple package replacement

### Option 2: Source Code Installation
- ✅ **Full development environment** - Access to source code
- ✅ **Customization** - Modify and extend functionality
- ✅ **Latest features** - Build from latest commits

## 🏗️ Option 1: Package Distribution Deployment

### Prerequisites

**All Platforms:**
- **Node.js 18+** - [Download here](https://nodejs.org/)
- **Active Supabase instance** (local or remote)

**Verification:**
```bash
# Check Node.js version (must be 18.0.0 or higher)
node --version

# Check npm
npm --version
```

### Step 1: Download Package

Download the distribution package:
- `supabase-mcp-server-supabase-0.5.0.tgz` (3.7MB)

### Step 2: Installation

#### Windows Installation

```powershell
# Option A: Global installation (recommended for system-wide access)
npm install -g supabase-mcp-server-supabase-0.5.0.tgz

# Option B: Local directory installation (for isolated deployments)
mkdir C:\mcp-servers\supabase
cd C:\mcp-servers\supabase
tar -xzf supabase-mcp-server-supabase-0.5.0.tgz
cd package
npm install --omit=dev
```

#### Linux Installation

```bash
# Option A: Global installation (recommended)
sudo npm install -g supabase-mcp-server-supabase-0.5.0.tgz

# Option B: Local directory installation
mkdir -p ~/.local/mcp-servers/supabase
cd ~/.local/mcp-servers/supabase
tar -xzf supabase-mcp-server-supabase-0.5.0.tgz
cd package
npm install --production
```

### Step 3: Verify Installation

```bash
# For global installation (clean version check)
mcp-supabase version

# For local installation (use the local binary)
node dist/cli.cjs version

# Alternative (shows initialization logs but works)
mcp-server-supabase --version
```

Expected output: `0.5.0`

**Note:** The `mcp-server-supabase` command is the MCP server itself and shows initialization logs when starting. For clean version checks, use `mcp-supabase version` instead.

## 🔧 MCP Client Configuration

### 🎨 Cursor IDE

#### Windows Configuration

Create `cursor-mcp-config.json` in your project root:

**For Global Installation:**
```json
{
  "mcpServers": {
    "supabase-local": {
      "command": "mcp-server-supabase",
      "args": [
        "--supabase-url=https://your-project.supabase.co",
        "--anon-key=your-anon-key-here",
        "--service-key=your-service-role-key-here"
      ]
    }
  }
}
```

**For Local Installation:**
```json
{
  "mcpServers": {
    "supabase-local": {
      "command": "node",
      "args": ["C:/mcp-servers/supabase/package/dist/transports/stdio.cjs"],
      "env": {
        "SUPABASE_URL": "https://your-project.supabase.co",
        "SUPABASE_ANON_KEY": "your-anon-key-here",
        "SUPABASE_SERVICE_ROLE_KEY": "your-service-role-key-here",
        "READ_ONLY": "false",
        "DEBUG_SQL": "false"
      }
    }
  }
}
```

#### Linux Configuration

```json
{
  "mcpServers": {
    "supabase-local": {
      "command": "node",
      "args": ["/usr/local/lib/node_modules/@supabase/mcp-server-supabase/dist/transports/stdio.cjs"],
      "env": {
        "SUPABASE_URL": "https://your-project.supabase.co",
        "SUPABASE_ANON_KEY": "your-anon-key-here",
        "SUPABASE_SERVICE_ROLE_KEY": "your-service-role-key-here",
        "READ_ONLY": "false",
        "DEBUG_SQL": "false"
      }
    }
  }
}
```

### 🤖 Claude Desktop

#### Windows Configuration

Update `%APPDATA%\Claude\claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "supabase-local": {
      "command": "mcp-server-supabase",
      "args": [
        "--supabase-url=https://your-project.supabase.co",
        "--anon-key=your-anon-key-here",
        "--service-key=your-service-role-key-here"
      ]
    }
  }
}
```

#### Linux Configuration

Update `~/.config/Claude/claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "supabase-local": {
      "command": "mcp-server-supabase",
      "args": [
        "--supabase-url=https://your-project.supabase.co",
        "--anon-key=your-anon-key-here",
        "--service-key=your-service-role-key-here"
      ]
    }
  }
}
```

### 🌀 Windsurf

Similar to Cursor IDE configuration, update your Windsurf MCP configuration file with the appropriate paths.

## 🏗️ Option 2: Source Code Installation

### Prerequisites

**Additional Requirements:**
- **Git** - For cloning the repository
- **Build tools** - npm, TypeScript compiler

### Step 1: Clone Repository

```bash
# Clone the repository
git clone <repository-url>
cd local-supabase-mcp
```

### Step 2: Install Dependencies

```bash
# Install all dependencies
npm install

# Install workspace dependencies
npm run install:all
```

### Step 3: Build Project

```bash
# Build all packages
npm run build

# Verify build
ls packages/mcp-server-supabase/dist/
```

### Step 4: Configure Environment

```bash
# Copy environment template
cp packages/mcp-server-supabase/.env.example packages/mcp-server-supabase/.env

# Edit with your Supabase details
nano packages/mcp-server-supabase/.env
```

### Step 5: Test Installation

```bash
# Test the server
npm run test:connection

# Start development server
npm run dev:local
```

## 🔑 Environment Configuration

### Required Environment Variables

```bash
# Core Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIs...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIs...

# Optional Configuration
READ_ONLY=false                    # Enable read-only mode
DEBUG_SQL=false                    # Enable SQL query logging
```

### Getting Supabase Credentials

#### For Remote Supabase Projects

1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project
3. Navigate to **Settings** > **API**
4. Copy the following:
   - **Project URL** → `SUPABASE_URL`
   - **anon public** key → `SUPABASE_ANON_KEY`
   - **service_role secret** key → `SUPABASE_SERVICE_ROLE_KEY`

#### For Local Supabase Instances

```bash
# Start local Supabase
supabase start

# Get connection details
supabase status

# Copy the values to your environment configuration
```

## 🐳 Docker Deployment

### Docker Compose Setup

Create `docker-compose.yml`:

```yaml
version: '3.8'
services:
  supabase-mcp:
    build: ./packages/mcp-server-supabase
    environment:
      SUPABASE_URL: https://your-project.supabase.co
      SUPABASE_ANON_KEY: your-anon-key-here
      SUPABASE_SERVICE_ROLE_KEY: your-service-role-key-here
      READ_ONLY: false
      DEBUG_SQL: false
    container_name: supabase-mcp-server
    restart: unless-stopped
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge
```

### Running with Docker

```bash
# Build and start
docker-compose up -d

# Check logs
docker-compose logs -f

# Connect from MCP client
docker exec -i supabase-mcp-server node /app/dist/transports/stdio.cjs
```

## ✅ Testing & Verification

### Basic Connection Test

```bash
# Test server startup
echo '{"method":"initialize","params":{"clientInfo":{"name":"test","version":"1.0"},"capabilities":{}}}' | mcp-server-supabase --supabase-url=https://your-project.supabase.co --anon-key=your-anon-key --service-key=your-service-key
```

### MCP Client Integration Test

1. **Restart your AI assistant completely**
2. **Open a new conversation**
3. **Try a simple command**: "List all tables in my database"
4. **Verify tools are loaded**: Should see 13 Supabase tools available

Expected tools:
- `list_tables`, `list_extensions`, `execute_sql`, `apply_migration`, `list_migrations`
- `get_project_url`, `get_anon_key`, `generate_typescript_types`
- `list_edge_functions`, `deploy_edge_function`
- `check_local_connection`, `get_local_config`, `search_docs`

## 🔧 Troubleshooting

### Common Issues

#### "Command not found: mcp-server-supabase"

**Solution:**
```bash
# Check global installation
npm list -g @supabase/mcp-server-supabase

# Reinstall if missing
npm install -g supabase-mcp-server-supabase-0.5.0.tgz

# Check PATH includes npm global bin
npm config get prefix
```

#### "Connection failed" Errors

**Solutions:**
1. **Verify credentials** - Double-check your Supabase URL and keys
2. **Test connectivity** - Ensure you can reach your Supabase instance
3. **Check permissions** - Verify your service role key has database access
4. **Firewall issues** - Ensure ports 443/80 are accessible

#### "ERR_REQUIRE_ESM" Error when running CLI

**Error message:**
```
Error [ERR_REQUIRE_ESM]: require() of ES Module ... not supported.
```

**Solution:**
This is a CommonJS/ES Module compatibility issue. The fix is included in version 0.5.0. Make sure you're using the latest package:

```bash
# Reinstall with the fixed version
npm uninstall -g @supabase/mcp-server-supabase
npm install -g supabase-mcp-server-supabase-0.5.0.tgz

# Verify the fix works
mcp-supabase version
```

If you're still getting this error, ensure you're using the correct command:
- ✅ Use: `mcp-supabase version` (clean output)
- ❌ Avoid: `mcp-server-supabase --version` (shows initialization logs)

#### "0 tools enabled" in AI Assistant

**Solutions:**
1. **Use correct file extension** - Use `.cjs` files, not `.js`
2. **Absolute paths** - Use full paths in configuration
3. **Restart completely** - Close and restart your AI assistant
4. **Check environment variables** - Ensure all variables are properly set

### Debug Mode

Enable detailed logging:

```bash
# Set debug environment
export DEBUG_SQL=true

# Run with verbose output
mcp-server-supabase --supabase-url=... --anon-key=... --service-key=...
```

### Advanced Troubleshooting

#### Windows-Specific Issues

```powershell
# Check Node.js in PATH
Get-Command node

# Check npm global prefix
npm config get prefix

# Add to PATH if needed
setx PATH "%PATH%;C:\Users\<USER>\AppData\Roaming\npm"
```

#### Linux-Specific Issues

```bash
# Check permissions
ls -la /usr/local/lib/node_modules/@supabase/

# Fix permissions if needed
sudo chown -R $(whoami) /usr/local/lib/node_modules/

# Check symbolic links
ls -la /usr/local/bin/mcp-server-supabase
```

## 🔒 Security Best Practices

### Production Deployment

1. **Use Environment Variables** - Never hardcode credentials in configuration files
2. **Enable Read-Only Mode** - For production clients: `READ_ONLY=true`
3. **Restrict Network Access** - Use firewall rules to limit database access
4. **Regular Key Rotation** - Update Supabase keys regularly
5. **Monitor Access** - Enable logging and monitor database queries

### Environment Security

```bash
# Use .env files for local development
echo "SUPABASE_URL=..." > .env
echo "SUPABASE_ANON_KEY=..." >> .env
echo "SUPABASE_SERVICE_ROLE_KEY=..." >> .env

# Add .env to .gitignore
echo ".env" >> .gitignore
```

## 📚 Additional Resources

- [Main README](README.md) - Project overview and features
- [Supabase Documentation](https://supabase.com/docs) - Supabase platform docs
- [MCP Specification](https://modelcontextprotocol.io/) - Model Context Protocol details
- [Node.js Installation](https://nodejs.org/en/download/) - Official Node.js downloads

## 🆘 Getting Help

### Support Channels

- 🐛 **Bug Reports** - [Create an issue](https://github.com/your-repo/issues)
- 💬 **Community Support** - [Join discussions](https://github.com/your-repo/discussions)
- 📖 **Documentation** - [View docs](https://github.com/your-repo/wiki)

### Before Asking for Help

1. ✅ **Check this deployment guide** - Follow all steps carefully
2. ✅ **Verify prerequisites** - Ensure Node.js 18+ is installed
3. ✅ **Test basic connectivity** - Can you connect to your Supabase instance?
4. ✅ **Check logs** - Look for error messages in debug mode
5. ✅ **Try the examples** - Use exact configuration examples provided

---

**🎉 Congratulations! Your Supabase MCP Server is now ready for production use.** 