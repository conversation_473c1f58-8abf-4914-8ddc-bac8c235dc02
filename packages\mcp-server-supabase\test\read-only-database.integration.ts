import { describe, test, expect, beforeAll } from 'vitest';
import {
  createTestClient,
  createServiceTestClient,
  createMemoryMasterTestClient,
  validateTestEnvironment,
  testDatabaseConnection,
  getTestDatabaseStats,
  testConfig
} from './test-config';

/**
 * Read-Only Database Integration Tests
 * 
 * These tests validate database connectivity and data integrity
 * without performing any write operations (INSERT, UPDATE, DELETE).
 * All tests are designed to be safe for production-like environments.
 */

describe('Read-Only Database Integration Tests', () => {
  beforeAll(async () => {
    // Validate test environment before running tests
    const validation = validateTestEnvironment();
    if (!validation.valid) {
      throw new Error(`Test environment validation failed: ${validation.errors.join(', ')}`);
    }
  });

  describe('Database Connectivity', () => {
    test('can connect with anonymous key to public schema', async () => {
      const client = createTestClient();
      
      // Test basic query on public schema
      const { data, error } = await client
        .from('token_store')
        .select('id')
        .limit(1);
      
      expect(error).toBeNull();
      expect(data).toBeDefined();
      expect(Array.isArray(data)).toBe(true);
    }, testConfig.testTimeout);

    test('can connect with service role to public schema', async () => {
      const client = createServiceTestClient();
      
      // Test service role access to public schema
      const { data, error } = await client
        .from('token_store')
        .select('*')
        .limit(5);
      
      expect(error).toBeNull();
      expect(data).toBeDefined();
      expect(Array.isArray(data)).toBe(true);
    }, testConfig.testTimeout);

    test('can connect to memory_master schema with service role', async () => {
      const client = createMemoryMasterTestClient();
      
      // Test connection to memory_master schema
      const { data, error } = await client
        .from('users')
        .select('id')
        .limit(1);
      
      // Note: This may fail due to permissions, which is expected behavior
      if (error) {
        expect(error.message).toContain('permission denied');
      } else {
        expect(data).toBeDefined();
        expect(Array.isArray(data)).toBe(true);
      }
    }, testConfig.testTimeout);
  });

  describe('Schema Validation', () => {
    test('can query for table existence', async () => {
      const client = createServiceTestClient();
      
      // Test that we can query the token_store table exists
      const { data, error } = await client
        .from('token_store')
        .select('*', { count: 'exact', head: true });
      
      expect(error).toBeNull();
      expect(data).toBeNull(); // head: true returns null data
      expect(typeof client).toBe('object');
    }, testConfig.testTimeout);

    test('can validate table structure', async () => {
      const client = createServiceTestClient();
      
      // Test that we can select specific columns from token_store
      const { data, error } = await client
        .from('token_store')
        .select('id, token, type')
        .limit(1);
      
      expect(error).toBeNull();
      expect(data).toBeDefined();
      expect(Array.isArray(data)).toBe(true);
      
      if (data && data.length > 0) {
        const record = data[0];
        expect(record.id).toBeDefined();
        expect(typeof record.id).toBe('number');
      }
    }, testConfig.testTimeout);
  });

  describe('Data Integrity Validation', () => {
    test('can validate data types in token_store table', async () => {
      const client = createServiceTestClient();
      
      const { data, error } = await client
        .from('token_store')
        .select('*')
        .limit(10);
      
      expect(error).toBeNull();
      expect(data).toBeDefined();
      expect(Array.isArray(data)).toBe(true);
      
      if (data && data.length > 0) {
        data.forEach(record => {
          // Validate basic data structure
          expect(record.id).toBeDefined();
          expect(typeof record.id).toBe('number');
          
          if (record.token) {
            expect(typeof record.token).toBe('string');
            expect(record.token.length).toBeGreaterThan(0);
          }
          
          if (record.type) {
            expect(typeof record.type).toBe('string');
          }
        });
      }
    }, testConfig.testTimeout);

    test('can validate date fields are properly formatted', async () => {
      const client = createServiceTestClient();
      
      const { data, error } = await client
        .from('token_store')
        .select('updated_at, expires_on')
        .not('updated_at', 'is', null)
        .limit(5);
      
      expect(error).toBeNull();
      expect(data).toBeDefined();
      
      if (data && data.length > 0) {
        data.forEach(record => {
          if (record.updated_at) {
            // Validate date format
            const date = new Date(record.updated_at);
            expect(date).toBeInstanceOf(Date);
            expect(isNaN(date.getTime())).toBe(false);
          }
          
          if (record.expires_on) {
            // Validate date string format
            expect(typeof record.expires_on).toBe('string');
            const datePattern = /^\d{4}-\d{2}-\d{2}$/;
            expect(datePattern.test(record.expires_on)).toBe(true);
          }
        });
      }
    }, testConfig.testTimeout);
  });

  describe('Query Performance', () => {
    test('simple queries complete within acceptable time', async () => {
      const client = createServiceTestClient();
      
      const startTime = Date.now();
      const { data, error } = await client
        .from('token_store')
        .select('id, type')
        .limit(100);
      const queryTime = Date.now() - startTime;
      
      expect(error).toBeNull();
      expect(data).toBeDefined();
      expect(queryTime).toBeLessThan(testConfig.performanceThreshold);
    }, testConfig.testTimeout);

    test('filtered queries perform efficiently', async () => {
      const client = createServiceTestClient();
      
      const startTime = Date.now();
      const { data, error } = await client
        .from('token_store')
        .select('*')
        .eq('type', 'refresh')
        .limit(50);
      const queryTime = Date.now() - startTime;
      
      expect(error).toBeNull();
      expect(data).toBeDefined();
      expect(queryTime).toBeLessThan(testConfig.performanceThreshold);
    }, testConfig.testTimeout);

    test('count queries execute efficiently', async () => {
      const client = createServiceTestClient();
      
      const startTime = Date.now();
      const { count, error } = await client
        .from('token_store')
        .select('*', { count: 'exact', head: true });
      const queryTime = Date.now() - startTime;
      
      expect(error).toBeNull();
      expect(count).toBeDefined();
      expect(typeof count).toBe('number');
      expect(count).toBeGreaterThanOrEqual(0);
      expect(queryTime).toBeLessThan(testConfig.performanceThreshold);
    }, testConfig.testTimeout);
  });

  describe('Database Statistics', () => {
    test('can retrieve basic database information', async () => {
      const client = createServiceTestClient();
      
      // Get count of token_store records
      const { count, error } = await client
        .from('token_store')
        .select('*', { count: 'exact', head: true });
      
      expect(error).toBeNull();
      expect(count).toBeDefined();
      expect(typeof count).toBe('number');
      expect(count).toBeGreaterThanOrEqual(0);
    }, testConfig.testTimeout);

    test('database contains expected minimum data', async () => {
      const client = createServiceTestClient();
      
      const { count, error } = await client
        .from('token_store')
        .select('*', { count: 'exact', head: true });
      
      expect(error).toBeNull();
      expect(count).toBeDefined();
      expect(count).toBeGreaterThanOrEqual(0);
    }, testConfig.testTimeout);
  });

  describe('Error Handling', () => {
    test('handles invalid table names gracefully', async () => {
      const client = createServiceTestClient();
      
      const { data, error } = await client
        .from('non_existent_table')
        .select('*')
        .limit(1);
      
      expect(data).toBeNull();
      expect(error).toBeDefined();
      expect(error?.message).toMatch(/does not exist|not found/i);
    }, testConfig.testTimeout);

    test('handles invalid column names gracefully', async () => {
      const client = createServiceTestClient();
      
      const { data, error } = await client
        .from('token_store')
        .select('non_existent_column')
        .limit(1);
      
      expect(data).toBeNull();
      expect(error).toBeDefined();
      expect(error?.message).toMatch(/column.*does not exist|not found/i);
    }, testConfig.testTimeout);

    test('handles malformed queries gracefully', async () => {
      const client = createServiceTestClient();
      
      const { data, error } = await client
        .from('token_store')
        .select('*')
        .eq('id', 'not_a_number'); // Invalid type for numeric field
      
      // This should either return no results or handle the type mismatch gracefully
      if (error) {
        expect(error.message).toBeDefined();
      } else {
        expect(data).toBeDefined();
        expect(Array.isArray(data)).toBe(true);
      }
    }, testConfig.testTimeout);
  });
});