import { ConfigManager, type ConfigProfile, type ConfigValue } from './config-manager.js';
import { ConfigFactory } from './config-factory.js';
import { contextLogger } from '../utils/logger.js';

/**
 * Configuration comparison result
 */
export interface ConfigComparison {
  added: string[];
  removed: string[];
  changed: Array<{
    key: string;
    oldValue: unknown;
    newValue: unknown;
    oldSource: string;
    newSource: string;
  }>;
  unchanged: string[];
}

/**
 * Configuration backup
 */
export interface ConfigBackup {
  timestamp: Date;
  profile: ConfigProfile;
  config: Record<string, ConfigValue>;
  metadata: {
    totalKeys: number;
    sources: Record<string, number>;
    version: string;
  };
}

/**
 * Configuration utilities for advanced operations
 */
export class ConfigUtils {
  private static logger = contextLogger.child({ component: 'ConfigUtils' });

  /**
   * Compare two configuration instances
   */
  static compare(
    config1: ConfigManager,
    config2: ConfigManager
  ): ConfigComparison {
    const keys1 = new Set(config1.keys());
    const keys2 = new Set(config2.keys());
    
    const added = Array.from(keys2).filter(key => !keys1.has(key));
    const removed = Array.from(keys1).filter(key => !keys2.has(key));
    const common = Array.from(keys1).filter(key => keys2.has(key));
    
    const changed: ConfigComparison['changed'] = [];
    const unchanged: string[] = [];
    
    for (const key of common) {
      const value1 = config1.get(key);
      const value2 = config2.get(key);
      const meta1 = config1.getWithMetadata(key);
      const meta2 = config2.getWithMetadata(key);
      
      if (JSON.stringify(value1) !== JSON.stringify(value2)) {
        changed.push({
          key,
          oldValue: value1,
          newValue: value2,
          oldSource: meta1?.source || 'unknown',
          newSource: meta2?.source || 'unknown',
        });
      } else {
        unchanged.push(key);
      }
    }
    
    return { added, removed, changed, unchanged };
  }

  /**
   * Create configuration backup
   */
  static createBackup(config: ConfigManager): ConfigBackup {
    const keys = config.keys();
    const configData: Record<string, ConfigValue> = {};
    
    for (const key of keys) {
      const metadata = config.getWithMetadata(key);
      if (metadata) {
        configData[key] = metadata;
      }
    }
    
    return {
      timestamp: new Date(),
      profile: (config as any).profile,
      config: configData,
      metadata: {
        totalKeys: keys.length,
        sources: config.getConfigSources(),
        version: '1.0.0',
      },
    };
  }

  /**
   * Restore configuration from backup
   */
  static async restoreFromBackup(
    backup: ConfigBackup,
    instanceName: string = 'restored'
  ): Promise<ConfigManager> {
    const config = new ConfigManager(backup.profile);
    
    for (const [key, configValue] of Object.entries(backup.config)) {
      config.set(key, configValue.value, configValue.source);
    }
    
    // Register with factory
    (ConfigFactory as any).instances.set(instanceName, config);
    
    this.logger.info('Configuration restored from backup', {
      instanceName,
      profile: backup.profile,
      keys: backup.metadata.totalKeys,
      backupTimestamp: backup.timestamp,
    });
    
    return config;
  }

  /**
   * Merge multiple configuration instances
   */
  static async merge(
    configs: ConfigManager[],
    targetProfile: ConfigProfile = 'development'
  ): Promise<ConfigManager> {
    if (configs.length === 0) {
      throw new Error('At least one configuration instance is required for merging');
    }
    
    const merged = new ConfigManager(targetProfile);
    
    // Merge configurations in order (later configs override earlier ones)
    for (const config of configs) {
      for (const key of config.keys()) {
        const metadata = config.getWithMetadata(key);
        if (metadata) {
          merged.set(key, metadata.value, metadata.source);
        }
      }
    }
    
    this.logger.info('Configuration instances merged', {
      sourceCount: configs.length,
      targetProfile,
      totalKeys: merged.keys().length,
    });
    
    return merged;
  }

  /**
   * Filter configuration by criteria
   */
  static filter(
    config: ConfigManager,
    criteria: {
      sources?: string[];
      sensitive?: boolean;
      pattern?: RegExp;
      profiles?: ConfigProfile[];
    }
  ): Record<string, ConfigValue> {
    const result: Record<string, ConfigValue> = {};
    
    for (const key of config.keys()) {
      const metadata = config.getWithMetadata(key);
      if (!metadata) continue;
      
      // Filter by source
      if (criteria.sources && !criteria.sources.includes(metadata.source)) {
        continue;
      }
      
      // Filter by sensitive flag
      if (criteria.sensitive !== undefined && metadata.sensitive !== criteria.sensitive) {
        continue;
      }
      
      // Filter by key pattern
      if (criteria.pattern && !criteria.pattern.test(key)) {
        continue;
      }
      
      // Filter by profile
      if (criteria.profiles && !criteria.profiles.includes(metadata.profile)) {
        continue;
      }
      
      result[key] = metadata;
    }
    
    return result;
  }

  /**
   * Generate configuration documentation
   */
  static generateDocs(config: ConfigManager): {
    profile: ConfigProfile;
    sections: Array<{
      name: string;
      description: string;
      keys: Array<{
        key: string;
        value: unknown;
        source: string;
        sensitive: boolean;
        description?: string;
        type: string;
      }>;
    }>;
    summary: {
      totalKeys: number;
      sources: Record<string, number>;
      sensitiveKeys: number;
    };
  } {
    const keys = config.keys();
    const sections = new Map<string, any[]>();
    let sensitiveCount = 0;
    
    // Group keys by category
    for (const key of keys) {
      const metadata = config.getWithMetadata(key);
      if (!metadata) continue;
      
      if (metadata.sensitive) sensitiveCount++;
      
      // Determine section based on key prefix
      let section = 'General';
      if (key.startsWith('SUPABASE_')) section = 'Supabase';
      else if (key.startsWith('DATABASE_')) section = 'Database';
      else if (key.startsWith('LOG_')) section = 'Logging';
      else if (key.startsWith('RATE_LIMIT_')) section = 'Rate Limiting';
      else if (key.startsWith('ENABLE_')) section = 'Feature Flags';
      
      if (!sections.has(section)) {
        sections.set(section, []);
      }
      
      sections.get(section)!.push({
        key,
        value: metadata.sensitive ? '[REDACTED]' : metadata.value,
        source: metadata.source,
        sensitive: metadata.sensitive,
        description: metadata.description,
        type: typeof metadata.value,
      });
    }
    
    // Convert to array format
    const sectionsArray = Array.from(sections.entries()).map(([name, keys]) => ({
      name,
      description: this.getSectionDescription(name),
      keys: keys.sort((a, b) => a.key.localeCompare(b.key)),
    }));
    
    return {
      profile: (config as any).profile,
      sections: sectionsArray,
      summary: {
        totalKeys: keys.length,
        sources: config.getConfigSources(),
        sensitiveKeys: sensitiveCount,
      },
    };
  }

  /**
   * Validate configuration against schema
   */
  static async validateWithSchema(
    config: ConfigManager,
    schema: any
  ): Promise<{
    valid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const result = {
      valid: true,
      errors: [] as string[],
      warnings: [] as string[],
    };
    
    try {
      const configObject = config.toObject(true);
      schema.parse(configObject);
    } catch (error: any) {
      result.valid = false;
      if (error.errors) {
        result.errors = error.errors.map((err: any) => 
          `${err.path.join('.')}: ${err.message}`
        );
      } else {
        result.errors.push(error.message || 'Unknown validation error');
      }
    }
    
    return result;
  }

  /**
   * Export configuration to different formats
   */
  static export(
    config: ConfigManager,
    format: 'json' | 'env' | 'yaml' | 'toml',
    includeSensitive: boolean = false
  ): string {
    const configObject = config.toObject(includeSensitive);
    
    switch (format) {
      case 'json':
        return JSON.stringify(configObject, null, 2);
        
      case 'env':
        return Object.entries(configObject)
          .map(([key, value]) => `${key}=${this.escapeEnvValue(value)}`)
          .join('\n');
          
      case 'yaml':
        // Simple YAML export (would need yaml library for full support)
        return Object.entries(configObject)
          .map(([key, value]) => `${key}: ${JSON.stringify(value)}`)
          .join('\n');
          
      case 'toml':
        // Simple TOML export (would need toml library for full support)
        return Object.entries(configObject)
          .map(([key, value]) => `${key} = ${JSON.stringify(value)}`)
          .join('\n');
          
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  }

  /**
   * Import configuration from string
   */
  static import(
    content: string,
    format: 'json' | 'env',
    targetConfig: ConfigManager
  ): void {
    let configObject: Record<string, unknown>;
    
    switch (format) {
      case 'json':
        configObject = JSON.parse(content);
        break;
        
      case 'env':
        configObject = this.parseEnvContent(content);
        break;
        
      default:
        throw new Error(`Unsupported import format: ${format}`);
    }
    
    for (const [key, value] of Object.entries(configObject)) {
      targetConfig.set(key, value, 'file');
    }
    
    this.logger.info('Configuration imported', {
      format,
      keys: Object.keys(configObject).length,
    });
  }

  /**
   * Get section description for documentation
   */
  private static getSectionDescription(section: string): string {
    const descriptions: Record<string, string> = {
      'Supabase': 'Supabase connection and authentication settings',
      'Database': 'Database connection and query configuration',
      'Logging': 'Application logging and debugging settings',
      'Rate Limiting': 'API rate limiting and throttling configuration',
      'Feature Flags': 'Feature toggles and experimental functionality',
      'General': 'General application configuration',
    };
    
    return descriptions[section] || 'Configuration settings';
  }

  /**
   * Escape environment variable value
   */
  private static escapeEnvValue(value: unknown): string {
    const str = String(value);
    if (str.includes(' ') || str.includes('\n') || str.includes('"')) {
      return `"${str.replace(/"/g, '\\"')}"`;
    }
    return str;
  }

  /**
   * Parse environment file content
   */
  private static parseEnvContent(content: string): Record<string, string> {
    const result: Record<string, string> = {};
    const lines = content.split('\n');
    
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#')) {
        const [key, ...valueParts] = trimmed.split('=');
        if (key && valueParts.length > 0) {
          const value = valueParts.join('=').replace(/^["']|["']$/g, '');
          result[key.trim()] = value;
        }
      }
    }
    
    return result;
  }
}

/**
 * Configuration decorator for dependency injection
 */
export function Config(key: string, defaultValue?: unknown) {
  return function (target: any, propertyKey: string) {
    Object.defineProperty(target, propertyKey, {
      get() {
        const config = ConfigFactory.get();
        return config ? config.get(key, defaultValue) : defaultValue;
      },
      enumerable: true,
      configurable: true,
    });
  };
}

/**
 * Configuration watcher utility
 */
export class ConfigWatcher {
  private watchers = new Map<string, () => void>();
  private logger = contextLogger.child({ component: 'ConfigWatcher' });

  /**
   * Watch for configuration changes
   */
  watch(
    config: ConfigManager,
    keys: string | string[],
    callback: (key: string, oldValue: unknown, newValue: unknown) => void
  ): () => void {
    const watchKeys = Array.isArray(keys) ? keys : [keys];
    const watcherId = `${Date.now()}-${Math.random()}`;
    
    const unsubscribe = config.onChange((event) => {
      if (watchKeys.includes(event.key)) {
        callback(event.key, event.oldValue, event.newValue);
      }
    });
    
    this.watchers.set(watcherId, unsubscribe);
    
    this.logger.debug('Configuration watcher added', {
      watcherId,
      keys: watchKeys,
    });
    
    return () => {
      const unwatcher = this.watchers.get(watcherId);
      if (unwatcher) {
        unwatcher();
        this.watchers.delete(watcherId);
        this.logger.debug('Configuration watcher removed', { watcherId });
      }
    };
  }

  /**
   * Remove all watchers
   */
  removeAll(): void {
    for (const [watcherId, unwatcher] of this.watchers.entries()) {
      unwatcher();
      this.watchers.delete(watcherId);
    }
    this.logger.info('All configuration watchers removed');
  }
}
