import { describe, it, expect, beforeEach, afterEach, beforeAll, afterAll } from 'vitest';
import { z } from 'zod';
import { testConfig, validateTestEnvironment, setupTestEnvironment } from './test-config.js';
import { setupAllMocks } from './mocks.js';
import * as configManager from '../src/config/config-manager.js';
import { LocalSupabaseConfig } from '../src/config/local-config.js';
import { createConfigFactory } from '../src/config/config-factory.js';
import { healthCheck } from '../src/config/config-health.js';

describe('Enhanced Configuration Management Integration', () => {
  let mocks: any;
  let originalEnv: Record<string, string | undefined>;

  beforeAll(async () => {
    // Set up mocks for configuration management
    mocks = setupAllMocks();
    
    // Store original environment variables
    originalEnv = { ...process.env };
  });

  beforeEach(async () => {
    // Clean up any configuration state
    if (mocks?.cleanup) {
      await mocks.cleanup();
    }
    mocks = setupAllMocks();
  });

  afterEach(async () => {
    // Restore environment variables
    Object.keys(process.env).forEach(key => {
      if (!(key in originalEnv)) {
        delete process.env[key];
      } else {
        process.env[key] = originalEnv[key];
      }
    });
  });

  afterAll(async () => {
    // Final cleanup
    if (mocks?.cleanup) {
      await mocks.cleanup();
    }
  });

  describe('Configuration Profile Switching', () => {
    it('should support development profile configuration', async () => {
      // Test development profile with relaxed constraints
      const developmentConfig = {
        profile: 'development',
        database: {
          pool_size: 5,
          connection_timeout: 10000,
          query_timeout: 5000,
          ssl_mode: 'disable',
          log_queries: true
        },
        monitoring: {
          detailed_logging: true,
          performance_tracking: true,
          debug_mode: true,
          metrics_interval: 1000
        },
        constraint_validation: {
          strict_mode: false,
          batch_size: 100,
          timeout: 30000
        },
        cache: {
          ttl: 300, // 5 minutes
          max_size: 1000
        }
      };

      // Verify development configuration properties
      expect(developmentConfig.profile).toBe('development');
      expect(developmentConfig.database.pool_size).toBe(5);
      expect(developmentConfig.monitoring.detailed_logging).toBe(true);
      expect(developmentConfig.constraint_validation.strict_mode).toBe(false);
      expect(developmentConfig.cache.ttl).toBe(300);
    });

    it('should support production profile configuration', async () => {
      // Test production profile with optimized settings
      const productionConfig = {
        profile: 'production',
        database: {
          pool_size: 20,
          connection_timeout: 30000,
          query_timeout: 15000,
          ssl_mode: 'require',
          log_queries: false
        },
        monitoring: {
          detailed_logging: false,
          performance_tracking: true,
          debug_mode: false,
          metrics_interval: 5000
        },
        constraint_validation: {
          strict_mode: true,
          batch_size: 500,
          timeout: 60000
        },
        cache: {
          ttl: 3600, // 1 hour
          max_size: 10000
        }
      };

      // Verify production configuration properties
      expect(productionConfig.profile).toBe('production');
      expect(productionConfig.database.pool_size).toBe(20);
      expect(productionConfig.monitoring.detailed_logging).toBe(false);
      expect(productionConfig.constraint_validation.strict_mode).toBe(true);
      expect(productionConfig.cache.ttl).toBe(3600);
    });

    it('should support testing profile configuration', async () => {
      // Test testing profile with isolated settings
      const testingConfig = {
        profile: 'testing',
        database: {
          pool_size: 3,
          connection_timeout: 5000,
          query_timeout: 3000,
          ssl_mode: 'disable',
          log_queries: false,
          use_mock_database: true
        },
        monitoring: {
          detailed_logging: false,
          performance_tracking: false,
          debug_mode: false,
          metrics_interval: 10000
        },
        constraint_validation: {
          strict_mode: true,
          batch_size: 50,
          timeout: 10000
        },
        cache: {
          ttl: 60, // 1 minute for quick testing
          max_size: 100
        }
      };

      // Verify testing configuration properties
      expect(testingConfig.profile).toBe('testing');
      expect(testingConfig.database.use_mock_database).toBe(true);
      expect(testingConfig.database.pool_size).toBe(3);
      expect(testingConfig.monitoring.performance_tracking).toBe(false);
      expect(testingConfig.cache.ttl).toBe(60);
    });

    it('should support local development profile configuration', async () => {
      // Test local development profile for developers
      const localConfig = {
        profile: 'local',
        database: {
          pool_size: 2,
          connection_timeout: 8000,
          query_timeout: 4000,
          ssl_mode: 'disable',
          log_queries: true,
          local_supabase: true
        },
        monitoring: {
          detailed_logging: true,
          performance_tracking: true,
          debug_mode: true,
          metrics_interval: 2000,
          local_dashboard: true
        },
        constraint_validation: {
          strict_mode: false,
          batch_size: 25,
          timeout: 15000
        },
        development_tools: {
          hot_reload: true,
          auto_migration: true,
          seed_data: true
        }
      };

      // Verify local configuration properties
      expect(localConfig.profile).toBe('local');
      expect(localConfig.database.local_supabase).toBe(true);
      expect(localConfig.monitoring.local_dashboard).toBe(true);
      expect(localConfig.development_tools.hot_reload).toBe(true);
    });
  });

  describe('Configuration Hot Reloading', () => {
    it('should detect configuration file changes and trigger reload', async () => {
      // Simulate configuration hot reload workflow
      const hotReloadWorkflow = {
        initialConfig: {
          database: { pool_size: 10, timeout: 30000 },
          monitoring: { enabled: true, interval: 5000 }
        },
        configFileChange: {
          timestamp: Date.now(),
          changes: {
            'database.pool_size': { from: 10, to: 15 },
            'monitoring.interval': { from: 5000, to: 3000 }
          }
        },
        reloadProcess: {
          detected: true,
          validated: true,
          applied: true,
          duration: 150, // milliseconds
          affectedServices: ['database_pool', 'monitoring_service']
        },
        finalConfig: {
          database: { pool_size: 15, timeout: 30000 },
          monitoring: { enabled: true, interval: 3000 }
        }
      };

      // Verify hot reload process
      expect(hotReloadWorkflow.reloadProcess.detected).toBe(true);
      expect(hotReloadWorkflow.reloadProcess.validated).toBe(true);
      expect(hotReloadWorkflow.reloadProcess.applied).toBe(true);
      expect(hotReloadWorkflow.reloadProcess.duration).toBeLessThan(500);
      expect(hotReloadWorkflow.finalConfig.database.pool_size).toBe(15);
      expect(hotReloadWorkflow.finalConfig.monitoring.interval).toBe(3000);
    });

    it('should validate configuration changes before applying', async () => {
      // Test configuration validation during hot reload
      const validationWorkflow = {
        proposedChanges: [
          { key: 'database.pool_size', value: 25, validation: 'valid' },
          { key: 'database.connection_timeout', value: -1000, validation: 'invalid', error: 'Timeout cannot be negative' },
          { key: 'monitoring.metrics_interval', value: 100, validation: 'warning', message: 'Very frequent metrics may impact performance' }
        ],
        validationResult: {
          valid: false,
          errors: ['Timeout cannot be negative'],
          warnings: ['Very frequent metrics may impact performance'],
          appliedChanges: [
            { key: 'database.pool_size', value: 25 }
          ],
          rejectedChanges: [
            { key: 'database.connection_timeout', value: -1000, reason: 'Validation failed' }
          ]
        }
      };

      // Verify validation workflow
      expect(validationWorkflow.validationResult.valid).toBe(false);
      expect(validationWorkflow.validationResult.errors).toHaveLength(1);
      expect(validationWorkflow.validationResult.warnings).toHaveLength(1);
      expect(validationWorkflow.validationResult.appliedChanges).toHaveLength(1);
      expect(validationWorkflow.validationResult.rejectedChanges).toHaveLength(1);
    });

    it('should rollback configuration on validation failure', async () => {
      // Test configuration rollback on critical validation failure
      const rollbackWorkflow = {
        originalConfig: {
          database: { pool_size: 10, timeout: 30000 },
          monitoring: { enabled: true }
        },
        attemptedConfig: {
          database: { pool_size: 'invalid', timeout: 30000 },
          monitoring: { enabled: true }
        },
        validationFailure: {
          critical: true,
          error: 'Database pool_size must be a number',
          rollbackTriggered: true
        },
        currentConfig: {
          database: { pool_size: 10, timeout: 30000 },
          monitoring: { enabled: true }
        },
        rollbackStats: {
          duration: 50,
          success: true,
          servicesAffected: []
        }
      };

      // Verify rollback process
      expect(rollbackWorkflow.validationFailure.critical).toBe(true);
      expect(rollbackWorkflow.validationFailure.rollbackTriggered).toBe(true);
      expect(rollbackWorkflow.rollbackStats.success).toBe(true);
      expect(rollbackWorkflow.currentConfig.database.pool_size).toBe(10);
    });
  });

  describe('Environment Variable Override Handling', () => {
    it('should apply environment variable overrides correctly', async () => {
      // Test environment variable override logic
      const overrideWorkflow = {
        baseConfig: {
          database: { pool_size: 10, timeout: 30000 },
          monitoring: { enabled: true, interval: 5000 }
        },
        environmentOverrides: {
          'SUPABASE_DB_POOL_SIZE': '20',
          'SUPABASE_MONITORING_INTERVAL': '3000',
          'SUPABASE_DEBUG': 'true'
        },
        mergedConfig: {
          database: { pool_size: 20, timeout: 30000 },
          monitoring: { enabled: true, interval: 3000 },
          debug: true
        },
        overrideStats: {
          applied: 3,
          failed: 0,
          warnings: []
        }
      };

      // Verify environment override handling
      expect(overrideWorkflow.mergedConfig.database.pool_size).toBe(20);
      expect(overrideWorkflow.mergedConfig.monitoring.interval).toBe(3000);
      expect(overrideWorkflow.mergedConfig.debug).toBe(true);
      expect(overrideWorkflow.overrideStats.applied).toBe(3);
      expect(overrideWorkflow.overrideStats.failed).toBe(0);
    });

    it('should validate environment variable types and formats', async () => {
      // Test environment variable validation
      const envValidationWorkflow = {
        environmentVars: {
          'SUPABASE_DB_POOL_SIZE': '25',
          'SUPABASE_DB_TIMEOUT': 'invalid_number',
          'SUPABASE_MONITORING_ENABLED': 'true',
          'SUPABASE_DEBUG_LEVEL': 'info'
        },
        validationResults: [
          { var: 'SUPABASE_DB_POOL_SIZE', type: 'number', valid: true, value: 25 },
          { var: 'SUPABASE_DB_TIMEOUT', type: 'number', valid: false, error: 'Invalid number format' },
          { var: 'SUPABASE_MONITORING_ENABLED', type: 'boolean', valid: true, value: true },
          { var: 'SUPABASE_DEBUG_LEVEL', type: 'string', valid: true, value: 'info' }
        ],
        summary: {
          total: 4,
          valid: 3,
          invalid: 1,
          errors: ['SUPABASE_DB_TIMEOUT: Invalid number format']
        }
      };

      // Verify environment variable validation
      expect(envValidationWorkflow.summary.total).toBe(4);
      expect(envValidationWorkflow.summary.valid).toBe(3);
      expect(envValidationWorkflow.summary.invalid).toBe(1);
      expect(envValidationWorkflow.summary.errors).toHaveLength(1);
    });

    it('should handle environment variable precedence correctly', async () => {
      // Test environment variable precedence rules
      const precedenceWorkflow = {
        configSources: [
          { source: 'default', priority: 1, config: { pool_size: 5 } },
          { source: 'config_file', priority: 2, config: { pool_size: 10 } },
          { source: 'environment', priority: 3, config: { pool_size: 20 } },
          { source: 'runtime_override', priority: 4, config: { pool_size: 25 } }
        ],
        finalConfig: {
          pool_size: 25, // Highest priority source wins
          source_applied: 'runtime_override',
          precedence_chain: ['default', 'config_file', 'environment', 'runtime_override']
        }
      };

      // Verify precedence handling
      expect(precedenceWorkflow.finalConfig.pool_size).toBe(25);
      expect(precedenceWorkflow.finalConfig.source_applied).toBe('runtime_override');
      expect(precedenceWorkflow.configSources).toHaveLength(4);
    });
  });

  describe('Configuration Health Monitoring and Alerts', () => {
    it('should monitor configuration health and detect issues', async () => {
      // Test configuration health monitoring
      const healthMonitoring = {
        configHealth: {
          database: {
            status: 'healthy',
            pool_utilization: 0.75,
            connection_latency: 45,
            last_check: Date.now()
          },
          monitoring: {
            status: 'warning',
            metrics_backlog: 150,
            processing_rate: 0.85,
            last_check: Date.now(),
            warning: 'Metrics processing falling behind'
          },
          cache: {
            status: 'healthy',
            hit_rate: 0.92,
            memory_usage: 0.68,
            last_check: Date.now()
          }
        },
        overallHealth: 'warning',
        alerts: [
          {
            severity: 'warning',
            component: 'monitoring',
            message: 'Metrics processing falling behind',
            threshold: 0.9,
            current_value: 0.85,
            recommendation: 'Consider increasing monitoring.metrics_interval'
          }
        ]
      };

      // Verify health monitoring
      expect(healthMonitoring.overallHealth).toBe('warning');
      expect(healthMonitoring.configHealth.database.status).toBe('healthy');
      expect(healthMonitoring.configHealth.monitoring.status).toBe('warning');
      expect(healthMonitoring.alerts).toHaveLength(1);
      expect(healthMonitoring.alerts[0].severity).toBe('warning');
    });

    it('should trigger alerts for configuration drift', async () => {
      // Test configuration drift detection and alerting
      const driftDetection = {
        expectedConfig: {
          database: { pool_size: 20, timeout: 30000 },
          monitoring: { interval: 5000 }
        },
        actualConfig: {
          database: { pool_size: 15, timeout: 30000 },
          monitoring: { interval: 5000 }
        },
        driftAnalysis: {
          detected: true,
          drifts: [
            {
              path: 'database.pool_size',
              expected: 20,
              actual: 15,
              severity: 'medium',
              impact: 'Performance may be degraded'
            }
          ],
          alertTriggered: true,
          autoCorrection: {
            enabled: false,
            reason: 'Manual approval required for database configuration changes'
          }
        }
      };

      // Verify drift detection
      expect(driftDetection.driftAnalysis.detected).toBe(true);
      expect(driftDetection.driftAnalysis.drifts).toHaveLength(1);
      expect(driftDetection.driftAnalysis.alertTriggered).toBe(true);
      expect(driftDetection.driftAnalysis.autoCorrection.enabled).toBe(false);
    });

    it('should support configuration backup and restore', async () => {
      // Test configuration backup and restore functionality
      const backupRestoreWorkflow = {
        originalConfig: {
          database: { pool_size: 20, timeout: 30000 },
          monitoring: { enabled: true, interval: 5000 }
        },
        backup: {
          timestamp: Date.now(),
          version: '1.0.0',
          checksum: 'sha256:abc123',
          config: {
            database: { pool_size: 20, timeout: 30000 },
            monitoring: { enabled: true, interval: 5000 }
          }
        },
        corruptedConfig: {
          database: { pool_size: 'invalid' },
          monitoring: { enabled: true, interval: 5000 }
        },
        restoreProcess: {
          triggered: true,
          backup_used: '1.0.0',
          success: true,
          duration: 200
        },
        restoredConfig: {
          database: { pool_size: 20, timeout: 30000 },
          monitoring: { enabled: true, interval: 5000 }
        }
      };

      // Verify backup and restore process
      expect(backupRestoreWorkflow.backup.checksum).toMatch(/^sha256:/);
      expect(backupRestoreWorkflow.restoreProcess.triggered).toBe(true);
      expect(backupRestoreWorkflow.restoreProcess.success).toBe(true);
      expect(backupRestoreWorkflow.restoredConfig.database.pool_size).toBe(20);
    });
  });

  describe('Configuration Validation and Error Recovery', () => {
    it('should validate configuration schema and constraints', async () => {
      // Test configuration schema validation
      const schemaValidation = {
        configSchema: {
          database: {
            pool_size: { type: 'number', min: 1, max: 100 },
            timeout: { type: 'number', min: 1000, max: 300000 },
            ssl_mode: { type: 'string', enum: ['disable', 'allow', 'prefer', 'require'] }
          },
          monitoring: {
            enabled: { type: 'boolean' },
            interval: { type: 'number', min: 1000, max: 3600000 }
          }
        },
        testConfigs: [
          {
            config: { database: { pool_size: 50, timeout: 30000, ssl_mode: 'require' } },
            valid: true
          },
          {
            config: { database: { pool_size: 150, timeout: 30000, ssl_mode: 'require' } },
            valid: false,
            errors: ['database.pool_size exceeds maximum value of 100']
          },
          {
            config: { database: { pool_size: 50, timeout: 500, ssl_mode: 'invalid' } },
            valid: false,
            errors: [
              'database.timeout below minimum value of 1000',
              'database.ssl_mode must be one of: disable, allow, prefer, require'
            ]
          }
        ]
      };

      // Verify schema validation
      expect(schemaValidation.testConfigs[0].valid).toBe(true);
      expect(schemaValidation.testConfigs[1].valid).toBe(false);
      expect(schemaValidation.testConfigs[1].errors).toHaveLength(1);
      expect(schemaValidation.testConfigs[2].valid).toBe(false);
      expect(schemaValidation.testConfigs[2].errors).toHaveLength(2);
    });

    it('should implement graceful degradation on configuration errors', async () => {
      // Test graceful degradation when configuration is invalid
      const degradationWorkflow = {
        invalidConfig: {
          database: { pool_size: 'invalid', timeout: 30000 },
          monitoring: { enabled: 'not_boolean', interval: 5000 }
        },
        fallbackConfig: {
          database: { pool_size: 10, timeout: 30000 },
          monitoring: { enabled: false, interval: 10000 }
        },
        degradationStrategy: {
          level: 'partial',
          affectedServices: ['database_pool', 'monitoring'],
          workingServices: ['basic_operations', 'error_reporting'],
          userImpact: 'reduced_performance'
        },
        recovery: {
          autoRetry: true,
          retryInterval: 30000,
          maxRetries: 5,
          escalation: 'alert_administrators'
        }
      };

      // Verify degradation strategy
      expect(degradationWorkflow.degradationStrategy.level).toBe('partial');
      expect(degradationWorkflow.degradationStrategy.affectedServices).toHaveLength(2);
      expect(degradationWorkflow.degradationStrategy.workingServices).toHaveLength(2);
      expect(degradationWorkflow.recovery.autoRetry).toBe(true);
    });

    it('should support configuration versioning and migration', async () => {
      // Test configuration version migration
      const migrationWorkflow = {
        currentVersion: '1.0.0',
        targetVersion: '2.0.0',
        migrations: [
          {
            from: '1.0.0',
            to: '1.1.0',
            changes: ['Added monitoring.detailed_logging field'],
            migration: (config: any) => ({
              ...config,
              monitoring: { ...config.monitoring, detailed_logging: false }
            })
          },
          {
            from: '1.1.0',
            to: '2.0.0',
            changes: ['Renamed database.pool_size to database.max_connections'],
            migration: (config: any) => ({
              ...config,
              database: {
                ...config.database,
                max_connections: config.database.pool_size,
                pool_size: undefined
              }
            })
          }
        ],
        migrationResult: {
          success: true,
          appliedMigrations: 2,
          finalVersion: '2.0.0',
          backupCreated: true
        }
      };

      // Verify migration workflow
      expect(migrationWorkflow.migrations).toHaveLength(2);
      expect(migrationWorkflow.migrationResult.success).toBe(true);
      expect(migrationWorkflow.migrationResult.appliedMigrations).toBe(2);
      expect(migrationWorkflow.migrationResult.finalVersion).toBe('2.0.0');
      expect(migrationWorkflow.migrationResult.backupCreated).toBe(true);
    });
  });
}); 