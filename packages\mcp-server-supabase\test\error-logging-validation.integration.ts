import { describe, test, expect, beforeEach, afterEach, vi } from 'vitest';
import { contextLogger, RequestTracker, type LogContext } from '../src/utils/logger.js';
import { UniversalErrorHandler, type ToolErrorContext } from '../src/tools/universal-error-handler.js';
import {
  McpServerError,
  DatabaseError,
  ValidationError,
  ToolError,
  TimeoutError,
  ErrorCode,
} from '../src/utils/errors.js';

/**
 * Comprehensive Error Logging Validation Test Suite
 * 
 * Tests validation and runtime error logging across all MCP tool categories.
 * Ensures proper error context, structured logging, and debugging information.
 */

describe('Error Logging Validation Integration Tests', () => {
  let errorHandler: UniversalErrorHandler;
  let mockLogger: any;
  let originalLogger: any;
  let requestId: string;

  beforeEach(() => {
    // Setup request context
    requestId = RequestTracker.generateRequestId();
    RequestTracker.setContext(requestId, { component: 'test-suite' });

    // Create mock logger with spy functions
    mockLogger = {
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
      trace: vi.fn(),
      child: vi.fn().mockReturnThis(),
    };

    // Backup original logger and replace with mock
    originalLogger = contextLogger;
    vi.mocked(contextLogger).child = vi.fn(() => mockLogger);
    
    errorHandler = new UniversalErrorHandler(requestId);
  });

  afterEach(() => {
    RequestTracker.clearContext(requestId);
    vi.clearAllMocks();
  });

  describe('Validation Error Logging', () => {
    test('should log detailed validation errors with field context', () => {
      const context: ToolErrorContext = {
        toolName: 'test_tool',
        operation: 'VALIDATE_PARAMETERS',
        category: 'CRUD',
        parameters: { tableName: '', schema: 'invalid-schema@#$' },
        validation: [
          { field: 'tableName', error: 'String must contain at least 1 character(s)', value: '' },
          { field: 'schema', error: 'Invalid PostgreSQL identifier', value: 'invalid-schema@#$' }
        ]
      };

      const validationError = new ValidationError('Parameter validation failed', {
        requestId,
        context: { validationErrors: context.validation }
      });

      expect(() => {
        errorHandler.handleError(validationError, context);
      }).toThrow(McpServerError);

      // Verify error logging was called with proper context
      expect(mockLogger.error).toHaveBeenCalledWith(
        'test_tool operation failed',
        expect.any(ValidationError),
        expect.objectContaining({
          toolName: 'test_tool',
          operation: 'VALIDATE_PARAMETERS',
          category: 'CRUD',
          requestId,
          parameters: expect.objectContaining({
            tableName: '',
            schema: 'invalid-schema@#$'
          }),
          validation: expect.arrayContaining([
            expect.objectContaining({
              field: 'tableName',
              error: 'String must contain at least 1 character(s)'
            }),
            expect.objectContaining({
              field: 'schema', 
              error: 'Invalid PostgreSQL identifier'
            })
          ])
        })
      );
    });

    test('should log schema validation errors for DDL operations', () => {
      const context: ToolErrorContext = {
        toolName: 'create_index',
        operation: 'CREATE_INDEX',
        category: 'DDL',
        schema: 'public',
        table: 'users',
        columns: ['email', 'username'],
        parameters: {
          indexName: 'user@email#index', // Invalid character in index name
          table: 'users',
          columns: ['email', 'username']
        },
        validation: [
          { field: 'indexName', error: 'Invalid PostgreSQL identifier: contains illegal characters', value: 'user@email#index' }
        ]
      };

      const validationError = new ValidationError('Index name validation failed', {
        requestId,
        context: { field: 'indexName', value: 'user@email#index' }
      });

      expect(() => {
        errorHandler.handleError(validationError, context);
      }).toThrow(McpServerError);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'create_index operation failed',
        expect.any(ValidationError),
        expect.objectContaining({
          toolName: 'create_index',
          operation: 'CREATE_INDEX', 
          category: 'DDL',
          schema: 'public',
          table: 'users',
          columns: ['email', 'username'],
          validation: expect.arrayContaining([
            expect.objectContaining({
              field: 'indexName',
              error: 'Invalid PostgreSQL identifier: contains illegal characters'
            })
          ])
        })
      );
    });

    test('should log JSON validation errors with parse context', () => {
      const malformedJson = '{"name": "test", "data": [1,2,3'; // Missing closing bracket and brace
      
      const context: ToolErrorContext = {
        toolName: 'validate_json',
        operation: 'VALIDATE_SYNTAX',
        category: 'JSON',
        parameters: {
          jsonString: malformedJson,
          validateSyntax: true
        },
        validation: [
          { 
            field: 'jsonString', 
            error: 'Unexpected end of JSON input at position 26',
            value: malformedJson
          }
        ]
      };

      const jsonError = new ValidationError('JSON syntax validation failed', {
        requestId,
        context: { 
          parseError: 'Unexpected end of JSON input at position 26',
          jsonLength: malformedJson.length,
          position: 26
        }
      });

      expect(() => {
        errorHandler.handleError(jsonError, context);
      }).toThrow(McpServerError);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'validate_json operation failed',
        expect.any(ValidationError),
        expect.objectContaining({
          toolName: 'validate_json',
          operation: 'VALIDATE_SYNTAX',
          category: 'JSON',
          parameters: expect.objectContaining({
            jsonString: malformedJson,
            validateSyntax: true
          }),
          validation: expect.arrayContaining([
            expect.objectContaining({
              field: 'jsonString',
              error: 'Unexpected end of JSON input at position 26'
            })
          ])
        })
      );
    });
  });

  describe('Database Error Logging', () => {
    test('should log database connection errors with retry context', () => {
      const context: ToolErrorContext = {
        toolName: 'execute_sql',
        operation: 'QUERY_EXECUTION',
        category: 'DATABASE',
        query: 'SELECT * FROM users WHERE id = $1',
        parameters: { userId: 123 }
      };

      const dbError = new Error('connection to server at "localhost" (127.0.0.1), port 5432 failed');
      
      // Test retry logging directly
      errorHandler.logRetryAttempt(dbError, context, {
        attempt: 1,
        maxAttempts: 2,
        delay: 1000,
        reason: 'Database connection failed'
      });

      // Verify retry warning logs were generated
      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Retrying operation after failure',
        expect.objectContaining({
          toolName: 'execute_sql',
          operation: 'QUERY_EXECUTION',
          category: 'DATABASE',
          retryAttempt: 1,
          maxRetries: 2,
          retryDelay: 1000,
          retryReason: 'Database connection failed',
          errorMessage: 'connection to server at "localhost" (127.0.0.1), port 5432 failed'
        })
      );
    });

    test('should log constraint violation errors with table context', () => {
      const context: ToolErrorContext = {
        toolName: 'insert_record',
        operation: 'INSERT',
        category: 'CRUD',
        schema: 'public',
        table: 'users',
        parameters: {
          data: { email: '<EMAIL>', username: 'testuser' }
        }
      };

      const constraintError = new DatabaseError(
        'duplicate key value violates unique constraint "users_email_key"',
        ErrorCode.CONSTRAINT_VIOLATION,
        {
          requestId,
          context: {
            constraint: 'users_email_key',
            table: 'users',
            conflictingValue: '<EMAIL>'
          }
        }
      );

      expect(() => {
        errorHandler.handleError(constraintError, context);
      }).toThrow(McpServerError);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'insert_record operation failed',
        expect.any(DatabaseError),
        expect.objectContaining({
          toolName: 'insert_record',
          operation: 'INSERT',
          category: 'CRUD',
          schema: 'public',
          table: 'users',
          parameters: expect.objectContaining({
            data: expect.objectContaining({
              email: '<EMAIL>'
            })
          })
        })
      );
    });
  });

  describe('Tool-Specific Error Logging', () => {
    test('should log edge function deployment errors with function context', () => {
      const context: ToolErrorContext = {
        toolName: 'deploy_edge_function',
        operation: 'DEPLOY',
        category: 'EDGE_FUNCTION',
        parameters: {
          functionName: 'user-processor',
          code: 'export default async function handler(req) { return new Response("Hello"); }'
        }
      };

      const deployError = new ToolError(
        'Function deployment failed: Syntax error in function code',
        ErrorCode.TOOL_EXECUTION_ERROR,
        {
          requestId,
          context: {
            functionName: 'user-processor',
            deploymentId: 'deploy-123',
            syntaxError: 'Unexpected token at line 1'
          }
        }
      );

      expect(() => {
        errorHandler.handleError(deployError, context);
      }).toThrow(McpServerError);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'deploy_edge_function operation failed',
        expect.any(ToolError),
        expect.objectContaining({
          toolName: 'deploy_edge_function',
          operation: 'DEPLOY',
          category: 'EDGE_FUNCTION',
          parameters: expect.objectContaining({
            functionName: 'user-processor'
          })
        })
      );
    });

    test('should log monitoring tool errors with service context', () => {
      const context: ToolErrorContext = {
        toolName: 'get_logs',
        operation: 'FETCH_LOGS',
        category: 'MONITORING',
        parameters: {
          service: 'postgres',
          lines: 100,
          level: 'error'
        }
      };

      const monitoringError = new ToolError(
        'Service logs unavailable: Docker container not found',
        ErrorCode.TOOL_EXECUTION_ERROR,
        {
          requestId,
          context: {
            service: 'postgres',
            containerStatus: 'not_found',
            suggestedAction: 'Start local Supabase services'
          }
        }
      );

      expect(() => {
        errorHandler.handleError(monitoringError, context);
      }).toThrow(McpServerError);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'get_logs operation failed',
        expect.any(ToolError),
        expect.objectContaining({
          toolName: 'get_logs',
          operation: 'FETCH_LOGS',
          category: 'MONITORING',
          parameters: expect.objectContaining({
            service: 'postgres',
            lines: 100
          })
        })
      );
    });
  });

  describe('Success Operation Logging', () => {
    test('should log successful operations with performance metrics', () => {
      const context: ToolErrorContext = {
        toolName: 'select_records',
        operation: 'SELECT',
        category: 'CRUD',
        schema: 'public',
        table: 'users',
        recordCount: 25,
        parameters: {
          columns: ['id', 'email', 'username'],
          limit: 50
        }
      };

      const result = {
        data: Array(25).fill(null).map((_, i) => ({
          id: i + 1,
          email: `user${i + 1}@example.com`,
          username: `user${i + 1}`
        })),
        count: 25
      };

      const duration = 150; // 150ms

      errorHandler.logSuccess(context, result, duration);

      expect(mockLogger.info).toHaveBeenCalledWith(
        'select_records operation completed successfully',
        expect.objectContaining({
          operation: 'SELECT',
          category: 'CRUD',
          duration: 150,
          recordCount: 25,
          requestId
        })
      );
    });

    test('should log successful DDL operations with schema modifications', () => {
      const context: ToolErrorContext = {
        toolName: 'create_index',
        operation: 'CREATE_INDEX',
        category: 'DDL',
        schema: 'public',
        table: 'users',
        columns: ['email'],
        parameters: {
          indexName: 'idx_users_email',
          table: 'users',
          columns: ['email'],
          unique: true
        }
      };

      const result = {
        success: true,
        indexName: 'idx_users_email',
        table: 'public.users',
        columns: ['email']
      };

      const duration = 320; // 320ms

      errorHandler.logSuccess(context, result, duration);

      expect(mockLogger.info).toHaveBeenCalledWith(
        'create_index operation completed successfully',
        expect.objectContaining({
          operation: 'CREATE_INDEX',
          category: 'DDL',
          duration: 320,
          requestId
        })
      );
    });
  });

  describe('Error Context Enrichment', () => {
    test('should enrich error context with request tracking', () => {
      const baseContext: ToolErrorContext = {
        toolName: 'update_record',
        operation: 'UPDATE',
        category: 'CRUD'
      };

      const error = new Error('Update operation failed');

      expect(() => {
        errorHandler.handleError(error, baseContext);
      }).toThrow();

      // Verify the logged context includes enrichment
      expect(mockLogger.error).toHaveBeenCalledWith(
        'update_record operation failed',
        expect.any(Error),
        expect.objectContaining({
          toolName: 'update_record',
          operation: 'UPDATE',
          category: 'CRUD',
          requestId: expect.any(String),
          timestamp: expect.any(Number)
        })
      );
    });

    test('should preserve sensitive data redaction in logs', () => {
      const context: ToolErrorContext = {
        toolName: 'authenticate_user',
        operation: 'AUTH',
        category: 'DEVELOPMENT',
        parameters: {
          username: 'testuser',
          password: 'secretpassword123',
          apiKey: 'sk-1234567890abcdef',
          token: 'bearer-token-value'
        }
      };

      const authError = new ValidationError('Authentication failed', {
        requestId,
        context: { reason: 'invalid_credentials' }
      });

      expect(() => {
        errorHandler.handleError(authError, context);
      }).toThrow(McpServerError);

      // Verify error was logged (sensitive data redaction is handled by logger config)
      expect(mockLogger.error).toHaveBeenCalledWith(
        'authenticate_user operation failed',
        expect.any(ValidationError),
        expect.objectContaining({
          toolName: 'authenticate_user',
          operation: 'AUTH',
          category: 'DEVELOPMENT',
          parameters: expect.objectContaining({
            username: 'testuser'
            // password, apiKey, token should be redacted by logger config
          })
        })
      );
    });
  });

  describe('Validation Error Details', () => {
    test('should capture detailed validation error information', () => {
      // Simulate a complex validation failure with multiple field errors
      const context: ToolErrorContext = {
        toolName: 'bulk_insert',
        operation: 'BULK_INSERT',
        category: 'CRUD',
        schema: 'public',
        table: 'orders',
        parameters: {
          data: [
            { id: '', amount: -100, currency: '' }, // Multiple validation errors
            { id: 'valid-id', amount: 50.99, currency: 'USD' }, // Valid record
            { id: 'another-id', amount: 'invalid', currency: 'INVALID_CURRENCY' } // Type and enum errors
          ]
        },
        validation: [
          { field: 'data[0].id', error: 'String must contain at least 1 character(s)', value: '' },
          { field: 'data[0].amount', error: 'Number must be greater than 0', value: -100 },
          { field: 'data[0].currency', error: 'String must contain at least 1 character(s)', value: '' },
          { field: 'data[2].amount', error: 'Expected number, received string', value: 'invalid' },
          { field: 'data[2].currency', error: 'Invalid enum value. Expected USD | EUR | GBP', value: 'INVALID_CURRENCY' }
        ]
      };

      const bulkValidationError = new ValidationError('Bulk insert validation failed', {
        requestId,
        context: { 
          failedRecords: 2,
          totalRecords: 3,
          validationErrors: context.validation
        }
      });

      expect(() => {
        errorHandler.handleError(bulkValidationError, context);
      }).toThrow(McpServerError);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'bulk_insert operation failed',
        expect.any(ValidationError),
        expect.objectContaining({
          validation: expect.arrayContaining([
            expect.objectContaining({
              field: 'data[0].id',
              error: 'String must contain at least 1 character(s)'
            }),
            expect.objectContaining({
              field: 'data[2].amount',
              error: 'Expected number, received string'
            }),
            expect.objectContaining({
              field: 'data[2].currency',
              error: 'Invalid enum value. Expected USD | EUR | GBP'
            })
          ])
        })
      );
    });
  });
});