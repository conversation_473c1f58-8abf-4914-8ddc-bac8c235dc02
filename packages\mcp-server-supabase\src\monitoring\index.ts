/**
 * Comprehensive monitoring and observability system for MCP Server
 * 
 * This module provides:
 * - Health check endpoints
 * - Metrics collection and exposure
 * - Distributed tracing
 * - Alerting system
 * - Test coverage reporting
 * - Monitoring dashboard
 */

export { 
  HealthEndpoints, 
  healthEndpoints, 
  setupHealthRoutes 
} from './health-endpoints.js';

export { 
  MetricsEndpoints, 
  metricsEndpoints, 
  setupMetricsRoutes 
} from './metrics-endpoints.js';

export { 
  DistributedTracer, 
  Span, 
  distributedTracer, 
  traced, 
  tracingMiddleware 
} from './distributed-tracing.js';

export type { TraceContext } from './distributed-tracing.js';

export { 
  AlertingSystem, 
  alertingSystem 
} from './alerting-system.js';

export type { 
  Alert, 
  AlertRule, 
  AlertSeverity, 
  AlertHandler 
} from './alerting-system.js';

import { contextLogger } from '../utils/logger.js';
import { setupHealthRoutes } from './health-endpoints.js';
import { setupMetricsRoutes } from './metrics-endpoints.js';
import { tracingMiddleware } from './distributed-tracing.js';
import { alertingSystem } from './alerting-system.js';

/**
 * Monitoring system configuration
 */
export interface MonitoringConfig {
  enableHealthEndpoints: boolean;
  enableMetricsEndpoints: boolean;
  enableDistributedTracing: boolean;
  enableAlerting: boolean;
  alertCheckInterval: number;
  tracingCleanupInterval: number;
  metricsRetentionDays: number;
}

/**
 * Default monitoring configuration
 */
export const defaultMonitoringConfig: MonitoringConfig = {
  enableHealthEndpoints: true,
  enableMetricsEndpoints: true,
  enableDistributedTracing: true,
  enableAlerting: true,
  alertCheckInterval: 60000, // 1 minute
  tracingCleanupInterval: 3600000, // 1 hour
  metricsRetentionDays: 7,
};

/**
 * Comprehensive monitoring system
 */
export class MonitoringSystem {
  private config: MonitoringConfig;
  private logger = contextLogger.child({ component: 'monitoring-system' });
  private initialized = false;

  constructor(config: Partial<MonitoringConfig> = {}) {
    this.config = { ...defaultMonitoringConfig, ...config };
  }

  /**
   * Initialize the monitoring system
   */
  async initialize(app?: any): Promise<void> {
    if (this.initialized) {
      this.logger.warn('Monitoring system already initialized');
      return;
    }

    this.logger.info('Initializing monitoring system', this.config);

    try {
      // Setup health endpoints
      if (this.config.enableHealthEndpoints && app) {
        setupHealthRoutes(app);
        this.logger.info('Health endpoints configured');
      }

      // Setup metrics endpoints
      if (this.config.enableMetricsEndpoints && app) {
        setupMetricsRoutes(app);
        this.logger.info('Metrics endpoints configured');
      }

      // Setup distributed tracing middleware
      if (this.config.enableDistributedTracing && app) {
        app.use(tracingMiddleware());
        this.logger.info('Distributed tracing middleware configured');
      }

      // Start alerting system
      if (this.config.enableAlerting) {
        alertingSystem.startMonitoring(this.config.alertCheckInterval);
        this.logger.info('Alerting system started');
      }

      this.initialized = true;
      this.logger.info('Monitoring system initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize monitoring system', error instanceof Error ? error : undefined);
      throw error;
    }
  }

  /**
   * Get monitoring system status
   */
  getStatus(): {
    initialized: boolean;
    config: MonitoringConfig;
    components: {
      healthEndpoints: boolean;
      metricsEndpoints: boolean;
      distributedTracing: boolean;
      alerting: boolean;
    };
  } {
    return {
      initialized: this.initialized,
      config: this.config,
      components: {
        healthEndpoints: this.config.enableHealthEndpoints,
        metricsEndpoints: this.config.enableMetricsEndpoints,
        distributedTracing: this.config.enableDistributedTracing,
        alerting: this.config.enableAlerting,
      },
    };
  }

  /**
   * Get comprehensive monitoring dashboard data
   */
  async getDashboardData(): Promise<{
    health: any;
    metrics: any;
    alerts: any;
    tracing: any;
    system: any;
  }> {
    const [healthData, metricsData, alertsData, tracingData] = await Promise.all([
      this.getHealthData(),
      this.getMetricsData(),
      this.getAlertsData(),
      this.getTracingData(),
    ]);

    const systemData = this.getSystemData();

    return {
      health: healthData,
      metrics: metricsData,
      alerts: alertsData,
      tracing: tracingData,
      system: systemData,
    };
  }

  /**
   * Get health data for dashboard
   */
  private async getHealthData(): Promise<any> {
    try {
      const { healthChecker } = await import('../utils/middleware.js');
      const { configHealthMonitor } = await import('../config/config-health.js');
      
      const [health, configHealth] = await Promise.all([
        healthChecker.getHealth(),
        configHealthMonitor.checkHealth(),
      ]);

      return {
        overall: health,
        configuration: configHealth,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to get health data', error instanceof Error ? error : undefined);
      return { error: 'Failed to collect health data' };
    }
  }

  /**
   * Get metrics data for dashboard
   */
  private async getMetricsData(): Promise<any> {
    try {
      const { performanceManager } = await import('../performance/index.js');
      const { PerformanceMonitor } = await import('../utils/middleware.js');
      
      const [performanceMetrics, operationMetrics] = await Promise.all([
        performanceManager.getMetrics(),
        Promise.resolve(PerformanceMonitor.getMetrics()),
      ]);

      return {
        performance: performanceMetrics,
        operations: operationMetrics,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to get metrics data', error instanceof Error ? error : undefined);
      return { error: 'Failed to collect metrics data' };
    }
  }

  /**
   * Get alerts data for dashboard
   */
  private getAlertsData(): any {
    try {
      return {
        active: alertingSystem.getActiveAlerts(),
        history: alertingSystem.getAlertHistory(50),
        stats: alertingSystem.getStats(),
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to get alerts data', error instanceof Error ? error : undefined);
      return { error: 'Failed to collect alerts data' };
    }
  }

  /**
   * Get tracing data for dashboard
   */
  private getTracingData(): any {
    try {
      const { distributedTracer } = require('./distributed-tracing.js');
      
      return {
        stats: distributedTracer.getStats(),
        activeTraces: Array.from(distributedTracer.getActiveTraces().keys()).slice(0, 20),
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to get tracing data', error instanceof Error ? error : undefined);
      return { error: 'Failed to collect tracing data' };
    }
  }

  /**
   * Get system data for dashboard
   */
  private getSystemData(): any {
    try {
      const memoryUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();
      
      return {
        memory: memoryUsage,
        cpu: cpuUsage,
        uptime: process.uptime(),
        pid: process.pid,
        version: process.version,
        platform: process.platform,
        arch: process.arch,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to get system data', error instanceof Error ? error : undefined);
      return { error: 'Failed to collect system data' };
    }
  }

  /**
   * Shutdown the monitoring system
   */
  async shutdown(): Promise<void> {
    if (!this.initialized) {
      return;
    }

    this.logger.info('Shutting down monitoring system');

    try {
      // Stop alerting system
      if (this.config.enableAlerting) {
        alertingSystem.stopMonitoring();
      }

      this.initialized = false;
      this.logger.info('Monitoring system shutdown complete');
    } catch (error) {
      this.logger.error('Error during monitoring system shutdown', error instanceof Error ? error : undefined);
      throw error;
    }
  }
}

/**
 * Global monitoring system instance
 */
export const monitoringSystem = new MonitoringSystem();

/**
 * Convenience function to initialize monitoring with Express app
 */
export async function initializeMonitoring(app: any, config?: Partial<MonitoringConfig>): Promise<void> {
  const monitoring = new MonitoringSystem(config);
  await monitoring.initialize(app);
}

/**
 * Convenience function to setup all monitoring routes
 */
export function setupMonitoringRoutes(app: any): void {
  setupHealthRoutes(app);
  setupMetricsRoutes(app);
  
  // Add dashboard endpoint
  app.get('/monitoring/dashboard', async (req: any, res: any) => {
    try {
      const dashboardData = await monitoringSystem.getDashboardData();
      res.json(dashboardData);
    } catch (error) {
      res.status(500).json({
        error: 'Failed to get dashboard data',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });
}
