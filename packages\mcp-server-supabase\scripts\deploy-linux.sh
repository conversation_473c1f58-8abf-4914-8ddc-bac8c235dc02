#!/bin/bash
# Linux/macOS Deployment Script for Local Supabase MCP Server

set -e

SUPABASE_URL="${1:-https://devdb.syncrobit.net}"
ANON_KEY="$2"
SERVICE_KEY="$3"
READ_ONLY="${4:-false}"
INSTALL_PATH="${5:-$HOME/.local-supabase-mcp}"
CONFIGURE_CURSOR="${6:-true}"
CONFIGURE_CLAUDE="${7:-false}"

echo "🚀 Deploying Local Supabase MCP Server for Linux/macOS..."

# Validate required parameters
if [[ -z "$ANON_KEY" || -z "$SERVICE_KEY" ]]; then
    echo "❌ Usage: $0 <supabase_url> <anon_key> <service_key> [read_only] [install_path] [configure_cursor] [configure_claude]"
    echo ""
    echo "Example:"
    echo "$0 'https://your-project.supabase.co' 'your_anon_key' 'your_service_key'"
    exit 1
fi

# Create installation directory
echo "📁 Creating installation directory: $INSTALL_PATH"
mkdir -p "$INSTALL_PATH"

# Build the project
echo "🔨 Building MCP server..."
cd "$(dirname "$0")/.."
npm run build || {
    echo "❌ Build failed"
    exit 1
}

# Copy built files
echo "📦 Copying files to installation directory..."
cp -r dist/* "$INSTALL_PATH/"

# Make executable
chmod +x "$INSTALL_PATH/transports/stdio.cjs"

# Create startup script
cat > "$INSTALL_PATH/start-mcp-server.sh" << 'EOF'
#!/bin/bash
echo "Starting Local Supabase MCP Server..."
SCRIPT_DIR="$(dirname "$0")"
node "$SCRIPT_DIR/transports/stdio.cjs" "$@"
EOF

chmod +x "$INSTALL_PATH/start-mcp-server.sh"# Create Cursor IDE configuration
if [[ "$CONFIGURE_CURSOR" == "true" ]]; then
    cat > "$INSTALL_PATH/cursor-config.json" << EOF
{
  "mcpServers": {
    "supabase-local": {
      "command": "node",
      "args": [
        "$INSTALL_PATH/transports/stdio.cjs"
      ],
      "env": {
        "SUPABASE_URL": "$SUPABASE_URL",
        "SUPABASE_ANON_KEY": "$ANON_KEY",
        "SUPABASE_SERVICE_ROLE_KEY": "$SERVICE_KEY",
        "READ_ONLY": "$READ_ONLY",
        "DEBUG_SQL": "false"
      }
    }
  }
}
EOF
    echo "✅ Cursor IDE configuration created: $INSTALL_PATH/cursor-config.json"
fi

# Create Claude Desktop configuration
if [[ "$CONFIGURE_CLAUDE" == "true" ]]; then
    cat > "$INSTALL_PATH/claude-desktop-config.json" << EOF
{
  "\$schema": "https://modelcontextprotocol.io/schemas/claude-desktop-config.json",
  "mcpServers": {
    "supabase-local": {
      "command": "node",
      "args": [
        "$INSTALL_PATH/transports/stdio.cjs"
      ],
      "env": {
        "SUPABASE_URL": "$SUPABASE_URL",
        "SUPABASE_ANON_KEY": "$ANON_KEY",
        "SUPABASE_SERVICE_ROLE_KEY": "$SERVICE_KEY",
        "READ_ONLY": "$READ_ONLY",
        "DEBUG_SQL": "false"
      }
    }
  }
}
EOF

    # Try to copy to Claude Desktop config location
    if [[ "$OSTYPE" == "darwin"* ]]; then
        CLAUDE_CONFIG_PATH="$HOME/Library/Application Support/Claude/claude_desktop_config.json"
    else
        CLAUDE_CONFIG_PATH="$HOME/.config/claude/claude_desktop_config.json"
    fi
    
    if [[ -d "$(dirname "$CLAUDE_CONFIG_PATH")" ]]; then
        cp "$INSTALL_PATH/claude-desktop-config.json" "$CLAUDE_CONFIG_PATH" && \
        echo "✅ Claude Desktop configuration installed: $CLAUDE_CONFIG_PATH" || \
        echo "⚠️  Could not automatically install Claude Desktop config. Manual copy required."
    fi
fi

echo ""
echo "✅ Deployment completed successfully!"
echo "📁 Installation path: $INSTALL_PATH"
echo ""

if [[ "$CONFIGURE_CURSOR" == "true" ]]; then
    echo "🔧 Next steps for Cursor IDE:"
    echo "1. Open Cursor IDE settings"
    echo "2. Add the configuration from: $INSTALL_PATH/cursor-config.json"
    echo "3. Restart Cursor IDE"
    echo ""
fi

echo "🧪 Test the installation:"
echo "node \"$INSTALL_PATH/transports/stdio.cjs\" --version"