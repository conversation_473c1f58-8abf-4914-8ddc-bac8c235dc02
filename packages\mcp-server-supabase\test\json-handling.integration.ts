import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { getJsonHandlingTools } from '../src/tools/json-handling-tools.js';
import { createLocalSupabasePlatform } from '../src/platform/local-platform.js';
import { getLocalConfig } from '../src/config/index.js';
import type { SupabasePlatform } from '../src/platform/types.js';

describe('JSON Handling Tools Integration', () => {
  let platform: SupabasePlatform;
  let jsonTools: ReturnType<typeof getJsonHandlingTools>;

  beforeAll(async () => {
    // Setup local platform for testing
    const config = getLocalConfig();
    platform = createLocalSupabasePlatform(config);
    
    // Initialize JSON handling tools
    jsonTools = getJsonHandlingTools({
      platform,
      projectId: 'test-project',
      readOnly: false,
    });
  });

  afterAll(async () => {
    // Cleanup if needed
    if (platform && typeof platform.close === 'function') {
      await platform.close();
    }
  });

  describe('JSON Validation', () => {
    it('should validate correct JSON syntax', async () => {
      const validJson = '{"name": "test", "value": 123}';
      
      const result = await jsonTools.validate_json_syntax.execute({
        project_id: 'test-project',
        json_data: validJson,
        check_encoding: true,
      });

      expect(result.is_valid).toBe(true);
      expect(result.error_message).toBeNull();
    });

    it('should detect invalid JSON syntax', async () => {
      const invalidJson = '{"name": "test", "value": 123'; // Missing closing brace
      
      const result = await jsonTools.validate_json_syntax.execute({
        project_id: 'test-project',
        json_data: invalidJson,
        check_encoding: true,
      });

      expect(result.is_valid).toBe(false);
      expect(result.error_message).toBeTruthy();
    });

    it('should validate JSON against schema', async () => {
      const validJson = '{"name": "test", "age": 25}';
      const schema = {
        type: 'object',
        required: ['name', 'age'],
      };
      
      const result = await jsonTools.validate_json_schema.execute({
        project_id: 'test-project',
        json_data: validJson,
        schema_definition: schema,
        strict_mode: false,
      });

      expect(result.is_valid).toBe(true);
      expect(result.error_message).toBeNull();
    });
  });

  describe('JSON Parsing and Extraction', () => {
    it('should safely parse valid JSON', async () => {
      const validJson = '{"user": {"name": "John", "age": 30}}';
      
      const result = await jsonTools.safe_json_parse.execute({
        project_id: 'test-project',
        json_data: validJson,
        return_raw_on_error: false,
      });

      expect(result.success).toBe(true);
      expect(result.data).toEqual({ user: { name: 'John', age: 30 } });
    });

    it('should extract values using path expressions', async () => {
      const jsonData = '{"user": {"name": "John", "age": 30}}';
      
      const result = await jsonTools.extract_json_value.execute({
        project_id: 'test-project',
        json_data: jsonData,
        path: '$.user.name',
        default_value: 'unknown',
      });

      expect(result.success).toBe(true);
      expect(result.value).toBe('John');
      expect(result.path_found).toBe(true);
    });

    it('should extract multiple values at once', async () => {
      const jsonData = '{"user": {"name": "John", "age": 30, "city": "NYC"}}';
      
      const result = await jsonTools.extract_multiple_values.execute({
        project_id: 'test-project',
        json_data: jsonData,
        paths: [
          { name: 'userName', path: '$.user.name' },
          { name: 'userAge', path: '$.user.age' },
          { name: 'userCountry', path: '$.user.country', default_value: 'USA' },
        ],
      });

      expect(result.success).toBe(true);
      expect(result.extractions.userName.value).toBe('John');
      expect(result.extractions.userAge.value).toBe(30);
      expect(result.extractions.userCountry.value).toBe('USA');
      expect(result.extractions.userCountry.path_found).toBe(false);
    });
  });

  describe('JSON Transformation', () => {
    it('should merge JSON objects', async () => {
      const obj1 = '{"name": "John", "age": 30}';
      const obj2 = '{"city": "NYC", "country": "USA"}';
      
      const result = await jsonTools.merge_json_objects.execute({
        project_id: 'test-project',
        json_objects: [obj1, obj2],
        merge_strategy: 'overwrite',
        deep_merge: true,
      });

      expect(result.success).toBe(true);
      expect(result.merged_json).toMatchObject({
        name: 'John',
        age: 30,
        city: 'NYC',
        country: 'USA',
      });
    });

    it('should filter JSON properties', async () => {
      const jsonData = '{"name": "John", "age": 30, "password": "secret", "city": "NYC"}';
      
      const result = await jsonTools.filter_json_properties.execute({
        project_id: 'test-project',
        json_data: jsonData,
        properties: ['name', 'city'],
        filter_mode: 'include',
        recursive: false,
      });

      expect(result.success).toBe(true);
      expect(result.filtered_json).toMatchObject({
        name: 'John',
        city: 'NYC',
      });
      expect(result.filtered_json).not.toHaveProperty('password');
    });

    it('should flatten nested JSON objects', async () => {
      const nestedJson = '{"user": {"profile": {"name": "John", "age": 30}}}';
      
      const result = await jsonTools.flatten_json_object.execute({
        project_id: 'test-project',
        json_data: nestedJson,
        separator: '.',
        max_depth: 10,
      });

      expect(result.success).toBe(true);
      expect(result.flattened_json).toMatchObject({
        'user.profile.name': 'John',
        'user.profile.age': 30,
      });
    });
  });

  describe('JSON Repair and Sanitization', () => {
    it('should repair malformed JSON', async () => {
      const malformedJson = '{"name": "John", "age": 30,}'; // Trailing comma
      
      const result = await jsonTools.repair_malformed_json.execute({
        project_id: 'test-project',
        malformed_json: malformedJson,
        repair_strategy: 'conservative',
        preserve_structure: true,
      });

      expect(result.success).toBe(true);
      expect(result.repairs_applied).toContain('Removed trailing commas');
    });

    it('should sanitize JSON data', async () => {
      const jsonData = '{"name": "  John  ", "age": null, "empty": {}}';
      
      const result = await jsonTools.sanitize_json_data.execute({
        project_id: 'test-project',
        json_data: jsonData,
        sanitization_rules: ['trim_strings', 'remove_null_values', 'remove_empty_objects'],
        max_string_length: 1000,
      });

      expect(result.success).toBe(true);
      expect(result.sanitized_json).toMatchObject({
        name: 'John', // Trimmed
        // age and empty should be removed
      });
    });

    it('should recover partial data from severely malformed JSON', async () => {
      const malformedJson = '{"name": "John"} invalid {"age": 30}';
      
      const result = await jsonTools.recover_partial_json.execute({
        project_id: 'test-project',
        malformed_json: malformedJson,
        recovery_mode: 'extract_objects',
        min_recovery_size: 5,
      });

      expect(result.success).toBe(true);
      expect(result.recovered_data.length).toBeGreaterThan(0);
      expect(result.recovery_log.length).toBeGreaterThan(0);
    });
  });

  describe('JSON Querying and Search', () => {
    it('should search for content in JSON', async () => {
      const jsonData = '{"users": [{"name": "John Doe", "email": "<EMAIL>"}, {"name": "Jane Smith", "email": "<EMAIL>"}]}';
      
      const result = await jsonTools.search_json_content.execute({
        project_id: 'test-project',
        json_data: jsonData,
        search_term: 'john',
        search_type: 'contains',
        case_sensitive: false,
        search_keys: false,
      });

      expect(result.success).toBe(true);
      expect(result.matches.length).toBeGreaterThan(0);
    });

    it('should filter JSON arrays', async () => {
      const jsonData = '{"users": [{"name": "John", "age": 30}, {"name": "Jane", "age": 25}, {"name": "Bob", "age": 35}]}';
      
      const result = await jsonTools.filter_json_array.execute({
        project_id: 'test-project',
        json_data: jsonData,
        array_path: '$.users',
        filter_conditions: [
          { property: 'age', operator: '>', value: 28 }
        ],
        condition_logic: 'AND',
      });

      expect(result.success).toBe(true);
      expect(result.filtered_array.length).toBe(2); // John and Bob
      expect(result.elements_matched).toBe(2);
    });
  });
});
