#!/usr/bin/env node

/**
 * MCP Server Wrapper for Cursor IDE
 * This wrapper runs the MCP server using tsx to avoid bundling issues
 */

const { spawn } = require('child_process');
const path = require('path');

// Set default environment variables
const defaultEnv = {
  SUPABASE_URL: 'https://devdb.syncrobit.net',
  SUPABASE_ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogImFub24iLAogICJpc3MiOiAic3VwYWJhc2UiLAogICJpYXQiOiAxNzQxMDk1MDAwLAogICJleHAiOiAxODk4ODYxNDAwCn0.J1N7sT41EI4ORiRFvPm3FPDArs43dzEq9g9-iq7d3pY',
  SUPABASE_SERVICE_ROLE_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogInNlcnZpY2Vfcm9sZSIsCiAgImlzcyI6ICJzdXBhYmFzZSIsCiAgImlhdCI6IDE3NDEwOTUwMDAsCiAgImV4cCI6IDE4OTg4NjE0MDAKfQ.4OLZbhrbccD5vfy14HcWgYDgJySzUXimxgcGA3OgaV8',
  READ_ONLY: 'false',
  DEBUG_SQL: 'false',
  LOG_LEVEL: 'error'
};

// Merge with existing environment variables
const env = { ...process.env, ...defaultEnv };

// Path to the source TypeScript file
const serverPath = path.join(__dirname, 'packages', 'mcp-server-supabase', 'src', 'transports', 'stdio.ts');

// Use tsx to run the TypeScript file directly
const tsxPath = path.join(__dirname, 'node_modules', '.bin', 'tsx.cmd');
const child = spawn(tsxPath, [serverPath, ...process.argv.slice(2)], {
  stdio: 'inherit',
  env: env,
  cwd: __dirname
});

child.on('error', (error) => {
  console.error('Failed to start MCP server:', error);
  process.exit(1);
});

child.on('exit', (code) => {
  process.exit(code || 0);
});

// Handle process termination
process.on('SIGINT', () => {
  child.kill('SIGINT');
});

process.on('SIGTERM', () => {
  child.kill('SIGTERM');
});
