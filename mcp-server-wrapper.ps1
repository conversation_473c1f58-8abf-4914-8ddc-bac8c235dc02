param(
    [string]$SupabaseUrl,
    [string]$AnonKey,
    [string]$ServiceKey
)

# Parse arguments that might come in --arg=value format
foreach ($arg in $args) {
    if ($arg -like "--supabase-url=*") {
        $SupabaseUrl = $arg.Split('=', 2)[1]
    }
    elseif ($arg -like "--anon-key=*") {
        $AnonKey = $arg.Split('=', 2)[1]
    }
    elseif ($arg -like "--service-key=*") {
        $ServiceKey = $arg.Split('=', 2)[1]
    }
}

# Set environment variables
if ($SupabaseUrl) {
    $env:SUPABASE_URL = $SupabaseUrl
}
if ($AnonKey) {
    $env:SUPABASE_ANON_KEY = $AnonKey
}
if ($ServiceKey) {
    $env:SUPABASE_SERVICE_ROLE_KEY = $ServiceKey
}

# Debug output
Write-Host "Environment variables set:"
Write-Host "SUPABASE_URL: $env:SUPABASE_URL"
Write-Host "SUPABASE_ANON_KEY: $($env:SUPABASE_ANON_KEY.Substring(0, 20))..."
Write-Host "SUPABASE_SERVICE_ROLE_KEY: $($env:SUPABASE_SERVICE_ROLE_KEY.Substring(0, 20))..."

# Call the actual MCP server
& mcp-server-supabase 