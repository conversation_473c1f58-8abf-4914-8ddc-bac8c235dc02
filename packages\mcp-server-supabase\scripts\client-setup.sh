#!/bin/bash

# MCP Server Supabase - Client Setup Script
# Run this script on target machines to set up the MCP server

set -e

echo "🚀 Setting up MCP Server Supabase..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    echo "   Visit: https://nodejs.org/"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d 'v' -f 2 | cut -d '.' -f 1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version $NODE_VERSION detected. Please upgrade to Node.js 18+."
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Test npx functionality
echo "🔧 Testing npx functionality..."
if ! npx --version &> /dev/null; then
    echo "❌ npx is not available. Please ensure npm is properly installed."
    exit 1
fi

echo "✅ npx is available"

# Test package availability
echo "📦 Testing MCP server package availability..."
if npx @supabase/mcp-server-supabase --version &> /dev/null; then
    VERSION=$(npx @supabase/mcp-server-supabase --version)
    echo "✅ MCP Server Supabase v$VERSION is available"
else
    echo "⚠️  Package not found via npx, but this is normal for first-time setups"
    echo "    The package will be downloaded automatically when first used"
fi

echo ""
echo "🎯 Setup Complete!"
echo ""
echo "📋 Next Steps:"
echo "1. Get your Supabase credentials:"
echo "   - Supabase URL (e.g., https://your-project.supabase.co)"
echo "   - Anonymous Key"
echo "   - Service Role Key"
echo ""
echo "2. Choose a configuration method:"
echo ""
echo "   Option A - CLI Arguments (recommended):"
echo "   Copy this configuration to your MCP config file:"
echo ""
echo '   {'
echo '     "mcpServers": {'
echo '       "supabase": {'
echo '         "command": "npx",'
echo '         "args": ['
echo '           "@supabase/mcp-server-supabase",'
echo '           "--supabase-url=YOUR_SUPABASE_URL",'
echo '           "--anon-key=YOUR_ANON_KEY",'
echo '           "--service-key=YOUR_SERVICE_KEY"'
echo '         ]'
echo '       }'
echo '     }'
echo '   }'
echo ""
echo "   Option B - Environment Variables:"
echo "   Copy this configuration to your MCP config file:"
echo ""
echo '   {'
echo '     "mcpServers": {'
echo '       "supabase": {'
echo '         "command": "npx",'
echo '         "args": ["@supabase/mcp-server-supabase"],'
echo '         "env": {'
echo '           "SUPABASE_URL": "YOUR_SUPABASE_URL",'
echo '           "SUPABASE_ANON_KEY": "YOUR_ANON_KEY",'
echo '           "SUPABASE_SERVICE_ROLE_KEY": "YOUR_SERVICE_KEY"'
echo '         }'
echo '       }'
echo '     }'
echo '   }'
echo ""
echo "3. Test your setup:"
echo "   npx @supabase/mcp-server-supabase --version"
echo ""
echo "📚 For more information, see the DEPLOYMENT.md file" 