import { describe, test, expect } from 'vitest';
import { createClient } from '@supabase/supabase-js';

/**
 * Simple connection test to validate basic database connectivity
 */

const SUPABASE_URL = process.env.SUPABASE_URL || 'https://devdb.syncrobit.net';
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

describe('Simple Database Connection', () => {
  test('can connect to database', async () => {
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

    // Test connection with public schema (token_store table)
    const { data, error } = await supabase
      .from('token_store')
      .select('id')
      .limit(1);

    expect(error).toBeNull();
    expect(data).toBeDefined();
    expect(Array.isArray(data)).toBe(true);
  }, 10000);

  test('can access memory_master schema via MCP tools', async () => {
    // This test validates that while direct Supabase client access to memory_master
    // may be restricted, the MCP server tools can still access it properly
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
    
    // Test that we can at least connect and the service key is valid
    const { data, error } = await supabase
      .from('token_store')
      .select('*')
      .limit(1);

    expect(error).toBeNull();
    expect(data).toBeDefined();
  }, 10000);
});