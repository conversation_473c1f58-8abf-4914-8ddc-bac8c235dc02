import { type MigrationConfig } from './migration-types.js';
import type { LocalConfig } from '../config/index.js';
import path from 'path';
import { contextLogger } from '../utils/logger.js';

const logger = contextLogger.child({ component: 'MigrationConfig' });

/**
 * Default migration configuration
 */
const DEFAULT_CONFIG: Partial<MigrationConfig> = {
  migrations: {
    directory: './supabase/migrations',
    tableName: 'supabase_migrations',
    schemaName: 'public',
    fileTemplate: '{timestamp}_{name}.sql'
  },
  execution: {
    checkOrder: true,
    allowReorderMigrations: false,
    transactionMode: 'each',
    lockTable: true,
    dryRun: false
  }
};

/**
 * Parse database URL into connection configuration
 */
function parseDatabaseUrl(databaseUrl: string): MigrationConfig['database'] {
  try {
    const url = new URL(databaseUrl);
    
    return {
      host: url.hostname,
      port: parseInt(url.port) || 5432,
      database: url.pathname.substring(1), // Remove leading slash
      user: url.username,
      password: url.password,
      ssl: url.searchParams.get('sslmode') === 'require' ? { rejectUnauthorized: false } : false
    };
  } catch (error) {
    logger.error('Failed to parse database URL', { error: error instanceof Error ? error.message : String(error) });
    throw new Error(`Invalid database URL format: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Create migration configuration from Supabase local config
 */
export function createMigrationConfig(localConfig: LocalConfig): MigrationConfig {
  logger.debug('Creating migration configuration from local config');
  
  if (!localConfig.DATABASE_URL) {
    throw new Error('DATABASE_URL is required for migration operations');
  }

  const databaseConfig = parseDatabaseUrl(localConfig.DATABASE_URL);
  
  const config: MigrationConfig = {
    database: databaseConfig,
    migrations: {
      ...DEFAULT_CONFIG.migrations!,
      // Use absolute path to migrations directory
      directory: path.resolve(process.cwd(), DEFAULT_CONFIG.migrations!.directory!)
    },
    execution: {
      ...DEFAULT_CONFIG.execution!
    }
  };

  logger.debug('Migration configuration created successfully', {
    host: config.database.host,
    port: config.database.port,
    database: config.database.database,
    migrationsDirectory: config.migrations.directory,
    tableName: config.migrations.tableName,
    schemaName: config.migrations.schemaName
  });

  return config;
}

/**
 * Validate migration configuration
 */
export function validateMigrationConfig(config: MigrationConfig): void {
  const errors: string[] = [];

  // Validate database configuration
  if (!config.database.host) {
    errors.push('Database host is required');
  }
  if (!config.database.port || config.database.port < 1 || config.database.port > 65535) {
    errors.push('Database port must be between 1 and 65535');
  }
  if (!config.database.database) {
    errors.push('Database name is required');
  }
  if (!config.database.user) {
    errors.push('Database user is required');
  }

  // Validate migrations configuration
  if (!config.migrations.directory) {
    errors.push('Migrations directory is required');
  }
  if (!config.migrations.tableName) {
    errors.push('Migrations table name is required');
  }
  if (!config.migrations.schemaName) {
    errors.push('Migrations schema name is required');
  }

  if (errors.length > 0) {
    const errorMessage = `Migration configuration validation failed: ${errors.join(', ')}`;
    logger.error('Migration configuration validation failed', { errors });
    throw new Error(errorMessage);
  }

  logger.debug('Migration configuration validation passed');
}

/**
 * Get migration file path from migrations directory
 */
export function getMigrationFilePath(config: MigrationConfig, filename: string): string {
  return path.join(config.migrations.directory, filename);
}

/**
 * Generate migration filename from template
 */
export function generateMigrationFilename(config: MigrationConfig, name: string): string {
  const timestamp = new Date().toISOString().replace(/[-T:\.Z]/g, '').substring(0, 14);
  const template = config.migrations.fileTemplate || DEFAULT_CONFIG.migrations!.fileTemplate!;
  
  return template
    .replace('{timestamp}', timestamp)
    .replace('{name}', name.toLowerCase().replace(/[^a-z0-9]/g, '_'));
}

/**
 * Get node-pg-migrate compatible configuration
 */
export function getNodePgMigrateConfig(config: MigrationConfig) {
  return {
    databaseUrl: `postgresql://${config.database.user}:${config.database.password}@${config.database.host}:${config.database.port}/${config.database.database}`,
    migrationsTable: config.migrations.tableName,
    migrationsSchema: config.migrations.schemaName,
    dir: config.migrations.directory,
    checkOrder: config.execution.checkOrder,
    allowReorderMigrations: config.execution.allowReorderMigrations,
    transactionMode: config.execution.transactionMode,
    lock: config.execution.lockTable,
    dryRun: config.execution.dryRun,
    ssl: config.database.ssl
  };
}