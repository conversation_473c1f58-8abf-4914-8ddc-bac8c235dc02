/**
 * Advanced Caching System for MCP Server Performance Optimization
 * 
 * Provides multi-level caching with TTL, LRU eviction, and cache warming.
 * Supports both in-memory and distributed caching strategies.
 */

import { EventEmitter } from 'events';
import { contextLogger } from '../utils/logger.js';
import type { ConfigManager } from '../config/config-manager.js';

export interface CacheEntry<T = any> {
  key: string;
  value: T;
  ttl: number;
  createdAt: Date;
  lastAccessed: Date;
  accessCount: number;
  size: number;
  tags?: string[];
}

export interface CacheOptions {
  ttl?: number;
  maxSize?: number;
  maxEntries?: number;
  enableCompression?: boolean;
  enableMetrics?: boolean;
  tags?: string[];
}

export interface CacheStats {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  evictions: number;
  totalSize: number;
  entryCount: number;
  hitRate: number;
  averageAccessTime: number;
}

export interface CacheManagerOptions {
  defaultTtl?: number;
  maxMemorySize?: number;
  maxEntries?: number;
  cleanupInterval?: number;
  enableMetrics?: boolean;
  enableCompression?: boolean;
  compressionThreshold?: number;
}

/**
 * High-performance multi-level cache manager
 */
export class CacheManager extends EventEmitter {
  private readonly logger = contextLogger.child({ component: 'CacheManager' });
  private readonly config?: ConfigManager;
  private readonly options: Required<CacheManagerOptions>;
  
  private cache = new Map<string, CacheEntry>();
  private accessOrder = new Map<string, number>(); // For LRU tracking
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    evictions: 0,
    totalSize: 0,
    entryCount: 0,
    hitRate: 0,
    averageAccessTime: 0,
  };
  
  private cleanupTimer?: NodeJS.Timeout;
  private accessCounter = 0;
  private totalAccessTime = 0;

  constructor(options: CacheManagerOptions = {}, config?: ConfigManager) {
    super();
    
    this.config = config;
    this.options = {
      defaultTtl: options.defaultTtl ?? config?.get('CACHE_TTL', 300000) ?? 300000, // 5 minutes
      maxMemorySize: options.maxMemorySize ?? config?.get('CACHE_MAX_MEMORY', 100 * 1024 * 1024) ?? 100 * 1024 * 1024, // 100MB
      maxEntries: options.maxEntries ?? config?.get('CACHE_MAX_ENTRIES', 10000) ?? 10000,
      cleanupInterval: options.cleanupInterval ?? config?.get('CACHE_CLEANUP_INTERVAL', 60000) ?? 60000, // 1 minute
      enableMetrics: options.enableMetrics ?? config?.get('CACHE_ENABLE_METRICS', true) ?? true,
      enableCompression: options.enableCompression ?? config?.get('CACHE_ENABLE_COMPRESSION', false) ?? false,
      compressionThreshold: options.compressionThreshold ?? config?.get('CACHE_COMPRESSION_THRESHOLD', 1024) ?? 1024, // 1KB
    };

    this.startCleanupTimer();
    
    this.logger.info('Cache manager initialized', {
      defaultTtl: this.options.defaultTtl,
      maxMemorySize: this.options.maxMemorySize,
      maxEntries: this.options.maxEntries,
      enableCompression: this.options.enableCompression,
    });
  }

  /**
   * Get value from cache
   */
  async get<T = any>(key: string): Promise<T | undefined> {
    const startTime = Date.now();
    
    try {
      const entry = this.cache.get(key);
      
      if (!entry) {
        this.recordMiss();
        return undefined;
      }

      // Check if entry has expired
      if (this.isExpired(entry)) {
        this.cache.delete(key);
        this.accessOrder.delete(key);
        this.updateStats();
        this.recordMiss();
        return undefined;
      }

      // Update access tracking
      entry.lastAccessed = new Date();
      entry.accessCount++;
      this.accessOrder.set(key, ++this.accessCounter);

      this.recordHit();
      
      this.logger.debug('Cache hit', { key, accessCount: entry.accessCount });
      
      return entry.value as T;
    } finally {
      if (this.options.enableMetrics) {
        this.totalAccessTime += Date.now() - startTime;
      }
    }
  }

  /**
   * Set value in cache
   */
  async set<T = any>(key: string, value: T, options: CacheOptions = {}): Promise<void> {
    try {
      const ttl = options.ttl ?? this.options.defaultTtl;
      const size = this.calculateSize(value);
      
      // Check if we need to make space
      await this.ensureSpace(size);

      const entry: CacheEntry<T> = {
        key,
        value,
        ttl,
        createdAt: new Date(),
        lastAccessed: new Date(),
        accessCount: 0,
        size,
        tags: options.tags,
      };

      // Compress if enabled and value is large enough
      if (this.options.enableCompression && size > this.options.compressionThreshold) {
        // Note: In a real implementation, you'd use a compression library like zlib
        this.logger.debug('Value would be compressed', { key, size });
      }

      this.cache.set(key, entry);
      this.accessOrder.set(key, ++this.accessCounter);
      
      this.stats.sets++;
      this.updateStats();
      
      this.logger.debug('Cache set', { key, size, ttl });
      
      this.emit('set', key, value, options);
    } catch (error) {
      this.logger.error('Failed to set cache entry', error instanceof Error ? error : undefined, { key });
      throw error;
    }
  }

  /**
   * Delete value from cache
   */
  async delete(key: string): Promise<boolean> {
    const existed = this.cache.has(key);
    
    if (existed) {
      this.cache.delete(key);
      this.accessOrder.delete(key);
      this.stats.deletes++;
      this.updateStats();
      
      this.logger.debug('Cache delete', { key });
      this.emit('delete', key);
    }
    
    return existed;
  }

  /**
   * Clear cache by tags
   */
  async clearByTags(tags: string[]): Promise<number> {
    let cleared = 0;
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.tags && entry.tags.some(tag => tags.includes(tag))) {
        this.cache.delete(key);
        this.accessOrder.delete(key);
        cleared++;
      }
    }
    
    if (cleared > 0) {
      this.updateStats();
      this.logger.info('Cache cleared by tags', { tags, cleared });
      this.emit('clearByTags', tags, cleared);
    }
    
    return cleared;
  }

  /**
   * Clear all cache entries
   */
  async clear(): Promise<void> {
    const entryCount = this.cache.size;
    
    this.cache.clear();
    this.accessOrder.clear();
    this.updateStats();
    
    this.logger.info('Cache cleared', { entriesCleared: entryCount });
    this.emit('clear', entryCount);
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * Get cache entries (for debugging)
   */
  getEntries(): CacheEntry[] {
    return Array.from(this.cache.values());
  }

  /**
   * Warm cache with predefined data
   */
  async warmCache(entries: Array<{ key: string; value: any; options?: CacheOptions }>): Promise<void> {
    this.logger.info('Warming cache', { entryCount: entries.length });
    
    for (const { key, value, options } of entries) {
      await this.set(key, value, options);
    }
    
    this.emit('warmed', entries.length);
  }

  /**
   * Shutdown cache manager
   */
  async shutdown(): Promise<void> {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }
    
    await this.clear();
    this.removeAllListeners();
    
    this.logger.info('Cache manager shutdown complete');
  }

  /**
   * Check if cache entry has expired
   */
  private isExpired(entry: CacheEntry): boolean {
    return Date.now() - entry.createdAt.getTime() > entry.ttl;
  }

  /**
   * Calculate approximate size of value in bytes
   */
  private calculateSize(value: any): number {
    try {
      return JSON.stringify(value).length * 2; // Rough estimate (UTF-16)
    } catch {
      return 1024; // Default size for non-serializable values
    }
  }

  /**
   * Ensure there's enough space for new entry
   */
  private async ensureSpace(requiredSize: number): Promise<void> {
    // Check entry count limit
    while (this.cache.size >= this.options.maxEntries) {
      this.evictLRU();
    }

    // Check memory size limit
    while (this.stats.totalSize + requiredSize > this.options.maxMemorySize) {
      this.evictLRU();
    }
  }

  /**
   * Evict least recently used entry
   */
  private evictLRU(): void {
    let oldestKey: string | undefined;
    let oldestAccess = Infinity;

    for (const [key, accessTime] of this.accessOrder.entries()) {
      if (accessTime < oldestAccess) {
        oldestAccess = accessTime;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      this.accessOrder.delete(oldestKey);
      this.stats.evictions++;
      
      this.logger.debug('Cache entry evicted (LRU)', { key: oldestKey });
      this.emit('evict', oldestKey, 'lru');
    }
  }

  /**
   * Start cleanup timer for expired entries
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanupExpired();
    }, this.options.cleanupInterval);
  }

  /**
   * Clean up expired entries
   */
  private cleanupExpired(): void {
    let cleaned = 0;
    
    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        this.cache.delete(key);
        this.accessOrder.delete(key);
        cleaned++;
      }
    }
    
    if (cleaned > 0) {
      this.updateStats();
      this.logger.debug('Expired cache entries cleaned', { cleaned });
      this.emit('cleanup', cleaned);
    }
  }

  /**
   * Update cache statistics
   */
  private updateStats(): void {
    this.stats.entryCount = this.cache.size;
    this.stats.totalSize = Array.from(this.cache.values()).reduce((sum, entry) => sum + entry.size, 0);
    this.stats.hitRate = this.stats.hits / (this.stats.hits + this.stats.misses) || 0;
    this.stats.averageAccessTime = this.totalAccessTime / (this.stats.hits + this.stats.misses) || 0;
  }

  /**
   * Record cache hit
   */
  private recordHit(): void {
    this.stats.hits++;
  }

  /**
   * Record cache miss
   */
  private recordMiss(): void {
    this.stats.misses++;
  }
}
