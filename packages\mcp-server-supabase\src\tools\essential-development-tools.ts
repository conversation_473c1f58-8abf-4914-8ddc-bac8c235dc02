import { z } from 'zod';
import type { SupabasePlatform } from '../platform/types.js';
import { injectableTool } from './util.js';

export type DevelopmentToolsOptions = {
  platform: SupabasePlatform;
  projectId?: string;
};

export function getEssentialDevelopmentTools({
  platform,
  projectId,
}: DevelopmentToolsOptions) {
  const project_id = projectId;

  return {
    get_project_url: injectableTool({
      description: 'Gets the API URL for a project.',
      parameters: z.object({
        project_id: z.string(),
      }),
      inject: { project_id },
      execute: async ({ project_id }) => {
        // For local development, return environment variable or default local URL
        const url = process.env.SUPABASE_URL || 'http://localhost:54321';
        return { url };
      },
    }),

    get_anon_key: injectableTool({
      description: 'Gets the anonymous API key for a project.',
      parameters: z.object({
        project_id: z.string(),
      }),
      inject: { project_id },
      execute: async ({ project_id }) => {
        // For local development, return environment variable or default local anon key
        const anonKey = process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0';
        return { anon_key: anonKey };
      },
    }),

    generate_typescript_types: injectableTool({
      description: 'Generates TypeScript types based on the database schema.',
      parameters: z.object({
        project_id: z.string(),
      }),
      inject: { project_id },
      execute: async ({ project_id }) => {
        try {
          // Generate TypeScript definitions for the current database schema
          const tablesQuery = `
            SELECT 
              t.table_schema,
              t.table_name,
              c.column_name,
              c.data_type,
              c.is_nullable,
              c.column_default,
              CASE 
                WHEN c.data_type = 'USER-DEFINED' THEN c.udt_name
                ELSE c.data_type
              END as pg_type
            FROM information_schema.tables t
            JOIN information_schema.columns c ON t.table_name = c.table_name 
              AND t.table_schema = c.table_schema
            WHERE t.table_schema NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
              AND t.table_type = 'BASE TABLE'
            ORDER BY t.table_schema, t.table_name, c.ordinal_position
          `;

          const result = await platform.executeSql(project_id, {
            query: tablesQuery,
            read_only: true,
          });

          // Group by table
          const tablesBySchema: Record<string, Record<string, any[]>> = {};
          
          for (const row of result) {
            const rowData = row as any;
            const { table_schema, table_name } = rowData;
            if (!tablesBySchema[table_schema]) {
              tablesBySchema[table_schema] = {};
            }
            if (!tablesBySchema[table_schema][table_name]) {
              tablesBySchema[table_schema][table_name] = [];
            }
            tablesBySchema[table_schema][table_name].push(rowData);
          }

          // Generate TypeScript types
          let tsTypes = '// Generated TypeScript types for Supabase database\n\n';

          // Database interface
          tsTypes += 'export interface Database {\n';
          for (const schema of Object.keys(tablesBySchema)) {
            tsTypes += `  ${schema}: {\n`;
            tsTypes += '    Tables: {\n';
            
            for (const [tableName, columns] of Object.entries(tablesBySchema[schema] || {})) {
              tsTypes += `      ${tableName}: {\n`;
              tsTypes += '        Row: {\n';
              
              for (const col of columns) {
                const tsType = mapPostgresToTypeScript(col.pg_type, col.is_nullable === 'YES');
                tsTypes += `          ${col.column_name}: ${tsType};\n`;
              }
              
              tsTypes += '        };\n';
              tsTypes += '        Insert: {\n';
              
              for (const col of columns) {
                const hasDefault = col.column_default !== null;
                const isNullable = col.is_nullable === 'YES';
                const tsType = mapPostgresToTypeScript(col.pg_type, isNullable);
                const optional = hasDefault || isNullable ? '?' : '';
                tsTypes += `          ${col.column_name}${optional}: ${tsType};\n`;
              }
              
              tsTypes += '        };\n';
              tsTypes += '        Update: {\n';
              
              for (const col of columns) {
                const tsType = mapPostgresToTypeScript(col.pg_type, true);
                tsTypes += `          ${col.column_name}?: ${tsType};\n`;
              }
              
              tsTypes += '        };\n';
              tsTypes += '      };\n';
            }
            
            tsTypes += '    };\n';
            tsTypes += '  };\n';
          }
          tsTypes += '}\n';

          return {
            typescript_types: tsTypes,
            generated_at: new Date().toISOString(),
            tables_count: Object.values(tablesBySchema).reduce((acc, schema) => acc + Object.keys(schema).length, 0),
          };
        } catch (error) {
          throw new Error(`Failed to generate TypeScript types: ${error instanceof Error ? error.message : String(error)}`);
        }
      },
    }),
  };
}

/**
 * Map PostgreSQL types to TypeScript types
 */
function mapPostgresToTypeScript(pgType: string, isNullable: boolean): string {
  let tsType: string;

  switch (pgType) {
    case 'integer':
    case 'bigint':
    case 'smallint':
    case 'decimal':
    case 'numeric':
    case 'real':
    case 'double precision':
    case 'serial':
    case 'bigserial':
      tsType = 'number';
      break;
    case 'boolean':
      tsType = 'boolean';
      break;
    case 'uuid':
    case 'text':
    case 'character varying':
    case 'varchar':
    case 'character':
    case 'char':
      tsType = 'string';
      break;
    case 'timestamp':
    case 'timestamp with time zone':
    case 'timestamp without time zone':
    case 'date':
    case 'time':
    case 'time with time zone':
    case 'time without time zone':
      tsType = 'string';
      break;
    case 'json':
    case 'jsonb':
      tsType = 'any';
      break;
    case 'bytea':
      tsType = 'string';
      break;
    case 'array':
    case 'ARRAY':
      tsType = 'any[]';
      break;
    default:
      if (pgType.includes('[]')) {
        tsType = 'any[]';
      } else {
        tsType = 'any';
      }
  }

  return isNullable ? `${tsType} | null` : tsType;
} 